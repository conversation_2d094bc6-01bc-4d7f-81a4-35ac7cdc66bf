import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaChevronDown } from "react-icons/fa";

const MernFullStackFAQS = ({ onBackToCourse }) => {
  const [openFaq, setOpenFaq] = useState(null);
  
  const faqs = [
    {
      question: "What is MERN Stack Development?",
      answer: "MERN Stack Development involves using MongoDB (database), Express.js (backend framework), React (frontend library), and Node.js (runtime environment) to build full-stack web applications. This technology stack allows developers to use JavaScript throughout the entire development process, from frontend to backend and database operations."
    },
    {
      question: "Do I need prior experience with JavaScript to learn MERN?",
      answer: "While basic JavaScript knowledge is helpful, our course starts with JavaScript fundamentals and gradually progresses to advanced concepts. We cover ES6+ features, asynchronous programming, and modern JavaScript patterns that are essential for MERN development. Complete beginners can follow along with our structured learning path."
    },
    {
      question: "How long does it take to master the MERN stack?",
      answer: "The timeline varies based on your background and dedication. With consistent practice (10-15 hours per week), most students can build their first full-stack MERN application within 3-4 months. Mastery comes with building multiple projects and understanding advanced concepts, which typically takes 6-12 months of continuous learning."
    },
    {
      question: "What projects will I build during the course?",
      answer: "You'll build several real-world projects including a social media platform, e-commerce application, task management system, real-time chat application, and a blog platform. Each project focuses on different aspects of MERN development and gradually increases in complexity to build your skills progressively."
    },
    {
      question: "Is MERN stack good for career opportunities?",
      answer: "Absolutely! MERN stack developers are in high demand due to the popularity of React and Node.js. Companies value developers who can work across the full stack using a single language (JavaScript). Career opportunities include Full Stack Developer, React Developer, Node.js Developer, and JavaScript Engineer positions."
    },
    {
      question: "How does MERN compare to other tech stacks?",
      answer: "MERN offers several advantages: single language (JavaScript) across the stack, large community support, extensive npm ecosystem, and excellent performance. Compared to LAMP or Django, MERN provides better real-time capabilities and modern development practices. It's particularly strong for building SPAs and real-time applications."
    },
    {
      question: "What tools and software do I need?",
      answer: "You'll need Node.js, a code editor (VS Code recommended), MongoDB (or MongoDB Atlas for cloud), Git for version control, and a modern web browser. We'll guide you through setting up the complete development environment including useful extensions and tools that enhance productivity."
    },
    {
      question: "Do you provide job placement assistance?",
      answer: "Yes! Our course includes career support services such as resume review, portfolio development guidance, mock interviews, and job placement assistance. We have partnerships with tech companies and provide networking opportunities to help you land your first MERN stack developer role."
    },
    {
      question: "Can I build mobile apps with MERN knowledge?",
      answer: "Yes! Your React knowledge directly translates to React Native for mobile app development. Additionally, you can build Progressive Web Apps (PWAs) using your MERN skills. Many students leverage their MERN foundation to expand into mobile development or create cross-platform applications."
    },
    {
      question: "What's included in the premium version?",
      answer: "Premium includes access to all live classes, 1-on-1 mentoring sessions, premium project templates, advanced deployment tutorials, career counseling, job placement assistance, lifetime access to course updates, and priority support. You also get access to our exclusive community and networking events."
    }
  ];

  const toggleFaq = (index) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-900/70 to-blue-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">MERN Stack FAQs</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="mb-6">
            <h3 className="text-2xl font-bold text-white mb-4">
              Frequently Asked Questions
            </h3>
            <p className="text-gray-300">
              Get answers to common questions about MERN stack development and our course.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-900/50 border border-gray-700/30 rounded-lg overflow-hidden"
              >
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-800/50 transition-colors"
                >
                  <h4 className="text-lg font-medium text-white pr-4">
                    {faq.question}
                  </h4>
                  <motion.div
                    animate={{ rotate: openFaq === index ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <FaChevronDown className="text-gray-400 flex-shrink-0" />
                  </motion.div>
                </button>
                
                <AnimatePresence>
                  {openFaq === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-gray-700/30"
                    >
                      <div className="px-6 py-4">
                        <p className="text-gray-300 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>

          {/* Additional Resources */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mt-8 bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/20 rounded-lg p-6"
          >
            <h4 className="text-lg font-semibold text-white mb-4">Still have questions?</h4>
            <p className="text-gray-300 mb-4">
              Can't find the answer you're looking for? Our support team is here to help you succeed in your MERN stack journey.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg font-medium hover:from-green-600 hover:to-blue-600 transition-all duration-300">
                Contact Support
              </button>
              <button className="px-6 py-3 border border-gray-500 text-gray-300 rounded-lg font-medium hover:border-gray-400 hover:text-white transition-all duration-300">
                Join Community
              </button>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default MernFullStackFAQS;
