import { Link, useNavigate } from "react-router-dom";
import { useGoogleLogin } from "@react-oauth/google";
import { googleAuth } from "./api";
import { useState } from "react";
import toast from "react-hot-toast";
import { Spinner } from "../../components/ui";
import axiosInstance from "../../utils/axiosInstance";


const Login = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);

  const responseGoogle = async (authResult) => {
    try {
      if (authResult["code"]) {
        const result = await googleAuth(authResult["code"]);
        const { name, image } = result.data.user;
        const token = result.data.token;

        if (token) {
          localStorage.setItem("user", JSON.stringify({ name, image }));
          localStorage.setItem("token", token);
          navigate("/");
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  const googleLogin = useGoogleLogin({
    onSuccess: responseGoogle,
    onError: responseGoogle,
    flow: "auth-code",
  });

  const validateFields = () => {
    let newErrors = {};
    if (!email) newErrors.email = "Email is required";
    if (!password) newErrors.password = "Password is required";
    setErrors(newErrors);
    setTimeout(() => {
      setErrors({});
    }, 3000);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    if (!validateFields()) return;

    setLoading(true);
    try {
      const { data } = await axiosInstance.post(
        "/auth/login",
        { email, password },
        { withCredentials: true }
      );
      if (data.success) {
        if (data.requiresTwoFA) {
          toast.success(
            "OTP sent to your email. Please verify to complete login."
          );
          navigate("/two-fa-otp");
        } else {
          toast.success(data.message);
          navigate("/two-fa");
        }
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error("Login Error:", error);
      toast.error("Something went wrong!");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#181c25] to-[#12171d] flex items-center justify-center px-4">
      <div className="w-full max-w-6xl bg-black/40 border border-gray-700 rounded-2xl shadow-xl p-6 md:p-12 flex flex-col md:flex-row items-center justify-between">
        {/* Left Side */}
        <div className="md:w-1/2 w-full flex flex-col justify-center items-center md:items-start px-4 md:px-8 mb-8 md:mb-0 text-white">
          <div className="flex flex-col items-center md:items-start w-full text-center md:text-left space-y-5 max-w-md ">
            <div>
              <h1 className="text-3xl md:text-4xl font-semibold text-white mb-1">
                Welcome to
              </h1>
              <h2 className="text-4xl md:text-6xl font-extrabold leading-relaxed bg-gradient-to-r from-blue-400 via-purple-500 to-pink-600 bg-clip-text text-transparent">
                Upcoding
              </h2>
            </div>

            {/* Short Tagline */}
            <p className="text-gray-300 text-sm md:text-base">
              Level up your skills with curated content, live practice, and
              community support.
            </p>

            {/* Features List */}
            <ul className="text-gray-400 text-sm md:text-base list-disc list-inside space-y-1">
              <li>📈 Track your progress in real-time</li>

              <li>🌐 Learn with a global community</li>
            </ul>
          </div>
        </div>

        {/* Right Side: Login Form */}
        <form
          className="md:w-1/2 w-full bg-[#0f141a] rounded-xl shadow-lg px-6 py-8 md:px-10 md:py-12 flex flex-col"
          onSubmit={handleLogin}
        >
          <h2 className="text-2xl font-extrabold text-white text-center mb-2">
            Login Page
          </h2>
          <p className="text-gray-400 mb-6 text-center text-sm">
            Welcome back! Continue your learning journey below
          </p>

          <input
            type="text"
            placeholder="Employee ID or Email"
            className="mb-3 px-4 py-3 rounded-lg border border-gray-700 bg-[#181e24] text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          {errors.email && (
            <p className="text-red-400 text-xs mb-2">{errors.email}</p>
          )}

          <div className="relative mb-3">
            <input
              type={showPassword ? "text" : "password"}
              placeholder="Password"
              className="w-full px-4 py-3 rounded-lg border border-gray-700 bg-[#181e24] text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-blue-500"
              onClick={() => setShowPassword((v) => !v)}
              tabIndex={-1}
            >
              {showPassword ? <EyeOffIcon /> : <EyeIcon />}
            </button>
          </div>
          {errors.password && (
            <p className="text-red-400 text-xs mb-2">{errors.password}</p>
          )}

          <button
            type="submit"
            className="w-full py-3 rounded-lg bg-blue-600 text-white font-semibold text-lg mt-2 mb-4 hover:bg-blue-700 transition"
            disabled={loading}
          >
            {loading ? <Spinner /> : "Sign In"}
          </button>

          <div className="flex justify-between items-center mb-4">
            <label className="flex items-center text-gray-400 text-sm">
              <input type="checkbox" className="mr-2" />
              Stay Signed In
            </label>
            <Link
              to="/forgot-password"
              className="text-blue-400 hover:underline text-sm"
            >
              Forgot password?
            </Link>
          </div>

          <button
            type="button"
            className="w-full py-2 rounded-lg border border-gray-700 flex items-center justify-center bg-[#181e24] text-white font-semibold mb-4 hover:bg-gray-800 transition"
            onClick={googleLogin}
          >
            <img
              src="/images/google.jpg"
              alt="Google"
              className="w-5 h-5 mr-3"
            />
            Sign In With Google
          </button>

          <p className="text-center text-gray-400 mt-1 text-sm">
            {"Don't"} have an account?{" "}
            <Link to="/SignUp" className="text-blue-400 hover:underline">
              Sign Up
            </Link>
          </p>
        </form>
      </div>
    </div>
  );
};

const EyeIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="w-5 h-5"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
    />
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M2.458 12C3.732 7.943 7.523 5 12 5c1.75 0 3.409.419 4.829 1.171M21.542 12c-1.274 4.057-5.064 7-9.542 7-1.75 0-3.409-.419-4.829-1.171"
    />
  </svg>
);

const EyeOffIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="w-5 h-5"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M13.875 18.825A10.05 10.05 0 0 1 12 19c-5.523 0-10-4.477-10-10a9.964 9.964 0 0 1 .26-2.283m18.619-.698A9.917 9.917 0 0 1 22 9c0 5.523-4.477 10-10 10a9.935 9.935 0 0 1-2.325-.275"
    />
  </svg>
);

export default Login;
