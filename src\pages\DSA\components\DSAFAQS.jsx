import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaChevronDown } from "react-icons/fa";

const DSAFAQS = ({ onBackToCourse }) => {
  const [openFaq, setOpenFaq] = useState(null);
  
  const faqs = [
    {
      question: "What is the best way to prepare for DSA interviews?",
      answer: "Start with understanding the core concepts of each data structure and algorithm. Practice regularly with problems of increasing difficulty. Focus on problem-solving patterns rather than memorizing solutions. Mock interviews and timed practice sessions are also very helpful."
    },
    {
      question: "How long does it take to master DSA?",
      answer: "The time varies based on your background and dedication. For beginners, it typically takes 3-6 months of consistent practice to become comfortable with most DSA concepts. Mastery is an ongoing process that continues to develop with experience."
    },
    {
      question: "Which data structures are most important for interviews?",
      answer: "Arrays, linked lists, stacks, queues, trees (especially binary search trees), graphs, and hash tables are fundamental. Understanding how and when to use each one is crucial for technical interviews."
    },
    {
      question: "How do I improve my problem-solving speed?",
      answer: "Regular practice with timed exercises helps improve speed. Start by understanding the problem thoroughly before coding. Learn to recognize common patterns in problems. Practice implementing standard algorithms until they become second nature."
    },
    {
      question: "What's the difference between time and space complexity?",
      answer: "Time complexity measures how the runtime of an algorithm grows as the input size increases. Space complexity measures how much additional memory an algorithm needs based on input size. Both are typically expressed using Big O notation."
    },
    {
      question: "Should I focus on theory or practice?",
      answer: "Both are important. Understanding theoretical concepts provides the foundation, while practice helps reinforce learning and develop problem-solving skills. A good approach is to learn a concept and immediately apply it through practice problems."
    }
  ];

  const toggleFaq = (index) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/70 to-indigo-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">Frequently Asked Questions</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Common DSA Questions</h3>
          <p className="text-gray-300">
            Find answers to frequently asked questions about data structures, algorithms, and interview preparation.
          </p>
        </div>
        
        {/* FAQs */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              className="border border-gray-700/30 rounded-lg overflow-hidden"
            >
              <button
                onClick={() => toggleFaq(index)}
                className="w-full flex items-center justify-between p-4 text-left bg-gray-800/30 hover:bg-gray-700/40 transition-colors"
              >
                <h4 className="font-medium text-white">{faq.question}</h4>
                <motion.div
                  animate={{ rotate: openFaq === index ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <FaChevronDown className="text-gray-500" />
                </motion.div>
              </button>
              
              <AnimatePresence>
                {openFaq === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="p-4 bg-gray-800/20">
                      <p className="text-gray-300">{faq.answer}</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DSAFAQS;