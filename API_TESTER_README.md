# API Tester Feature

## Overview
A fully functional and reusable API testing tool integrated into the LeetCode-style code editor. This tool allows users to test REST APIs directly from the browser using the Fetch API.

## Features

### Core Functionality
- **HTTP Methods**: Support for GET, POST, PUT, DELETE, and PATCH requests
- **URL Input**: Enter any API endpoint URL
- **Headers Management**: JSON-formatted headers editor with syntax highlighting
- **Request Body**: JSON request body editor for POST/PUT/PATCH methods
- **Response Display**: Shows status code, headers, and formatted response body
- **Error Handling**: Comprehensive error handling with user-friendly messages

### User Experience
- **Theme Support**: Matches the main app's dark/light theme
- **Keyboard Shortcuts**: 
  - `Ctrl+Enter` / `Cmd+Enter`: Send request
  - `Ctrl+K` / `Cmd+K`: Clear response
  - `Ctrl+R` / `Cmd+R`: Reset form
- **Quick Examples**: Pre-configured example requests for testing
- **Request History**: Save and load recent requests (stored in localStorage)
- **Loading States**: Visual feedback during request processing
- **Responsive Design**: Works on desktop and mobile devices

### Advanced Features
- **Request Timing**: Shows request duration in milliseconds
- **Status Color Coding**: Visual indicators for different HTTP status codes
- **JSON Formatting**: Automatic JSON formatting for responses
- **Content Type Detection**: Handles both JSON and text responses
- **CORS Support**: Works with CORS-enabled APIs

## Usage

### Navigation
1. From any code editor page, click the "API Tester" button in the header
2. Or navigate directly to `/api-tester`

### Making Requests
1. Select HTTP method from dropdown
2. Enter the API endpoint URL
3. Add headers in JSON format (optional)
4. Add request body for POST/PUT/PATCH methods (optional)
5. Click "Send [METHOD]" or use Ctrl+Enter

### Quick Examples
Use the provided example buttons to quickly test:
- **GET Post**: Fetches a single post from JSONPlaceholder
- **POST Example**: Creates a new post with sample data
- **JSON Response**: Tests JSON response handling

### Request History
- Click "Save" to save current request configuration
- Click "History" to view and load saved requests
- Up to 10 recent requests are stored locally

## Technical Implementation

### Files Structure
```
src/
├── components/features/ApiTester.jsx     # Main component
├── hooks/useApiTester.js                 # Custom hook for state management
├── App.jsx                               # Route configuration
└── components/features/index.js          # Export configuration
```

### Key Components
- **ApiTester Component**: Main UI component with request/response panels
- **useApiTester Hook**: Manages state, API calls, and localStorage operations
- **Theme Integration**: Uses existing theme system from the main app

### Dependencies
- React (hooks, state management)
- React Router (navigation)
- Framer Motion (animations)
- Fetch API (HTTP requests)
- localStorage (request history)

## Integration Points

### Navigation Integration
- Added "API Tester" button to CodeEditor header
- New route `/api-tester` in App.jsx
- Exported from features index

### Theme Integration
- Uses same theme state management pattern as other components
- Consistent color schemes and styling
- Theme toggle button in header

### Reusability
- Modular component design
- Custom hook separates logic from UI
- Can be easily integrated into other parts of the application

## Testing Examples

### Test APIs
1. **JSONPlaceholder** (https://jsonplaceholder.typicode.com/)
   - GET /posts - List all posts
   - GET /posts/1 - Get specific post
   - POST /posts - Create new post

2. **HTTPBin** (https://httpbin.org/)
   - GET /json - Returns JSON data
   - POST /post - Echo POST data
   - GET /status/200 - Returns specific status

3. **Public APIs**
   - Any CORS-enabled public API
   - REST endpoints with JSON responses

## Future Enhancements

### Potential Features
- Request/Response history with search
- Environment variables support
- Request collections/folders
- Import/Export functionality
- Authentication helpers (Bearer token, API key)
- Response time graphs
- Request templates
- Bulk request testing
- WebSocket support

### Performance Optimizations
- Request caching
- Response streaming for large payloads
- Request cancellation
- Retry mechanisms

## Browser Compatibility
- Modern browsers with Fetch API support
- Chrome 42+, Firefox 39+, Safari 10.1+, Edge 14+
- Mobile browsers supported

## Security Considerations
- CORS restrictions apply
- No server-side proxy (frontend-only)
- localStorage used for history (client-side only)
- No sensitive data persistence

## Troubleshooting

### Common Issues
1. **CORS Errors**: API must support CORS for browser requests
2. **Network Errors**: Check internet connection and URL validity
3. **JSON Parse Errors**: Ensure valid JSON format in headers/body
4. **Timeout Issues**: Large responses may take time to load

### Error Messages
- Clear, user-friendly error descriptions
- Specific guidance for common issues
- Request timing information for debugging
