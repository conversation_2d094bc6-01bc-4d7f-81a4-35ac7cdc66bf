import { useState } from "react";
import { sixtyQuestions } from "../../../utils/SixtyQuestion";

const useInterviewData = () => {
  const [interviewData] = useState(sixtyQuestions);
  
  // Sample interview checklist similar to DSA page
  const interviewChecklist = {
    title: "Interview Preparation Checklist",
    items: [
      { text: "Understand the question thoroughly before answering", checked: true },
      { text: "Use clear and concise language to explain complex concepts", checked: true },
      { text: "Practice common behavioral questions", checked: true },
      { text: "Research the company before your interview", checked: true },
      { text: "Prepare questions to ask your interviewer", checked: true },
      { text: "Review your resume and be ready to discuss all aspects of it", checked: false },
      { text: "Practice your answers out loud to gain confidence", checked: false },
      { text: "Use the STAR method for behavioral questions", checked: false },
    ]
  };

  return { interviewData, interviewChecklist };
};

export default useInterviewData;
