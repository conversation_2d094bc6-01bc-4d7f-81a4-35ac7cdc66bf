import React, { useState, useRef, useCallback, useEffect } from "react";

const ResizablePanel = ({ 
  children, 
  initialWidth = 50, 
  minWidth = 20, 
  maxWidth = 80, 
  onResize,
  className = ""
}) => {
  const [width, setWidth] = useState(initialWidth);
  const [isResizing, setIsResizing] = useState(false);
  const panelRef = useRef(null);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);

  const handleMouseDown = useCallback((e) => {
    e.preventDefault();
    setIsResizing(true);
    startXRef.current = e.clientX;
    startWidthRef.current = width;

    // Prevent text selection during resize
    document.body.style.userSelect = 'none';
    document.body.style.cursor = 'col-resize';
  }, [width]);

  // Touch support for mobile
  const handleTouchStart = useCallback((e) => {
    e.preventDefault();
    setIsResizing(true);
    startXRef.current = e.touches[0].clientX;
    startWidthRef.current = width;

    document.body.style.userSelect = 'none';
  }, [width]);

  const handleTouchMove = useCallback((e) => {
    if (!isResizing) return;
    e.preventDefault();

    const deltaX = e.touches[0].clientX - startXRef.current;
    const containerWidth = panelRef.current?.parentElement?.offsetWidth || window.innerWidth;
    const deltaPercent = (deltaX / containerWidth) * 100;
    const newWidth = Math.max(minWidth, Math.min(maxWidth, startWidthRef.current + deltaPercent));

    setWidth(newWidth);
    onResize?.(newWidth);
  }, [isResizing, minWidth, maxWidth, onResize]);

  const handleTouchEnd = useCallback(() => {
    setIsResizing(false);
    document.body.style.userSelect = '';
  }, []);

  const handleMouseMove = useCallback((e) => {
    if (!isResizing) return;

    const deltaX = e.clientX - startXRef.current;
    const containerWidth = panelRef.current?.parentElement?.offsetWidth || window.innerWidth;
    const deltaPercent = (deltaX / containerWidth) * 100;
    const newWidth = Math.max(minWidth, Math.min(maxWidth, startWidthRef.current + deltaPercent));
    
    setWidth(newWidth);
    onResize?.(newWidth);
  }, [isResizing, minWidth, maxWidth, onResize]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    document.body.style.userSelect = '';
    document.body.style.cursor = '';
  }, []);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  return (
    <div 
      ref={panelRef}
      className={`relative ${className}`}
      style={{ width: `${width}%` }}
    >
      {children}
      
      {/* Resize Handle */}
      <div
        className={`absolute top-0 right-0 w-2 h-full cursor-col-resize bg-transparent hover:bg-blue-500/50 transition-colors touch-none ${
          isResizing ? 'bg-blue-500' : ''
        }`}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        style={{ zIndex: 10 }}
      >
        {/* Visual indicator */}
        <div className="absolute top-1/2 right-0 transform -translate-y-1/2 w-1 h-8 bg-gray-400 dark:bg-gray-600 rounded-l opacity-0 hover:opacity-100 transition-opacity" />

        {/* Mobile-friendly resize handle */}
        <div className="absolute top-1/2 right-0 transform -translate-y-1/2 w-4 h-12 -mr-1 flex items-center justify-center md:hidden">
          <div className="w-1 h-6 bg-gray-400 dark:bg-gray-600 rounded"></div>
        </div>
      </div>
    </div>
  );
};

export default ResizablePanel;
