import React, { useState } from "react";
import {
  SystemDesignHero,
  SystemDesignCards,
  SystemDesignIntroduction,
  LabEnvironment,
  InterviewChecklist,
  ProjectsComponent
} from "./components";
import useSystemDesignData from "./hooks/useSystemDesignData";
import useSidebarState from "../../hooks/useSidebarState";
import { CourseLayout, Footer } from "../../components/layout";

const SystemDesign = () => {
  const { interviewChecklist } = useSystemDesignData();
  const { isSidebarOpen, toggleSidebar } = useSidebarState(true); // Default to open
  const [activeSection, setActiveSection] = useState(null);

  const handleScrollToTop = (e) => {
    e.preventDefault();
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const handleCardSelect = (cardId) => {
    setActiveSection(cardId);
    // Scroll to content area
    setTimeout(() => {
      const contentArea = document.getElementById('content-area');
      if (contentArea) {
        contentArea.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };

  // CSS Animation Styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    @keyframes slideUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .page-animate {
      animation: fadeIn 0.5s ease forwards;
    }
    .content-animate {
      animation: slideUp 0.5s ease forwards;
    }
    .float-button {
      transition: all 0.3s ease;
    }
    .float-button:hover {
      transform: scale(1.1);
    }
    .float-button:active {
      transform: scale(0.9);
    }
    .float-button-icon {
      display: block;
      animation: bounceUpDown 2s infinite;
    }
    @keyframes bounceUpDown {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5px); }
    }
  `;

  const renderContent = () => {
    switch (activeSection) {
      case "Introduction":
        return <SystemDesignIntroduction />;
      case "LabEnvironment":
        return <LabEnvironment />;
      case "Projects":
        return <ProjectsComponent />;
      case "InterviewChecklist":
        return <InterviewChecklist interviewChecklist={interviewChecklist} />;
      default:
        return null;
    }
  };

  return (
    <CourseLayout 
      isSidebarOpen={isSidebarOpen} 
      toggleSidebar={toggleSidebar}
      title="System Design"
    >
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-[#1a3c50] to-[#010509] text-white min-h-screen flex items-center px-6">
        <SystemDesignHero />
      </section>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 transition-all duration-300">
        {/* Section Header */}
        <div className="text-center mb-8 mt-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-2">System Design Learning Path</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Select a resource category to start learning system design through theory, interactive coding, projects, or interview preparation
          </p>
        </div>

        {/* Course Cards */}
        <SystemDesignCards onCardSelect={handleCardSelect} />
        
        {/* Selected Content */}
        {activeSection && (
          <div id="content-area" className="mt-12 content-animate">
            {renderContent()}
          </div>
        )}
      </div>

      {/* Footer */}
      <Footer />

      {/* Floating Back to Top Button */}
      <button
        className="fixed bottom-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-full shadow-2xl hover:shadow-3xl float-button z-50"
        onClick={handleScrollToTop}
        style={{ 
          backdropFilter: "blur(10px)",
          right: isSidebarOpen ? "calc(8px + 280px)" : "8px"
        }}
      >
        <span className="text-2xl float-button-icon">↑</span>
      </button>
    </CourseLayout>
  );
};

export default SystemDesign;
