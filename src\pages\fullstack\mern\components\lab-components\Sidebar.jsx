import React from "react";
import { FaPlus, FaTimes, FaFile } from "react-icons/fa";

const Sidebar = ({
  files,
  activeFileId,
  onFileSelect,
  onCreateNewFile,
  onRenameFile,
  onDeleteFile,
  getFileIcon
}) => {
  return (
    <div className="w-64 bg-gray-800 border-r border-gray-700 flex flex-col">
      {/* File Explorer */}
      <div className="flex-1 p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-gray-300">File Explorer</h3>
          <button
            onClick={onCreateNewFile}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
            title="New File"
          >
            <FaPlus className="text-xs text-gray-400" />
          </button>
        </div>
        
        <div className="space-y-1">
          {Object.values(files).map((file) => (
            <div
              key={file.id}
              className={`group flex items-center justify-between p-2 rounded cursor-pointer transition-colors ${
                activeFileId === file.id 
                  ? 'bg-blue-600/30 text-blue-300' 
                  : 'hover:bg-gray-700 text-gray-300'
              }`}
              onClick={() => onFileSelect(file.id)}
            >
              <div className="flex items-center space-x-2">
                <span className="text-xs">{getFileIcon(file.name)}</span>
                <span className="text-sm">{file.name}</span>
                {!file.saved && <span className="w-2 h-2 bg-orange-400 rounded-full"></span>}
              </div>
              
              <div className="flex items-center space-x-1">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    const newName = prompt('Enter new name:', file.name);
                    if (newName && newName !== file.name) {
                      onRenameFile(file.name, newName);
                    }
                  }}
                  className="p-1 hover:bg-gray-600 rounded transition-colors opacity-0 group-hover:opacity-100"
                  title="Rename"
                >
                  <FaFile className="text-xs text-gray-400" />
                </button>
                
                {Object.keys(files).length > 1 && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      if (confirm(`Delete ${file.name}?`)) {
                        onDeleteFile(file.id);
                      }
                    }}
                    className="p-1 hover:bg-gray-600 rounded transition-colors opacity-0 group-hover:opacity-100"
                    title="Delete"
                  >
                    <FaTimes className="text-xs text-red-400" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
