export const languageTemplates = {
  javascript: {
    'two-sum': `/**
 * @param {number[]} nums
 * @param {number} target
 * @return {number[]}
 */
var twoSum = function(nums, target) {
    // Write your solution here
    
};`,
    'reverse-integer': `/**
 * @param {number} x
 * @return {number}
 */
var reverse = function(x) {
    // Write your solution here
    
};`,
    'palindrome-number': `/**
 * @param {number} x
 * @return {boolean}
 */
var isPalindrome = function(x) {
    // Write your solution here
    
};`,
    'valid-parentheses': `/**
 * @param {string} s
 * @return {boolean}
 */
var isValid = function(s) {
    // Write your solution here
    
};`,
    'merge-sorted-arrays': `/**
 * @param {number[]} nums1
 * @param {number} m
 * @param {number[]} nums2
 * @param {number} n
 * @return {void} Do not return anything, modify nums1 in-place instead.
 */
var merge = function(nums1, m, nums2, n) {
    // Write your solution here
    
};`,
    default: `// Write your JavaScript solution here
function solution() {
    // Your code here
}`
  },
  python: {
    'two-sum': `class Solution:
    def twoSum(self, nums: List[int], target: int) -> List[int]:
        # Write your solution here
        pass`,
    'reverse-integer': `class Solution:
    def reverse(self, x: int) -> int:
        # Write your solution here
        pass`,
    'palindrome-number': `class Solution:
    def isPalindrome(self, x: int) -> bool:
        # Write your solution here
        pass`,
    'valid-parentheses': `class Solution:
    def isValid(self, s: str) -> bool:
        # Write your solution here
        pass`,
    'merge-sorted-arrays': `class Solution:
    def merge(self, nums1: List[int], m: int, nums2: List[int], n: int) -> None:
        """
        Do not return anything, modify nums1 in-place instead.
        """
        # Write your solution here
        pass`,
    default: `# Write your Python solution here
class Solution:
    def solution(self):
        # Your code here
        pass`
  },
  java: {
    'two-sum': `class Solution {
    public int[] twoSum(int[] nums, int target) {
        // Write your solution here
        
    }
}`,
    'reverse-integer': `class Solution {
    public int reverse(int x) {
        // Write your solution here
        
    }
}`,
    'palindrome-number': `class Solution {
    public boolean isPalindrome(int x) {
        // Write your solution here
        
    }
}`,
    'valid-parentheses': `class Solution {
    public boolean isValid(String s) {
        // Write your solution here
        
    }
}`,
    'merge-sorted-arrays': `class Solution {
    public void merge(int[] nums1, int m, int[] nums2, int n) {
        // Write your solution here
        
    }
}`,
    default: `// Write your Java solution here
class Solution {
    public void solution() {
        // Your code here
    }
}`
  },
  cpp: {
    'two-sum': `class Solution {
public:
    vector<int> twoSum(vector<int>& nums, int target) {
        // Write your solution here
        
    }
};`,
    'reverse-integer': `class Solution {
public:
    int reverse(int x) {
        // Write your solution here
        
    }
};`,
    'palindrome-number': `class Solution {
public:
    bool isPalindrome(int x) {
        // Write your solution here
        
    }
};`,
    'valid-parentheses': `class Solution {
public:
    bool isValid(string s) {
        // Write your solution here
        
    }
};`,
    'merge-sorted-arrays': `class Solution {
public:
    void merge(vector<int>& nums1, int m, vector<int>& nums2, int n) {
        // Write your solution here
        
    }
};`,
    default: `// Write your C++ solution here
class Solution {
public:
    void solution() {
        // Your code here
    }
};`
  }
};
