import { motion } from "framer-motion";
import { 
  FaFileContract, 
  FaShieldAlt, 
  FaUserCheck, 
  FaGavel,
  FaExclamationTriangle,
  FaHandshake,
  FaLock,
  FaGlobe,
  FaEnvelope,
  FaCalendarAlt,
  FaInfoCircle,
  FaCheckCircle
} from "react-icons/fa";

const TermsAndConditions = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
      },
    },
  };

  const sections = [
    {
      id: "acceptance",
      title: "1. Acceptance of Terms",
      icon: <FaCheckCircle />,
      color: "from-green-500 to-emerald-500",
      content: [
        "By accessing and using our platform, you accept and agree to be bound by the terms and provision of this agreement.",
        "If you do not agree to abide by the above, please do not use this service.",
        "These terms apply to all visitors, users, and others who access or use the service."
      ]
    },
    {
      id: "services",
      title: "2. Description of Services",
      icon: <FaGlobe />,
      color: "from-blue-500 to-cyan-500",
      content: [
        "We provide online educational services including courses, tutorials, coding challenges, and community features.",
        "Our platform offers both free and premium content to help users learn programming and technology skills.",
        "Services may be modified, updated, or discontinued at any time without prior notice."
      ]
    },
    {
      id: "accounts",
      title: "3. User Accounts",
      icon: <FaUserCheck />,
      color: "from-purple-500 to-pink-500",
      content: [
        "You are responsible for maintaining the confidentiality of your account and password.",
        "You agree to accept responsibility for all activities that occur under your account.",
        "You must notify us immediately of any unauthorized use of your account.",
        "We reserve the right to terminate accounts that violate these terms."
      ]
    },
    {
      id: "conduct",
      title: "4. User Conduct",
      icon: <FaShieldAlt />,
      color: "from-orange-500 to-red-500",
      content: [
        "You agree not to use the service for any unlawful purpose or to solicit others to perform unlawful acts.",
        "You agree not to post or transmit any content that is harmful, threatening, abusive, or hateful.",
        "Respect other users and maintain a professional and constructive environment.",
        "Do not attempt to gain unauthorized access to any portion of the service."
      ]
    },
    {
      id: "content",
      title: "5. Content and Intellectual Property",
      icon: <FaLock />,
      color: "from-indigo-500 to-purple-500",
      content: [
        "All content on this platform, including text, graphics, logos, and software, is our property or licensed to us.",
        "You may not reproduce, distribute, or create derivative works from our content without permission.",
        "User-generated content remains the property of the user, but you grant us a license to use it.",
        "We respect intellectual property rights and expect users to do the same."
      ]
    },
    {
      id: "privacy",
      title: "6. Privacy Policy",
      icon: <FaLock />,
      color: "from-teal-500 to-green-500",
      content: [
        "We collect and use personal information as described in our Privacy Policy.",
        "We implement appropriate security measures to protect your personal information.",
        "We do not sell or rent your personal information to third parties.",
        "You have the right to access, update, or delete your personal information."
      ]
    },
    {
      id: "payments",
      title: "7. Payments and Refunds",
      icon: <FaHandshake />,
      color: "from-yellow-500 to-orange-500",
      content: [
        "Premium services require payment as specified on our pricing page.",
        "All payments are processed securely through third-party payment processors.",
        "Refunds are available within 30 days of purchase, subject to our refund policy.",
        "Subscription fees are non-refundable except as required by law."
      ]
    },
    {
      id: "limitation",
      title: "8. Limitation of Liability",
      icon: <FaExclamationTriangle />,
      color: "from-red-500 to-pink-500",
      content: [
        "We provide the service 'as is' without any warranties, express or implied.",
        "We are not liable for any indirect, incidental, or consequential damages.",
        "Our total liability shall not exceed the amount paid by you for the service.",
        "Some jurisdictions do not allow limitation of liability, so these limitations may not apply to you."
      ]
    },
    {
      id: "termination",
      title: "9. Termination",
      icon: <FaGavel />,
      color: "from-gray-500 to-slate-500",
      content: [
        "We may terminate or suspend your account immediately for any breach of these terms.",
        "You may terminate your account at any time by contacting our support team.",
        "Upon termination, your right to use the service will cease immediately.",
        "Provisions that should survive termination will remain in effect."
      ]
    },
    {
      id: "changes",
      title: "10. Changes to Terms",
      icon: <FaCalendarAlt />,
      color: "from-blue-500 to-indigo-500",
      content: [
        "We reserve the right to modify these terms at any time.",
        "Changes will be effective immediately upon posting on this page.",
        "Your continued use of the service constitutes acceptance of the modified terms.",
        "We will notify users of significant changes via email or platform notifications."
      ]
    }
  ];

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="py-16 px-4 md:px-6 lg:px-8"
    >
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div variants={itemVariants} className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <FaFileContract className="text-2xl text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white">Terms & Conditions</h1>
          </div>
          <div className="bg-gradient-to-br from-slate-800/90 via-purple-900/80 to-pink-900/70 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-slate-600/50 hover:border-purple-400/50 transition-all duration-500">
            <p className="text-xl text-slate-200 leading-relaxed max-w-4xl mx-auto mb-4">
              Please read these Terms and Conditions carefully before using our service. 
              These terms govern your use of our platform and services.
            </p>
            <div className="flex items-center justify-center gap-2 text-slate-300">
              <FaCalendarAlt className="text-purple-400" />
              <span className="text-sm">Last updated: January 2024</span>
            </div>
          </div>
        </motion.div>

        {/* Quick Navigation */}
        <motion.div variants={itemVariants} className="mb-12">
          <div className="bg-gradient-to-br from-slate-800/90 via-slate-700/80 to-slate-800/90 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-slate-600/50">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <FaInfoCircle className="text-blue-400" />
              Quick Navigation
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {sections.map((section, index) => (
                <a
                  key={section.id}
                  href={`#${section.id}`}
                  className="flex items-center gap-2 p-3 rounded-lg bg-slate-700/30 hover:bg-slate-600/50 text-slate-300 hover:text-white transition-all duration-300 text-sm"
                >
                  <span className="text-blue-400">{section.icon}</span>
                  {section.title}
                </a>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Terms Sections */}
        <div className="space-y-8">
          {sections.map((section, index) => (
            <motion.div
              key={section.id}
              id={section.id}
              variants={itemVariants}
              className="bg-gradient-to-br from-slate-800/90 via-slate-700/80 to-slate-800/90 backdrop-blur-xl rounded-2xl shadow-xl p-8 border border-slate-600/50 hover:border-slate-500/50 transition-all duration-500"
            >
              <div className="flex items-center gap-4 mb-6">
                <div className={`w-12 h-12 bg-gradient-to-r ${section.color} rounded-xl flex items-center justify-center`}>
                  <span className="text-white text-xl">{section.icon}</span>
                </div>
                <h2 className="text-2xl font-bold text-white">{section.title}</h2>
              </div>
              
              <div className="space-y-4">
                {section.content.map((paragraph, pIndex) => (
                  <p key={pIndex} className="text-slate-300 leading-relaxed">
                    {paragraph}
                  </p>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Contact Information */}
        <motion.div variants={itemVariants} className="mt-16">
          <div className="bg-gradient-to-br from-slate-800/90 via-blue-900/80 to-purple-900/70 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-slate-600/50 hover:border-blue-400/50 transition-all duration-500">
            <div className="text-center">
              <div className="flex items-center justify-center gap-3 mb-6">
                <FaEnvelope className="text-3xl text-blue-400" />
                <h2 className="text-3xl font-bold text-white">Questions About These Terms?</h2>
              </div>
              <p className="text-xl text-slate-200 mb-8 max-w-2xl mx-auto">
                If you have any questions about these Terms and Conditions, please don't hesitate to contact us.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 font-semibold">
                  Contact Support
                </button>
                <button className="px-8 py-4 bg-slate-700/50 hover:bg-slate-600/50 text-white rounded-xl border border-slate-600/50 hover:border-slate-500/50 transition-all duration-300 font-semibold">
                  Legal Department
                </button>
              </div>
              
              <div className="mt-8 pt-8 border-t border-slate-600/30">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                  <div>
                    <FaEnvelope className="text-2xl text-blue-400 mx-auto mb-2" />
                    <div className="text-white font-semibold">Email</div>
                    <div className="text-slate-300 text-sm"><EMAIL></div>
                  </div>
                  <div>
                    <FaGlobe className="text-2xl text-green-400 mx-auto mb-2" />
                    <div className="text-white font-semibold">Address</div>
                    <div className="text-slate-300 text-sm">123 Tech Street, City, Country</div>
                  </div>
                  <div>
                    <FaCalendarAlt className="text-2xl text-purple-400 mx-auto mb-2" />
                    <div className="text-white font-semibold">Response Time</div>
                    <div className="text-slate-300 text-sm">Within 48 hours</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default TermsAndConditions;
