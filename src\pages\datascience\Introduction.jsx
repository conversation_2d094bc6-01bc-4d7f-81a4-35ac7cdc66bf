import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ump<PERSON>,
  Si<PERSON>atplotlib,
  Si<PERSON><PERSON>born,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Si<PERSON><PERSON>yter,
  SiSql,
  <PERSON><PERSON><PERSON>ly,
} from "./components/DataScienceIntroduction.icons";

const DataScienceIntroduction = ({ showPremiumOverlay, onBackToCourse }) => {
  const [showMore, setShowMore] = useState(false);

  const toggleReadMore = () => {
    setShowMore(!showMore);
  };

  // CSS styles for animations
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes scaleIn {
      from { transform: scale(0.95); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    .animate-fadeIn {
      animation: fadeIn 0.6s ease-out forwards;
    }
    
    .animate-scaleIn {
      animation: scaleIn 0.5s ease-out forwards;
    }
    
    .hover-scale {
      transition: transform 0.3s ease;
    }
    
    .hover-scale:hover {
      transform: scale(1.05);
    }
  `;

  return (
    <div className="py-8">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => {
              if (onBackToCourse) {
                onBackToCourse();
              }
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <span className="text-xl">←</span>
            <span>Back to Course</span>
          </button>
        </div>

        {/* Header Section */}
        <div className="text-center mb-16 animate-fadeIn">
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-teal-600 to-blue-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
            📊 Data Science Fundamentals
          </div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Master{" "}
            <span className="bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text text-transparent">
              Data Science
            </span>
          </h2>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Transform raw data into actionable insights with our comprehensive data science curriculum
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Left Column */}
          <div className="space-y-8 animate-fadeIn">
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                <span className="text-3xl mr-3">🎯</span>
                What You'll Learn
              </h3>
              
              <div className="space-y-4">
                {[
                  "Python programming for data analysis",
                  "Statistical analysis and hypothesis testing",
                  "Data visualization with matplotlib & seaborn",
                  "Machine learning algorithms and modeling",
                  "Deep learning with TensorFlow and PyTorch",
                  "Big data processing with pandas and numpy",
                  "SQL for data manipulation",
                  "Data cleaning and preprocessing techniques"
                ].map((item, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-teal-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-white/80">{item}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                <span className="text-3xl mr-3">📈</span>
                Career Opportunities
              </h3>
              
              <div className="grid grid-cols-2 gap-4">
                {[
                  "Data Scientist",
                  "Data Analyst", 
                  "ML Engineer",
                  "Research Scientist",
                  "Business Analyst",
                  "Data Engineer",
                  "AI Researcher",
                  "Consultant"
                ].map((role, index) => (
                  <div key={index} className="bg-teal-500/10 border border-teal-500/20 rounded-lg p-3 text-center">
                    <span className="text-teal-300 font-medium text-sm">{role}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-8 animate-fadeIn">
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                <span className="text-3xl mr-3">🛠️</span>
                Tools & Technologies
              </h3>
              
              <div className="grid grid-cols-3 gap-4">
                {[
                  { name: "Python", icon: <SiPython className="w-8 h-8 mx-auto text-[#3776AB]" /> },
                  { name: "Pandas", icon: <SiPandas className="w-8 h-8 mx-auto text-[#150458]" /> },
                  { name: "NumPy", icon: <SiNumpy className="w-8 h-8 mx-auto text-[#013243]" /> },
                  { name: "Matplotlib", icon: <SiMatplotlib className="w-8 h-8 mx-auto text-[#11557C]" /> },
                  { name: "Seaborn", icon: <SiSeaborn className="w-8 h-8 mx-auto text-[#4C72B0]" /> },
                  { name: "Scikit-learn", icon: <SiScikitlearn className="w-8 h-8 mx-auto text-[#F7931E]" /> },
                  { name: "TensorFlow", icon: <SiTensorflow className="w-8 h-8 mx-auto text-[#FF6F00]" /> },
                  { name: "Jupyter", icon: <SiJupyter className="w-8 h-8 mx-auto text-[#F37626]" /> },
                  { name: "SQL", icon: <SiSql className="w-8 h-8 mx-auto text-[#4479A1]" /> },
                  { name: "Plotly", icon: <SiPlotly className="w-8 h-8 mx-auto text-[#3F4F75]" /> }
                ].map((tool, index) => (
                  <div key={index} className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 text-center hover:bg-blue-500/20 transition-colors duration-300">
                    <div className="text-2xl mb-2">{tool.icon}</div>
                    <span className="text-blue-300 font-medium text-xs">{tool.name}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                <span className="text-3xl mr-3">📚</span>
                Learning Path
              </h3>
              
              <div className="space-y-4">
                {[
                  { phase: "Foundation", duration: "4 weeks", topics: "Python, Statistics, Data Analysis" },
                  { phase: "Visualization", duration: "3 weeks", topics: "Matplotlib, Seaborn, Plotly" },
                  { phase: "Machine Learning", duration: "6 weeks", topics: "Algorithms, Model Building" },
                  { phase: "Deep Learning", duration: "4 weeks", topics: "Neural Networks, TensorFlow" },
                  { phase: "Projects", duration: "3 weeks", topics: "Real-world Applications" }
                ].map((phase, index) => (
                  <div key={index} className="border-l-4 border-teal-400 pl-4 py-2">
                    <div className="flex justify-between items-start mb-1">
                      <h4 className="font-semibold text-white">{phase.phase}</h4>
                      <span className="text-xs text-teal-300 bg-teal-500/20 px-2 py-1 rounded">{phase.duration}</span>
                    </div>
                    <p className="text-white/60 text-sm">{phase.topics}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Expandable Content */}
        <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl mb-12 animate-scaleIn">
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
            <span className="text-3xl mr-3">💡</span>
            Course Details
          </h3>
          
          <div className="text-white/80 leading-relaxed">
            <p className="mb-4">
              Our comprehensive Data Science course is designed to take you from beginner to professional level. 
              You'll start with Python fundamentals and progress through statistical analysis, machine learning, 
              and deep learning concepts.
            </p>
            
            {showMore && (
              <div className="animate-fadeIn">
                <p className="mb-4">
                  The curriculum includes hands-on projects working with real datasets from various industries including 
                  finance, healthcare, e-commerce, and social media. You'll learn to clean messy data, perform exploratory 
                  data analysis, build predictive models, and create compelling visualizations.
                </p>
                
                <p className="mb-4">
                  Advanced topics include natural language processing, computer vision, time series analysis, and 
                  deployment strategies for machine learning models. By the end of this course, you'll have a 
                  portfolio of projects demonstrating your ability to solve real business problems with data.
                </p>
                
                <div className="bg-teal-500/10 border border-teal-500/20 rounded-lg p-4 mt-6">
                  <h4 className="font-bold text-teal-300 mb-2">Industry Recognition</h4>
                  <p className="text-sm">
                    Our curriculum is designed in collaboration with industry experts from top tech companies. 
                    Graduates have successfully transitioned to data science roles at Google, Microsoft, Amazon, 
                    and leading startups worldwide.
                  </p>
                </div>
              </div>
            )}
            
            <button
              onClick={toggleReadMore}
              className="text-teal-400 hover:text-teal-300 font-medium mt-4 transition-colors duration-300"
            >
              {showMore ? "Show Less ↑" : "Read More ↓"}
            </button>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-teal-600/20 to-blue-600/20 backdrop-blur-lg border border-white/10 rounded-3xl p-12 animate-scaleIn">
          <h3 className="text-3xl font-bold text-white mb-4">
            Ready to Start Your Data Science Journey?
          </h3>
          <p className="text-white/80 text-lg mb-8 max-w-2xl mx-auto">
            Join thousands of students who have successfully launched their data science careers with our comprehensive program.
          </p>
          <button
            onClick={showPremiumOverlay}
            className="bg-gradient-to-r from-teal-600 to-blue-600 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl hover-scale"
          >
            Get Premium Access →
          </button>
        </div>
      </div>
    </div>
  );
};

export default DataScienceIntroduction;
