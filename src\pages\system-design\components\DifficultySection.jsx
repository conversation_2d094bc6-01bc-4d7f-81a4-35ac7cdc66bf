import { motion } from "framer-motion";
import QuestionCard from "./QuestionCard";

const DifficultySection = ({ section, sectionIndex }) => {
  const getDifficultyConfig = (badgeClass) => {
    const configs = {
      "difficulty-easy": {
        gradient: "from-green-500 to-emerald-500",
        icon: "fas fa-leaf",
        bgGradient: "from-green-50 to-emerald-50",
        borderColor: "border-green-200"
      },
      "difficulty-medium": {
        gradient: "from-orange-500 to-amber-500",
        icon: "fas fa-fire",
        bgGradient: "from-orange-50 to-amber-50",
        borderColor: "border-orange-200"
      },
      "difficulty-hard": {
        gradient: "from-red-500 to-rose-500",
        icon: "fas fa-bolt",
        bgGradient: "from-red-50 to-rose-50",
        borderColor: "border-red-200"
      }
    };
    return configs[badgeClass] || configs["difficulty-easy"];
  };

  const config = getDifficultyConfig(section.badgeClass);

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: sectionIndex * 0.2 }}
      className={`bg-gradient-to-br ${config.bgGradient} rounded-2xl p-6 mb-8 shadow-lg ${config.borderColor} border`}
    >
      <motion.div
        initial={{ opacity: 0, x: -30 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: sectionIndex * 0.2 + 0.2 }}
        className="flex items-center gap-4 mb-6 pb-4 border-b border-gray-200"
      >
        <motion.i
          className={`${config.icon} text-2xl text-white p-3 bg-gradient-to-r ${config.gradient} rounded-xl shadow-md`}
          whileHover={{ rotate: 10, scale: 1.1 }}
          transition={{ duration: 0.3 }}
        />
        <h2 className="text-2xl font-bold text-gray-800">
          {section.level}
        </h2>
        <motion.span
          whileHover={{ scale: 1.05 }}
          className={`px-4 py-2 bg-gradient-to-r ${config.gradient} text-white text-sm font-bold rounded-full shadow-md`}
        >
          {section.badgeText}
        </motion.span>
      </motion.div>

      <div className="space-y-4">
        {section.questions.map((question, qIndex) => (
          <QuestionCard 
            key={qIndex} 
            question={question} 
            index={sectionIndex * 2 + qIndex} 
          />
        ))}
      </div>
    </motion.div>
  );
};

export default DifficultySection;
