import React from "react";
import Editor from "@monaco-editor/react";
import { FaTimes } from "react-icons/fa";

const EditorArea = ({
  files,
  activeFileId,
  openTabs,
  theme,
  onFileSelect,
  onTabClose,
  onEditorChange,
  onEditorMount,
  getFileIcon
}) => {
  const currentFile = files[activeFileId];

  return (
    <div className="flex-1 flex flex-col">
      {/* Tab Bar */}
      <div className="bg-gray-800 border-b border-gray-700 flex items-center">
        {openTabs.map((tabId) => {
          const file = files[tabId];
          if (!file) return null;
          
          return (
            <div
              key={tabId}
              className={`flex items-center px-4 py-2 border-r border-gray-700 cursor-pointer transition-colors ${
                activeFileId === tabId 
                  ? 'bg-gray-900 text-white' 
                  : 'bg-gray-800 text-gray-400 hover:text-white hover:bg-gray-750'
              }`}
              onClick={() => onFileSelect(tabId)}
            >
              <span className="text-xs mr-2">{getFileIcon(file.name)}</span>
              <span className="text-sm">{file.name}</span>
              {!file.saved && <span className="w-1.5 h-1.5 bg-orange-400 rounded-full ml-2"></span>}
              
              {openTabs.length > 1 && (
                <button
                  onClick={(e) => onTabClose(e, tabId)}
                  className="ml-2 p-1 hover:bg-gray-600 rounded transition-colors"
                >
                  <FaTimes className="text-xs" />
                </button>
              )}
            </div>
          );
        })}
      </div>

      {/* Editor Container */}
      <div className="flex-1 flex">
        {/* Code Editor - Now takes full width */}
        <div className="flex-1 bg-gray-900">
          {currentFile && (
            <Editor
              height="100%"
              language={currentFile.language}
              value={currentFile.content}
              onChange={onEditorChange}
              onMount={onEditorMount}
              theme={theme === 'vs-dark' ? 'mern-dark' : 'light'}
              options={{
                minimap: { enabled: false },
                fontSize: 14,
                wordWrap: "on",
                automaticLayout: true,
                scrollBeyondLastLine: false,
                renderWhitespace: 'selection',
                selectOnLineNumbers: true,
                roundedSelection: false,
                readOnly: false,
                cursorStyle: 'line',
                glyphMargin: true,
                folding: true,
                lineNumbers: 'on',
                lineDecorationsWidth: 10,
                lineNumbersMinChars: 3,
                renderLineHighlight: 'all',
                contextmenu: true,
                mouseWheelZoom: true,
                smoothScrolling: true,
                cursorBlinking: 'blink',
                cursorSmoothCaretAnimation: true,
                renderFinalNewline: true,
                quickSuggestions: true,
                suggestOnTriggerCharacters: true,
                acceptSuggestionOnEnter: 'on',
                tabCompletion: 'on',
                wordBasedSuggestions: true,
                parameterHints: { enabled: true },
                autoClosingBrackets: 'always',
                autoClosingQuotes: 'always',
                autoSurround: 'languageDefined',
                colorDecorators: true,
                dragAndDrop: true,
                find: {
                  seedSearchStringFromSelection: true,
                  autoFindInSelection: 'never'
                }
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default EditorArea;
