import React from "react";
import { Link } from "react-router-dom";
import {
  FaSave,
  FaPlay,
  FaSun,
  FaMoon,
  FaEye,
  FaFolder
} from "react-icons/fa";

const TopMenuBar = ({
  onBackToCourse,
  selectedChallenge,
  challenges,
  onChallengeChange,
  theme,
  onToggleTheme,
  onSaveAllFiles,
  onRunCode,
  onOpenPreview,
  onToggleSidebar
}) => {
  return (
    <div className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <button 
          onClick={onBackToCourse}
          className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm transition-colors"
        >
          ← Back to Course
        </button>
        <h2 className="text-lg font-semibold text-white">MERN Stack Lab Environment</h2>
        
        {/* Challenge Selector in Top Bar */}
        <div className="flex items-center space-x-2">
          <label className="text-sm text-gray-300">Challenge:</label>
          <select
            value={selectedChallenge}
            onChange={onChallengeChange}
            className="px-3 py-1 bg-gray-700 border border-gray-600 rounded text-sm text-white focus:ring-1 focus:ring-green-400 focus:border-green-400"
          >
            {challenges.map((challenge) => (
              <option key={challenge.value} value={challenge.value}>
                {challenge.label}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      <div className="flex items-center space-x-2">
        <Link
          to="/api-tester"
          className="px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors text-sm font-medium"
          title="API Tester"
        >
          API Tester
        </Link>
        <Link
          to="/process-manager"
          className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded transition-colors text-sm font-medium"
          title="Process Manager"
        >
          Process Manager
        </Link>
        <button
          onClick={onToggleTheme}
          className="p-2 hover:bg-gray-700 rounded transition-colors"
          title="Toggle Theme"
        >
          {theme === 'vs-dark' ? <FaSun className="text-yellow-400" /> : <FaMoon className="text-blue-400" />}
        </button>
        <button
          onClick={onSaveAllFiles}
          className="p-2 hover:bg-gray-700 rounded transition-colors"
          title="Save All Files"
        >
          <FaSave className="text-green-400" />
        </button>
        <button
          onClick={onRunCode}
          className="p-2 hover:bg-gray-700 rounded transition-colors"
          title="Run Code"
        >
          <FaPlay className="text-blue-400" />
        </button>
        <button
          onClick={onOpenPreview}
          className="p-2 hover:bg-gray-700 rounded transition-colors"
          title="Open Live Preview"
        >
          <FaEye className="text-green-400" />
        </button>
        <button
          onClick={onToggleSidebar}
          className="p-2 hover:bg-gray-700 rounded transition-colors"
          title="Toggle Sidebar"
        >
          <FaFolder className="text-gray-400" />
        </button>
      </div>
    </div>
  );
};

export default TopMenuBar;
