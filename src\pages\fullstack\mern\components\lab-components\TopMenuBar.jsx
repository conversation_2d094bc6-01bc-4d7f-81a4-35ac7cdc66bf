import React from "react";
import { <PERSON> } from "react-router-dom";
import {
  FaSave,
  FaPlay,
  FaSun,
  FaMoon,
  FaEye,
  FaFolder,
  FaTools,
  FaCogs
} from "react-icons/fa";

const TopMenuBar = ({
  onBackToCourse,
  selectedChallenge,
  challenges,
  onChallengeChange,
  theme,
  onToggleTheme,
  onSaveAllFiles,
  onRunCode,
  onOpenPreview,
  onToggleSidebar
}) => {
  return (
    <div className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center justify-between">
      <div className="flex items-center space-x-2 md:space-x-4">
        <button
          onClick={onBackToCourse}
          className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm transition-colors"
        >
          <span className="hidden sm:inline">← Back to Course</span>
          <span className="sm:hidden">←</span>
        </button>
        <h2 className="text-sm md:text-lg font-semibold text-white">
          <span className="hidden md:inline">MERN Stack Lab Environment</span>
          <span className="md:hidden">MERN Lab</span>
        </h2>

        {/* Challenge Selector in Top Bar */}
        <div className="hidden sm:flex items-center space-x-2">
          <label className="text-sm text-gray-300">Challenge:</label>
          <select
            value={selectedChallenge}
            onChange={onChallengeChange}
            className="px-3 py-1 bg-gray-700 border border-gray-600 rounded text-sm text-white focus:ring-1 focus:ring-green-400 focus:border-green-400"
          >
            {challenges.map((challenge) => (
              <option key={challenge.value} value={challenge.value}>
                {challenge.label}
              </option>
            ))}
          </select>
        </div>

        {/* Mobile Challenge Selector */}
        <div className="sm:hidden">
          <select
            value={selectedChallenge}
            onChange={onChallengeChange}
            className="px-2 py-1 bg-gray-700 border border-gray-600 rounded text-xs text-white focus:ring-1 focus:ring-green-400 focus:border-green-400"
          >
            {challenges.map((challenge) => (
              <option key={challenge.value} value={challenge.value}>
                {challenge.label.length > 15 ? challenge.label.substring(0, 15) + '...' : challenge.label}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      <div className="flex items-center space-x-1 md:space-x-2">
        {/* Developer Tools Group */}
        <div className="flex items-center space-x-1 bg-gray-700/50 rounded-lg p-1">
          <Link
            to="/api-tester"
            className="group flex items-center space-x-1 md:space-x-2 px-2 md:px-3 py-2 bg-green-600/90 hover:bg-green-600 text-white rounded-md transition-all duration-200 text-xs md:text-sm font-medium transform hover:scale-105 hover:shadow-lg"
            title="Test APIs and endpoints"
          >
            <FaTools className="text-xs md:text-sm group-hover:rotate-12 transition-transform duration-200" />
            <span className="hidden lg:inline">API Tester</span>
            <span className="lg:hidden hidden sm:inline">API</span>
          </Link>
          <Link
            to="/process-manager"
            className="group flex items-center space-x-1 md:space-x-2 px-2 md:px-3 py-2 bg-purple-600/90 hover:bg-purple-600 text-white rounded-md transition-all duration-200 text-xs md:text-sm font-medium transform hover:scale-105 hover:shadow-lg"
            title="Monitor system processes"
          >
            <FaCogs className="text-xs md:text-sm group-hover:spin transition-transform duration-500" />
            <span className="hidden lg:inline">Processes</span>
            <span className="lg:hidden hidden sm:inline">Proc</span>
          </Link>
        </div>

        {/* Separator */}
        <div className="w-px h-6 bg-gray-600"></div>

        {/* Lab Actions Group */}
        <div className="hidden sm:flex items-center space-x-1 bg-gray-700/50 rounded-lg p-1">
          <button
            onClick={onSaveAllFiles}
            className="group p-2 hover:bg-gray-600 rounded-md transition-all duration-200 transform hover:scale-110"
            title="Save All Files (Ctrl+S)"
          >
            <FaSave className="text-green-400 group-hover:text-green-300 transition-colors" />
          </button>
          <button
            onClick={onRunCode}
            className="group p-2 hover:bg-gray-600 rounded-md transition-all duration-200 transform hover:scale-110"
            title="Run Code (Ctrl+Enter)"
          >
            <FaPlay className="text-blue-400 group-hover:text-blue-300 transition-colors" />
          </button>
          <button
            onClick={onOpenPreview}
            className="group p-2 hover:bg-gray-600 rounded-md transition-all duration-200 transform hover:scale-110"
            title="Open Live Preview"
          >
            <FaEye className="text-green-400 group-hover:text-green-300 transition-colors" />
          </button>
        </div>

        {/* Mobile Actions - Compact */}
        <div className="sm:hidden flex items-center space-x-1">
          <button
            onClick={onRunCode}
            className="group p-2 bg-blue-600/90 hover:bg-blue-600 rounded-md transition-all duration-200 transform hover:scale-110"
            title="Run Code"
          >
            <FaPlay className="text-white text-xs" />
          </button>
          <button
            onClick={onSaveAllFiles}
            className="group p-2 bg-green-600/90 hover:bg-green-600 rounded-md transition-all duration-200 transform hover:scale-110"
            title="Save All"
          >
            <FaSave className="text-white text-xs" />
          </button>
        </div>

        {/* Separator */}
        <div className="hidden sm:block w-px h-6 bg-gray-600"></div>

        {/* Settings Group */}
        <div className="flex items-center space-x-1 bg-gray-700/50 rounded-lg p-1">
          <button
            onClick={onToggleTheme}
            className="group p-2 hover:bg-gray-600 rounded-md transition-all duration-200 transform hover:scale-110"
            title="Toggle Theme"
          >
            {theme === 'vs-dark' ?
              <FaSun className="text-yellow-400 group-hover:text-yellow-300 transition-colors text-xs md:text-sm" /> :
              <FaMoon className="text-blue-400 group-hover:text-blue-300 transition-colors text-xs md:text-sm" />
            }
          </button>
          <button
            onClick={onToggleSidebar}
            className="group p-2 hover:bg-gray-600 rounded-md transition-all duration-200 transform hover:scale-110"
            title="Toggle Sidebar"
          >
            <FaFolder className="text-gray-400 group-hover:text-gray-300 transition-colors text-xs md:text-sm" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default TopMenuBar;
