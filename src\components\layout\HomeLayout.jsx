import { motion } from "framer-motion";
import { AnimatedBackground } from "../ui";
import { SidebarWrapper, MainContentWrapper } from "./index";

const HomeLayout = ({ isSidebarOpen, toggleSidebar }) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden"
    >
      {/* Background animated elements */}
      
      <SidebarWrapper isSidebarOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />

      {/* Main Content with dynamic left margin for fixed sidebar */}
      <MainContentWrapper isSidebarOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
    </motion.div>
  );
};

export default HomeLayout;
