import React, { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { LeetCodeEditor } from "./leetcode-editor";

const DSALabEnvironment = ({ onBackToCourse, showPremiumOverlay }) => {
  const [viewMode, setViewMode] = useState('editor'); // 'overview' or 'editor'

  const practiceProblems = [
    {
      title: "Array Manipulation",
      difficulty: "Easy",
      description: "Practice basic array operations and manipulations",
      tags: ["Arrays", "Loops", "Indexing"]
    },
    {
      title: "Linked List Operations",
      difficulty: "Medium",
      description: "Implement and manipulate linked list data structures",
      tags: ["Linked Lists", "Pointers", "Memory Management"]
    },
    {
      title: "Binary Search Tree",
      difficulty: "Medium",
      description: "Create and traverse binary search trees",
      tags: ["Trees", "Recursion", "Sorting"]
    },
    {
      title: "Graph Algorithms",
      difficulty: "Hard",
      description: "Implement BFS, DFS and shortest path algorithms",
      tags: ["Graphs", "Traversal", "Pathfinding"]
    }
  ];

  if (viewMode === 'editor') {
    return (
      <LeetCodeEditor
        onBackToCourse={() => setViewMode('overview')}
        showPremiumOverlay={showPremiumOverlay}
      />
    );
  }

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-900/70 to-teal-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">Practice Lab</h2>
        <div className="flex items-center space-x-3">
          <Link
            to="/api-tester"
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors font-medium"
          >
            API Tester
          </Link>
          <button
            onClick={() => setViewMode('editor')}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
          >
            🚀 Launch LeetCode Editor
          </button>
          <button
            onClick={onBackToCourse}
            className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
          >
            Back to Course
          </button>
        </div>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Interactive DSA Practice Environment</h3>
          <p className="text-gray-300">
            Sharpen your DSA skills with our interactive coding environment. Solve problems, 
            test your solutions, and track your progress.
          </p>
        </div>
        
        {/* Practice Problems */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {practiceProblems.map((problem, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="border border-gray-700/30 bg-gray-800/30 rounded-lg p-4 hover:shadow-md transition-shadow"
              onClick={showPremiumOverlay}
            >
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-white">{problem.title}</h4>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  problem.difficulty === "Easy" ? "bg-green-100 text-green-800" :
                  problem.difficulty === "Medium" ? "bg-yellow-100 text-yellow-800" :
                  "bg-red-100 text-red-800"
                }`}>
                  {problem.difficulty}
                </span>
              </div>
              <p className="text-sm text-gray-300 mb-3">{problem.description}</p>
              <div className="flex flex-wrap gap-2">
                {problem.tags.map((tag, idx) => (
                  <span key={idx} className="px-2 py-1 bg-blue-900/30 text-blue-300 text-xs rounded-md border border-blue-700/30">
                    {tag}
                  </span>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* LeetCode Editor Features */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-8"
        >
          <div className="bg-gradient-to-r from-blue-900/30 to-purple-900/30 rounded-lg p-6 border border-blue-700/30">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center">
              <span className="mr-3">🚀</span>
              LeetCode-Style DSA Editor
            </h3>
            <p className="text-gray-300 mb-6">
              Experience a professional coding environment with our LeetCode-style editor featuring
              resizable panels, multiple language support, and real-time testing capabilities.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                <div className="text-2xl mb-2">🔧</div>
                <h4 className="font-semibold text-white mb-2">Multi-Language Support</h4>
                <p className="text-sm text-gray-300">Code in JavaScript, Python, Java, and C++ with syntax highlighting</p>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                <div className="text-2xl mb-2">📱</div>
                <h4 className="font-semibold text-white mb-2">Resizable Panels</h4>
                <p className="text-sm text-gray-300">Drag to resize problem and code panels for optimal viewing</p>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                <div className="text-2xl mb-2">🧪</div>
                <h4 className="font-semibold text-white mb-2">Test Cases</h4>
                <p className="text-sm text-gray-300">Run custom inputs and validate against predefined test cases</p>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                <div className="text-2xl mb-2">🌙</div>
                <h4 className="font-semibold text-white mb-2">Theme Toggle</h4>
                <p className="text-sm text-gray-300">Switch between dark and light themes for comfortable coding</p>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                <div className="text-2xl mb-2">📊</div>
                <h4 className="font-semibold text-white mb-2">Real-time Output</h4>
                <p className="text-sm text-gray-300">See execution results and submission feedback instantly</p>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                <div className="text-2xl mb-2">📱</div>
                <h4 className="font-semibold text-white mb-2">Mobile Responsive</h4>
                <p className="text-sm text-gray-300">Optimized interface that works seamlessly on all devices</p>
              </div>
            </div>

            <div className="text-center space-y-4">
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Link
                  to="/api-tester"
                  className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
                >
                  🔧 API Tester
                </Link>
                <button
                  onClick={() => setViewMode('editor')}
                  className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
                >
                  🚀 Launch LeetCode Editor
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default DSALabEnvironment;