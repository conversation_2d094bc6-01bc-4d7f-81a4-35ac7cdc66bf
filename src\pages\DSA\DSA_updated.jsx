import React from "react";
import DSAIntroduction from "./components/DSAIntroduction";
import DSALabEnvironment from "./components/DSALabEnvironment";
import DSALiveClasses from "./components/DSALiveClasses";
import DSAFAQS from "./components/DSAFAQS";
import { DifficultySection, InterviewChecklist } from "./components";
import DSAHero from "./components/DSAHero";
import DSALayout from "./components/DSALayout";
import DSAPremiumModal from "./components/DSAPremiumModal";
import { CourseResourcesSection } from "../../components/ui";
import useSidebarState from "../../hooks/useSidebarState";
import useDSAData from "./hooks/useDSAData";

const DSA_updated = () => {
  const { dsaData, interviewChecklist } = useDSAData();

  const courseConfig = {
    title: "DSA Course Resources",
    subtitle: "Select a resource category to master Data Structures & Algorithms through theory, practice, live classes, or FAQs",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-gray-300"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn DSA fundamentals and core concepts",
        icon: "📚",
        component: DSAIntroduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Lab Environment",
        description: "Practice coding in interactive environments",
        icon: "💻",
        component: DSALabEnvironment,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Classes",
        description: "Join instructor-led coding sessions",
        icon: "🎥",
        component: DSALiveClasses,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to common DSA questions",
        icon: "❓",
        component: DSAFAQS,
        props: {}
      },
      {
        id: "DifficultyLevels",
        title: "Practice by Difficulty",
        description: "Progress from basic to advanced problems",
        icon: "📊",
        component: DifficultySection,
        props: { dsaData }
      },
      {
        id: "InterviewPrep",
        title: "Interview Preparation",
        description: "Essential checklist for tech interviews",
        icon: "🎯",
        component: InterviewChecklist,
        props: { interviewChecklist }
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={DSAHero}
      LayoutComponent={DSALayout}
      PremiumModalComponent={DSAPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default DSA_updated;
