import { motion } from "framer-motion";
import { FaPython, FaReact, FaDatabase, FaServer } from "react-icons/fa";

const PythonFullStackHero = ({ showPremiumOverlay }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <div className="relative overflow-hidden min-h-[80vh] flex items-center">
      {/* Background Elements */}
      <div className="absolute inset-0 z-0">
        {/* Animated gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a2e44] to-[#0d1b2a]"></div>
        
        {/* Animated code pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 right-0 bottom-0 text-xs md:text-sm overflow-hidden text-gray-400 font-mono">
            {Array.from({ length: 20 }).map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0.3, x: -10 }}
                animate={{ opacity: [0.1, 0.3, 0.1], x: 0 }}
                transition={{ duration: 10, repeat: Infinity, delay: i * 0.2 }}
                className="whitespace-nowrap"
                style={{ position: 'absolute', top: `${i * 5}%` }}
              >
                {`from flask import Flask, jsonify\napp = Flask(__name__)\n\<EMAIL>('/api/data')\ndef get_data():\n    return jsonify({'message': 'Hello from Python!'})\n`.repeat(2)}
              </motion.div>
            ))}
          </div>
        </div>
        
        {/* Glowing orbs */}
        <motion.div
          animate={{ scale: [1, 1.2, 1], opacity: [0.2, 0.3, 0.2] }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
          className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-green-500/20 blur-3xl"
        />
        <motion.div
          animate={{ scale: [1, 1.3, 1], opacity: [0.1, 0.2, 0.1] }}
          transition={{ duration: 10, repeat: Infinity, ease: "easeInOut", delay: 1 }}
          className="absolute bottom-1/3 right-1/4 w-96 h-96 rounded-full bg-blue-500/20 blur-3xl"
        />
      </div>
      
      {/* Content */}
      <div className="container mx-auto px-4 py-16 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="flex flex-col lg:flex-row items-center gap-12"
        >
          {/* Left side - Text content */}
          <div className="lg:w-1/2 text-center lg:text-left">
            <motion.div variants={itemVariants} className="inline-block mb-2 px-3 py-1 bg-green-900/30 border border-green-700/30 rounded-full">
              <span className="text-green-400 text-sm font-medium">Full Stack Development</span>
            </motion.div>
            
            <motion.h1 variants={itemVariants} className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              <span className="text-white">Python</span>
              <br />
              <span className="bg-gradient-to-r from-green-400 via-blue-400 to-purple-500 bg-clip-text text-transparent">Full Stack Journey</span>
            </motion.h1>
            
            <motion.p variants={itemVariants} className="text-gray-300 text-lg mb-8 max-w-xl">
              Master both frontend and backend development with Python. Learn Flask, Django, 
              database design, API development, and modern frontend frameworks to build 
              complete web applications.
            </motion.p>
            
            <motion.div variants={itemVariants} className="flex flex-wrap gap-4 justify-center lg:justify-start">
              <button 
                onClick={showPremiumOverlay}
                className="px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-medium rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-green-500/25"
              >
                Start Learning
              </button>
              <button className="px-6 py-3 border border-green-500/30 hover:border-green-500/50 text-green-400 hover:text-green-300 font-medium rounded-lg transition-all duration-300 bg-green-900/20 hover:bg-green-900/30">
                View Curriculum
              </button>
            </motion.div>
          </div>
          
          {/* Right side - Visual elements */}
          <div className="lg:w-1/2">
            <motion.div 
              variants={itemVariants}
              className="relative"
            >
              {/* Stack visualization */}
              <div className="relative h-80">
                {/* Frontend Layer */}
                <motion.div 
                  whileHover={{ y: -5 }}
                  className="absolute top-0 left-0 right-0 h-24 bg-gradient-to-r from-blue-900/40 to-blue-800/40 rounded-t-xl border border-blue-700/30 p-4 z-30"
                >
                  <div className="flex items-center gap-3 mb-2">
                    <FaReact className="text-blue-400 text-xl" />
                    <h3 className="text-white font-medium">Frontend</h3>
                  </div>
                  <p className="text-blue-200 text-sm">React, Vue.js, or HTML/CSS/JS with Python templates</p>
                </motion.div>
                
                {/* Backend Layer */}
                <motion.div 
                  whileHover={{ y: -5 }}
                  className="absolute top-20 left-4 right-4 h-24 bg-gradient-to-r from-green-900/40 to-green-800/40 border border-green-700/30 p-4 z-20"
                >
                  <div className="flex items-center gap-3 mb-2">
                    <FaPython className="text-green-400 text-xl" />
                    <h3 className="text-white font-medium">Backend</h3>
                  </div>
                  <p className="text-green-200 text-sm">Flask, Django, FastAPI for robust server-side logic</p>
                </motion.div>
                
                {/* API Layer */}
                <motion.div 
                  whileHover={{ y: -5 }}
                  className="absolute top-40 left-8 right-8 h-24 bg-gradient-to-r from-purple-900/40 to-purple-800/40 border border-purple-700/30 p-4 z-10"
                >
                  <div className="flex items-center gap-3 mb-2">
                    <FaServer className="text-purple-400 text-xl" />
                    <h3 className="text-white font-medium">API Layer</h3>
                  </div>
                  <p className="text-purple-200 text-sm">RESTful APIs, GraphQL, and microservices architecture</p>
                </motion.div>
                
                {/* Database Layer */}
                <motion.div 
                  whileHover={{ y: -5 }}
                  className="absolute top-60 left-12 right-12 h-24 bg-gradient-to-r from-gray-900/40 to-gray-800/40 rounded-b-xl border border-gray-700/30 p-4"
                >
                  <div className="flex items-center gap-3 mb-2">
                    <FaDatabase className="text-gray-400 text-xl" />
                    <h3 className="text-white font-medium">Database</h3>
                  </div>
                  <p className="text-gray-300 text-sm">SQL, NoSQL, and ORM integration with Python</p>
                </motion.div>
              </div>
              
              {/* Decorative elements */}
              <motion.div 
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="absolute -z-10 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full border-2 border-dashed border-green-500/20"
              />
              <motion.div 
                animate={{ rotate: -360 }}
                transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
                className="absolute -z-10 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 rounded-full border-2 border-dashed border-blue-500/10"
              />
            </motion.div>
          </div>
        </motion.div>
        
        {/* Stats Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6"
        >
          {[
            { value: "20+", label: "Python Projects" },
            { value: "5+", label: "Web Frameworks" },
            { value: "100+", label: "Coding Exercises" },
            { value: "24/7", label: "Learning Support" },
          ].map((stat, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-gradient-to-br from-gray-800/40 to-gray-900/40 border border-gray-700/30 rounded-xl p-4 text-center backdrop-blur-sm"
            >
              <div className="text-3xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent mb-1">
                {stat.value}
              </div>
              <div className="text-gray-400 text-sm">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default PythonFullStackHero;