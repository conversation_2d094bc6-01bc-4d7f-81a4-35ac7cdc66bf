import React, { useState } from "react";

const MLLiveClasses = ({ showPremiumOverlay }) => {
  const [activeTab, setActiveTab] = useState("schedule");

  const tabs = [
    { id: "schedule", label: "Class Schedule", icon: "fas fa-calendar-alt" },
    { id: "recordings", label: "Past Recordings", icon: "fas fa-play-circle" },
    { id: "workshops", label: "Special Workshops", icon: "fas fa-chalkboard-teacher" }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case "schedule":
        return <ClassSchedule showPremiumOverlay={showPremiumOverlay} />;
      case "recordings":
        return <PastRecordings showPremiumOverlay={showPremiumOverlay} />;
      case "workshops":
        return <SpecialWorkshops showPremiumOverlay={showPremiumOverlay} />;
      default:
        return <ClassSchedule showPremiumOverlay={showPremiumOverlay} />;
    }
  };

  // Animation styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .fade-in {
      animation: fadeIn 0.5s forwards;
    }
  `;

  return (
    <div className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md overflow-hidden text-white">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="p-6 bg-gradient-to-r from-[#1e293b]/20 to-[#0f172a]/20">
        <h2 className="text-3xl font-bold mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>ML Live Classes</h2>
        <p className="text-blue-100/80">Interactive machine learning sessions with expert instructors and hands-on coding</p>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-white/10 flex">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium transition-colors duration-200 relative ${
              activeTab === tab.id
                ? "text-purple-400 border-b-2 border-purple-400"
                : "text-white/70 hover:text-white"
            }`}
          >
            <i className={`${tab.icon} mr-2`}></i>
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Content */}
      <div className="p-6 fade-in">
        {renderContent()}
      </div>
    </div>
  );
};

// Class Schedule Component
const ClassSchedule = ({ showPremiumOverlay }) => {
  const upcomingClasses = [
    {
      title: "Introduction to Machine Learning",
      instructor: "Dr. Alex Chen",
      date: "July 16, 2025",
      time: "10:00 AM - 11:30 AM",
      level: "Beginner",
      registered: true
    },
    {
      title: "Supervised Learning Algorithms",
      instructor: "Prof. Sarah Williams",
      date: "July 18, 2025",
      time: "2:00 PM - 3:30 PM",
      level: "Intermediate",
      registered: false
    },
    {
      title: "Deep Learning with TensorFlow",
      instructor: "Dr. Michael Zhang",
      date: "July 20, 2025",
      time: "11:00 AM - 12:30 PM",
      level: "Advanced",
      registered: true
    },
    {
      title: "Computer Vision Applications",
      instructor: "Dr. Lisa Johnson",
      date: "July 22, 2025",
      time: "3:00 PM - 4:30 PM",
      level: "Advanced",
      registered: false
    }
  ];

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4 text-white">Upcoming ML Classes</h3>
      
      <div className="space-y-4">
        {upcomingClasses.map((classInfo, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10 hover:border-purple-400/50 transition-all duration-300">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div className="flex-1">
                <h4 className="text-lg font-semibold text-white mb-2">{classInfo.title}</h4>
                <div className="space-y-1 text-sm text-white/80">
                  <p className="flex items-center gap-2">
                    <span className="text-purple-400">👨‍🏫</span>
                    {classInfo.instructor}
                  </p>
                  <p className="flex items-center gap-2">
                    <span className="text-purple-400">📅</span>
                    {classInfo.date} at {classInfo.time}
                  </p>
                  <div className="flex items-center gap-2">
                    <span className="text-purple-400">📊</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      classInfo.level === "Beginner" ? "bg-green-500/20 text-green-300" :
                      classInfo.level === "Intermediate" ? "bg-yellow-500/20 text-yellow-300" :
                      "bg-red-500/20 text-red-300"
                    }`}>
                      {classInfo.level}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col gap-2">
                {classInfo.registered ? (
                  <button className="px-4 py-2 bg-green-600/20 text-green-300 border border-green-600/30 rounded-lg cursor-default">
                    ✓ Registered
                  </button>
                ) : (
                  <button
                    onClick={showPremiumOverlay}
                    className="px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300"
                  >
                    Register Now
                  </button>
                )}
                <button
                  onClick={showPremiumOverlay}
                  className="px-4 py-2 border border-white/20 text-white/80 rounded-lg hover:bg-white/10 transition-all duration-300 text-sm"
                >
                  View Details
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="text-center mt-8">
        <button
          onClick={showPremiumOverlay}
          className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <span className="mr-2">📅</span>
          View Full Schedule
          <span className="ml-2">→</span>
        </button>
      </div>
    </div>
  );
};

// Past Recordings Component
const PastRecordings = ({ showPremiumOverlay }) => {
  const recordings = [
    {
      title: "Linear Regression Deep Dive",
      duration: "1h 25m",
      views: "2.3k",
      rating: 4.8,
      thumbnail: "🧮"
    },
    {
      title: "Decision Trees and Random Forest",
      duration: "1h 45m",
      views: "1.8k",
      rating: 4.9,
      thumbnail: "🌳"
    },
    {
      title: "Neural Network Fundamentals",
      duration: "2h 10m",
      views: "3.1k",
      rating: 4.7,
      thumbnail: "🧠"
    },
    {
      title: "Clustering Algorithms",
      duration: "1h 30m",
      views: "1.5k",
      rating: 4.6,
      thumbnail: "🎯"
    }
  ];

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4 text-white">Past Class Recordings</h3>
      
      <div className="grid md:grid-cols-2 gap-6">
        {recordings.map((recording, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10 hover:border-purple-400/50 transition-all duration-300">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-lg flex items-center justify-center text-2xl">
                {recording.thumbnail}
              </div>
              <div className="flex-1">
                <h4 className="text-lg font-semibold text-white mb-2">{recording.title}</h4>
                <div className="space-y-1 text-sm text-white/80">
                  <p className="flex items-center gap-2">
                    <span className="text-purple-400">⏱️</span>
                    {recording.duration}
                  </p>
                  <p className="flex items-center gap-2">
                    <span className="text-purple-400">👀</span>
                    {recording.views} views
                  </p>
                  <p className="flex items-center gap-2">
                    <span className="text-purple-400">⭐</span>
                    {recording.rating}/5.0
                  </p>
                </div>
                <button
                  onClick={showPremiumOverlay}
                  className="mt-3 px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 text-sm w-full"
                >
                  Watch Recording
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="text-center mt-8">
        <button
          onClick={showPremiumOverlay}
          className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <span className="mr-2">🎥</span>
          Access All Recordings
          <span className="ml-2">→</span>
        </button>
      </div>
    </div>
  );
};

// Special Workshops Component
const SpecialWorkshops = ({ showPremiumOverlay }) => {
  const workshops = [
    {
      title: "MLOps Workshop",
      description: "Learn to deploy and maintain ML models in production",
      date: "July 25, 2025",
      instructor: "DevOps Expert",
      icon: "⚙️"
    },
    {
      title: "AI Ethics Seminar",
      description: "Understand the ethical implications of AI systems",
      date: "July 30, 2025",
      instructor: "Ethics Specialist",
      icon: "⚖️"
    },
    {
      title: "Industry Use Cases",
      description: "Real-world ML applications across industries",
      date: "August 5, 2025",
      instructor: "Industry Veteran",
      icon: "🏭"
    }
  ];

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4 text-white">Special Workshops</h3>
      
      <div className="space-y-4">
        {workshops.map((workshop, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10 hover:border-purple-400/50 transition-all duration-300">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-lg flex items-center justify-center text-xl">
                {workshop.icon}
              </div>
              <div className="flex-1">
                <h4 className="text-lg font-semibold text-white mb-2">{workshop.title}</h4>
                <p className="text-white/80 mb-3">{workshop.description}</p>
                <div className="flex items-center gap-4 text-sm text-white/70">
                  <span className="flex items-center gap-1">
                    <span className="text-purple-400">📅</span>
                    {workshop.date}
                  </span>
                  <span className="flex items-center gap-1">
                    <span className="text-purple-400">👨‍🏫</span>
                    {workshop.instructor}
                  </span>
                </div>
              </div>
              <button
                onClick={showPremiumOverlay}
                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 text-sm"
              >
                Register
              </button>
            </div>
          </div>
        ))}
      </div>
      
      <div className="text-center mt-8">
        <button
          onClick={showPremiumOverlay}
          className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <span className="mr-2">🎯</span>
          Join All Workshops
          <span className="ml-2">→</span>
        </button>
      </div>
    </div>
  );
};

export default MLLiveClasses;
