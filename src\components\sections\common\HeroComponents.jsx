import React from 'react';
import { motion } from 'framer-motion';

// Stats data
const statsData = [
  {
    number: "100+",
    label: "Interview Questions",
    icon: "🎯",
    gradient: "from-blue-500 to-blue-600"
  },
  {
    number: "250h+",
    label: "Training Content",
    icon: "⚡",
    gradient: "from-indigo-500 to-indigo-600"
  },
  {
    number: "10K+",
    label: "Active Learners",
    icon: "👨‍💻",
    gradient: "from-purple-500 to-purple-600"
  },
  {
    number: "95%",
    label: "Job Placement",
    icon: "🎯",
    gradient: "from-violet-500 to-violet-600"
  }
];

// Stats card component
const StatCard = ({ number, label, icon, gradient }) => (
  <motion.div
    initial={{ y: 20, opacity: 0 }}
    animate={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.5, delay: 0.5 }}
    className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:border-white/20 transition-colors"
  >
    <div className="text-3xl mb-2">{icon}</div>
    <div className={`text-3xl font-bold bg-gradient-to-r ${gradient} bg-clip-text text-transparent mb-2`}>
      {number}
    </div>
    <div className="text-gray-300 text-sm">{label}</div>
  </motion.div>
);

// Export components
export const HeroStats = ({ itemVariants }) => (
  <motion.div
    variants={itemVariants}
    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-16"
  >
    {statsData.map((stat) => (
      <StatCard key={stat.label} {...stat} />
    ))}
  </motion.div>
);

export const HeroTerminal = ({ itemVariants }) => (
  <motion.div
    initial={{ x: 30, opacity: 0 }}
    animate={{ x: 0, opacity: 1 }}
    transition={{ duration: 0.6, delay: 0.4 }}
    className="relative"
  >
    <div className="bg-gray-900/80 backdrop-blur-xl border border-gray-800/50 rounded-xl overflow-hidden shadow-[0_5px_30px_rgba(0,0,0,0.25)]">
      <div className="flex items-center gap-2 px-4 py-3 bg-gray-800/90 border-b border-gray-700/50">
        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
        <div className="flex-1 flex justify-center">
          <span className="text-xs font-medium text-gray-400">terminal@codexuslabs</span>
        </div>
      </div>
      <div className="p-6 font-mono text-sm">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.8 }}
        >
          <div className="flex items-start mb-2">
            <span className="text-green-400 mr-2">$</span>
            <span className="text-blue-300">kubectl create deployment nginx --image=nginx</span>
          </div>
          <div className="text-gray-400 ml-4 mb-3">deployment.apps/nginx created</div>
          <div className="flex items-start mb-2">
            <span className="text-green-400 mr-2">$</span>
            <span className="text-blue-300">kubectl expose deployment nginx --port=80</span>
          </div>
          <div className="text-gray-400 ml-4 mb-3">service/nginx exposed</div>
          <div className="flex items-start">
            <span className="text-green-400 mr-2">$</span>
            <span className="text-blue-300 animate-pulse">_</span>
          </div>
        </motion.div>
      </div>
    </div>
  </motion.div>
);

export const HeroTrustIndicators = ({ itemVariants }) => (
  <motion.div
    variants={itemVariants}
    className="flex flex-wrap justify-center gap-6 mt-10 opacity-80"
  >
    
  </motion.div>
);
