import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { Courses } from "../features";
import { HotCourses, RecommendedCourses } from "../ui";
import { GradientOrbs } from "./common/BackgroundEffects";
import { useState } from "react";
import axios from "axios";
import { useEffect } from "react";
import axiosInstance from "../../utils/axiosInstance";

const CourseSection = ({ itemVariants }) => {

  const [hotLabs,setHotLabs] = useState([])
  const [advancedLabs,setAdvancedLabs] = useState([])
  
    useEffect(() => {
    const fetchHotLabs = async () => {
      try {
        const { data } = await axiosInstance.get("/lab/hot-labs");
  
        if (data.success) {
          const labsWithExtras = data.labs.map((lab, index) => ({
            title: lab.name,
            image: lab.icon?.secure_url || "",
            rating: 4.5 + (index % 3) * 0.1,      
            students: `${1 + index}.k+`,        
          }));
  
          setHotLabs(labsWithExtras);
        }
      } catch (error) {
        const message = error?.response?.data?.message;
        console.error(message);
      }
    };

    const fetchAdvancedLabs = async () => {
      try {
        const { data } = await axiosInstance.get("/lab/advanced-labs");
  
        if (data.success) {
          const labsWithExtras = data.labs.map((lab) => ({
            title: lab.name,
            image: lab.icon?.secure_url || "",
            level: "Intermediate",      
            duration: `6 weeks`,        
          }));
  
          setAdvancedLabs(labsWithExtras)
        }
      } catch (error) {
        const message = error?.response?.data?.message;
        console.error(message);
      }
    };
  
    fetchHotLabs();
    fetchAdvancedLabs();
  }, []);

  console.log(hotLabs)
  
  
  return (
    <motion.div
      variants={itemVariants}
      className="py-16 px-6 relative overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <GradientOrbs />
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        <div className="courses-container text-white">
          <Courses />

          {/* Hot & Recommended Courses Grid */}
          <motion.section
            variants={itemVariants}
            className="container mx-auto px-4 py-12 max-w-3xl"
          >
            <div className="flex flex-col gap-8">
              <HotCourses
                hotLabs={hotLabs}
              />

              <RecommendedCourses
                advancedLabs={advancedLabs}
              />
            </div>
          </motion.section>
        </div>

        <div className="mt-12 text-center">
          <Link
            to="/courses"
            className="inline-block px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg transition-colors duration-300 shadow-[0_0_15px_rgba(59,130,246,0.3)]"
          >
            View All Courses
          </Link>
        </div>
      </div>
    </motion.div>
  );
};

export default CourseSection;
