import React, { useState } from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";

const DataScienceSidebar = ({ isSidebarOpen, toggleSidebar }) => {
  const { user } = useSelector((state) => state.auth);
  const [hoveredItem, setHoveredItem] = useState(null);

  const courses = [
    { name: "Python", path: "/pythoncourse", icon: "🐍", color: "from-green-500 to-blue-500" },
    { name: "Full Stack", path: "/fullstack-course", icon: "💻", color: "from-blue-500 to-purple-500" },
    { name: "Data Science", path: "/datascience-course", icon: "📊", color: "from-teal-500 to-blue-500" },
    { name: "AI Course", path: "/ai-course", icon: "🤖", color: "from-purple-500 to-pink-500" },
    { name: "Machine Learning", path: "/ml-course", icon: "🧠", color: "from-orange-500 to-red-500" },
  ];

  const interviewPrep = [
    { name: "System Design", path: "/sys_des_for_int", icon: "🏗️", color: "from-gray-500 to-blue-500" },
    { name: "DSA", path: "/data_strut", icon: "📈", color: "from-green-500 to-teal-500" },
    { name: "SQL 50", path: "/sql_50", icon: "🗄️", color: "from-yellow-500 to-orange-500" },
    { name: "Top Questions", path: "/gg_75", icon: "💯", color: "from-red-500 to-pink-500" },
    { name: "JavaScript", path: "/30_days_js", icon: "🟨", color: "from-yellow-400 to-yellow-600" },
  ];

  const containerVariants = {
    hidden: { opacity: 0, x: -100 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 },
  };

  return (
    <motion.div
      initial="hidden"
      animate={isSidebarOpen ? "visible" : "hidden"}
      variants={containerVariants}
      className={`h-screen bg-gradient-to-b from-[#1a3c50] via-[#0f2027] to-[#010509] backdrop-blur-xl border-r border-white/10 fixed left-0 top-[107px] z-20 transition-all duration-500 ease-out shadow-2xl ${
        isSidebarOpen ? 'translate-x-0 opacity-100' : 'translate-x-[-100%] opacity-0'
      }`}
      style={{ width: "320px" }}
    >
      {/* Custom Scrollbar Styles - Completely Hidden */}
      <style dangerouslySetInnerHTML={{ __html: `
        .hidden-scrollbar {
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* Internet Explorer 10+ */
        }
        .hidden-scrollbar::-webkit-scrollbar {
          display: none; /* WebKit browsers (Chrome, Safari, Edge) */
        }
        .glass-effect {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .gradient-text {
          background: linear-gradient(135deg, #14b8a6, #0891b2, #06b6d4);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      `}} />
      
      <div className="flex flex-col h-full hidden-scrollbar overflow-y-auto">{/* Scroll bar is now completely hidden */}
        {/* User Profile Section */}
        <motion.div 
          variants={itemVariants}
          className="p-6 border-b border-white/10"
        >
          <div className="flex items-center gap-4">
            <div className="relative">
              <motion.img
                whileHover={{ scale: 1.1 }}
                className="rounded-full w-14 h-14 border-2 border-teal-400/50 shadow-lg object-cover"
                src={user ? user?.image : "/images/user.png"}
                alt="User"
              />
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-teal-400 rounded-full border-2 border-[#1a3c50]" />
            </div>
            <div className="flex-1">
              <h6 className="font-bold text-white text-lg gradient-text">
                {user ? user?.name : "Data Explorer"}
              </h6>
              <p className="text-teal-300 text-sm font-medium flex items-center gap-2">
                <span className="w-2 h-2 bg-gradient-to-r from-green-400 to-teal-400 rounded-full animate-pulse"></span>
                Learning Data Science
              </p>
            </div>
          </div>
        </motion.div>

        {/* Navigation Content */}
        <div className="flex-1 p-4 space-y-6">
          {/* Home */}
          <motion.div variants={itemVariants}>
            <Link
              to="/"
              className={`flex items-center gap-4 p-4 rounded-xl transition-all duration-300 glass-effect hover:bg-white/10 group ${
                window.location.pathname === "/" ? "bg-gradient-to-r from-teal-500/20 to-blue-500/20 border-teal-400/30" : ""
              }`}
            >
              <div className="text-2xl group-hover:scale-110 transition-transform duration-200">🏠</div>
              <span className="font-semibold text-white group-hover:text-teal-300 transition-colors duration-200">Dashboard</span>
            </Link>
          </motion.div>

          {/* Courses Section */}
          <motion.div variants={itemVariants}>
            <h5 className="px-4 mb-4 text-sm font-bold uppercase tracking-wider gradient-text">
              Learning Paths
            </h5>
            <div className="space-y-2">
              {courses.map((course, index) => (
                <motion.div
                  key={course.path}
                  variants={itemVariants}
                  whileHover={{ x: 5 }}
                  onHoverStart={() => setHoveredItem(course.path)}
                  onHoverEnd={() => setHoveredItem(null)}
                >
                  <Link
                    to={course.path}
                    className={`flex items-center gap-4 p-3 rounded-xl transition-all duration-300 glass-effect hover:bg-white/10 group relative overflow-hidden ${
                      window.location.pathname === course.path ? "bg-gradient-to-r from-teal-500/20 to-blue-500/20 border-teal-400/30" : ""
                    }`}
                  >
                    {/* Animated background on hover */}
                    <div className={`absolute inset-0 bg-gradient-to-r ${course.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`} />
                    
                    <div className="text-2xl group-hover:scale-110 transition-transform duration-200 relative z-10">
                      {course.icon}
                    </div>
                    <span className={`font-medium relative z-10 transition-colors duration-300 ${
                      hoveredItem === course.path ? "text-teal-300" : "text-white"
                    }`}>
                      {course.name}
                    </span>
                    
                    {window.location.pathname === course.path && (
                      <div className="absolute right-3 w-2 h-2 bg-teal-400 rounded-full animate-pulse" />
                    )}
                  </Link>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Interview Prep */}
          <motion.div variants={itemVariants}>
            <h5 className="px-4 mb-4 text-sm font-bold uppercase tracking-wider gradient-text">
              Interview Prep
            </h5>
            <div className="space-y-2">
              {interviewPrep.map((item, index) => (
                <motion.div
                  key={item.path}
                  variants={itemVariants}
                  whileHover={{ x: 5 }}
                  onHoverStart={() => setHoveredItem(item.path)}
                  onHoverEnd={() => setHoveredItem(null)}
                >
                  <Link
                    to={item.path}
                    className={`flex items-center gap-4 p-3 rounded-xl transition-all duration-300 glass-effect hover:bg-white/10 group relative overflow-hidden ${
                      window.location.pathname === item.path ? "bg-gradient-to-r from-teal-500/20 to-blue-500/20 border-teal-400/30" : ""
                    }`}
                  >
                    {/* Animated background on hover */}
                    <div className={`absolute inset-0 bg-gradient-to-r ${item.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`} />
                    
                    <div className="text-2xl group-hover:scale-110 transition-transform duration-200 relative z-10">
                      {item.icon}
                    </div>
                    <span className={`font-medium relative z-10 transition-colors duration-300 ${
                      hoveredItem === item.path ? "text-teal-300" : "text-white"
                    }`}>
                      {item.name}
                    </span>
                    
                    {window.location.pathname === item.path && (
                      <div className="absolute right-3 w-2 h-2 bg-teal-400 rounded-full animate-pulse" />
                    )}
                  </Link>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Quick Actions */}
          <motion.div variants={itemVariants} className="pt-4 border-t border-white/10">
            <h5 className="px-4 mb-4 text-sm font-bold uppercase tracking-wider gradient-text">
              Quick Actions
            </h5>
            <div className="space-y-2">
              <motion.button
                whileHover={{ x: 5 }}
                className="w-full flex items-center gap-4 p-3 rounded-xl transition-all duration-300 glass-effect hover:bg-white/10 group text-left"
              >
                <div className="text-2xl group-hover:scale-110 transition-transform duration-200">⚡</div>
                <span className="font-medium text-white group-hover:text-teal-300 transition-colors duration-200">Start Lab</span>
              </motion.button>
              
              <motion.button
                whileHover={{ x: 5 }}
                className="w-full flex items-center gap-4 p-3 rounded-xl transition-all duration-300 glass-effect hover:bg-white/10 group text-left"
              >
                <div className="text-2xl group-hover:scale-110 transition-transform duration-200">📊</div>
                <span className="font-medium text-white group-hover:text-teal-300 transition-colors duration-200">Analytics</span>
              </motion.button>
            </div>
          </motion.div>
        </div>

        {/* Footer */}
        <motion.div 
          variants={itemVariants}
          className="p-4 border-t border-white/10 bg-gradient-to-r from-teal-600/10 to-blue-600/10"
        >
          <div className="text-center">
            <p className="text-white/60 text-xs mb-2">Upcoding</p>
            <div className="flex justify-center space-x-2">
              <div className="w-2 h-2 bg-teal-400 rounded-full animate-pulse" />
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }} />
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }} />
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default DataScienceSidebar;
