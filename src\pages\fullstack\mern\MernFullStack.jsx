import React from "react";
import { CourseResourcesSection } from "../../../components/ui";
import useSidebarState from "../../../hooks/useSidebarState";
import MernFullStackHero from "./components/MernFullStackHero";
import FullstackLayout from "../components/FullstackLayout";
import FullstackPremiumModal from "../components/FullstackPremiumModal";
import MernFullStackIntroduction from "./components/MernFullStackIntroduction";
import MernFullStackLabEnvironment from "./components/MernFullStackLabEnvironment";
import MernFullStackLiveClasses from "./components/MernFullStackLiveClasses";
import MernFullStackFAQS from "./components/MernFullStackFAQS";

const MernFullStack = () => {
  const courseConfig = {
    title: "MERN Full Stack Development",
    description: "Master MongoDB, Express.js, React, and Node.js",
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn MERN stack fundamentals",
        icon: "📚",
        component: MernFullStackIntroduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Practice Lab",
        description: "Build full stack applications with MERN",
        icon: "💻",
        component: MernFullStackLabEnvironment,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Projects",
        description: "Work on real-world MERN projects",
        icon: "🚀",
        component: MernFullStackLiveClasses,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to common questions",
        icon: "❓",
        component: MernFullStackFAQS,
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={MernFullStackHero}
      LayoutComponent={FullstackLayout}
      PremiumModalComponent={FullstackPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default MernFullStack;
