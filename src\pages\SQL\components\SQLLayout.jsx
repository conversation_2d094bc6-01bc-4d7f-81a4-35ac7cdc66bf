import React from 'react';
import { motion } from 'framer-motion';
import { Footer, CourseLayout } from '../../../components/layout';

const SQLLayout = ({ children, isSidebarOpen, toggleSidebar }) => {
  return (
    <CourseLayout 
      isSidebarOpen={isSidebarOpen} 
      toggleSidebar={toggleSidebar}
      title="SQL Master"
    >
      {/* Main Content */}
      <motion.main
        className="flex-1 relative z-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        {children}
      </motion.main>
      
      {/* Footer */}
      <Footer />
    </CourseLayout>
  );
};

export default SQLLayout;
