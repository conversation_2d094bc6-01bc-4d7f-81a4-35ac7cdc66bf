import { useState, useCallback, useEffect, useRef } from 'react';

export const useProcessManager = () => {
  const [theme, setTheme] = useState('dark');
  const [processes, setProcesses] = useState([]);
  const [logs, setLogs] = useState([]);
  const [systemStats, setSystemStats] = useState({
    cpu: [],
    memory: [],
    timestamp: []
  });
  const [selectedProcess, setSelectedProcess] = useState(null);
  const [showLogs, setShowLogs] = useState(false);
  
  const intervalRef = useRef(null);

  // Mock process data generator
  const generateMockProcesses = useCallback(() => {
    const processNames = [
      'node.js', 'chrome.exe', 'vscode.exe', 'npm.exe', 'git.exe',
      'python.exe', 'java.exe', 'docker.exe', 'nginx.exe', 'mysql.exe',
      'redis-server', 'mongodb.exe', 'webpack.exe', 'babel.exe', 'eslint.exe'
    ];

    const statuses = ['running', 'stopped', 'sleeping', 'zombie'];
    
    return Array.from({ length: 12 }, (_, index) => ({
      pid: 1000 + index,
      name: processNames[index] || `process-${index}`,
      status: index < 8 ? 'running' : statuses[Math.floor(Math.random() * statuses.length)],
      cpu: Math.random() * 100,
      memory: Math.random() * 2048, // MB
      uptime: Math.floor(Math.random() * 86400), // seconds
      startTime: new Date(Date.now() - Math.random() * 86400000).toISOString(),
      user: 'system',
      priority: Math.floor(Math.random() * 20) - 10
    }));
  }, []);

  // Initialize processes
  useEffect(() => {
    setProcesses(generateMockProcesses());
  }, [generateMockProcesses]);

  // Update system stats periodically
  useEffect(() => {
    const updateStats = () => {
      const now = new Date().toLocaleTimeString();
      const cpuUsage = Math.random() * 100;
      const memoryUsage = Math.random() * 100;

      setSystemStats(prev => ({
        cpu: [...prev.cpu.slice(-19), cpuUsage],
        memory: [...prev.memory.slice(-19), memoryUsage],
        timestamp: [...prev.timestamp.slice(-19), now]
      }));

      // Update process stats
      setProcesses(prev => prev.map(process => ({
        ...process,
        cpu: process.status === 'running' ? Math.random() * 100 : 0,
        memory: process.status === 'running' ? 
          process.memory + (Math.random() - 0.5) * 10 : process.memory,
        uptime: process.status === 'running' ? process.uptime + 1 : process.uptime
      })));
    };

    intervalRef.current = setInterval(updateStats, 2000);
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const toggleTheme = useCallback(() => {
    setTheme(prev => prev === 'dark' ? 'light' : 'dark');
  }, []);

  const addLog = useCallback((message, type = 'info') => {
    const newLog = {
      id: Date.now(),
      timestamp: new Date().toLocaleTimeString(),
      message,
      type,
      process: selectedProcess?.name || 'system'
    };
    setLogs(prev => [newLog, ...prev.slice(0, 99)]); // Keep last 100 logs
  }, [selectedProcess]);

  const startProcess = useCallback((pid) => {
    setProcesses(prev => prev.map(process => 
      process.pid === pid 
        ? { ...process, status: 'running', startTime: new Date().toISOString() }
        : process
    ));
    addLog(`Process ${pid} started`, 'success');
  }, [addLog]);

  const stopProcess = useCallback((pid) => {
    setProcesses(prev => prev.map(process => 
      process.pid === pid 
        ? { ...process, status: 'stopped', cpu: 0 }
        : process
    ));
    addLog(`Process ${pid} stopped`, 'warning');
  }, [addLog]);

  const restartProcess = useCallback((pid) => {
    setProcesses(prev => prev.map(process => 
      process.pid === pid 
        ? { 
            ...process, 
            status: 'running', 
            startTime: new Date().toISOString(),
            uptime: 0
          }
        : process
    ));
    addLog(`Process ${pid} restarted`, 'info');
  }, [addLog]);

  const killProcess = useCallback((pid) => {
    setProcesses(prev => prev.map(process => 
      process.pid === pid 
        ? { ...process, status: 'zombie', cpu: 0 }
        : process
    ));
    addLog(`Process ${pid} killed`, 'error');
  }, [addLog]);

  const clearLogs = useCallback(() => {
    setLogs([]);
    addLog('Logs cleared', 'info');
  }, [addLog]);

  const formatUptime = useCallback((seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  }, []);

  const formatMemory = useCallback((mb) => {
    if (mb >= 1024) {
      return `${(mb / 1024).toFixed(1)} GB`;
    }
    return `${mb.toFixed(1)} MB`;
  }, []);

  const getStatusColor = useCallback((status) => {
    switch (status) {
      case 'running': return 'text-green-500';
      case 'stopped': return 'text-yellow-500';
      case 'sleeping': return 'text-blue-500';
      case 'zombie': return 'text-red-500';
      default: return 'text-gray-500';
    }
  }, []);

  const getLogTypeColor = useCallback((type) => {
    switch (type) {
      case 'success': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'error': return 'text-red-400';
      case 'info': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  }, []);

  return {
    // State
    theme,
    processes,
    logs,
    systemStats,
    selectedProcess,
    showLogs,

    // Actions
    toggleTheme,
    startProcess,
    stopProcess,
    restartProcess,
    killProcess,
    setSelectedProcess,
    setShowLogs,
    clearLogs,

    // Utilities
    formatUptime,
    formatMemory,
    getStatusColor,
    getLogTypeColor
  };
};
