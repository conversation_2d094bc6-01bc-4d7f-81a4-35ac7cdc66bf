# Process Manager Documentation

## Overview
A fully functional, frontend-only Process Manager component built with React that simulates system process monitoring and management. The component integrates seamlessly with the existing code editor and DSA lab environment, maintaining consistent dark/light theme support.

## Features

### 🖥️ **Core Functionality**
- **Process Table**: Interactive table showing PID, name, status, CPU usage, memory usage, and uptime
- **Process Controls**: Start, Stop, Restart, and Kill actions for each process
- **Real-time Updates**: Live updating of process statistics every 2 seconds
- **Process Selection**: Click on any process to view detailed information

### 📊 **System Monitoring**
- **Live Charts**: CPU and Memory usage graphs using Recharts
- **System Stats Cards**: Current CPU, Memory, Active Processes, and System Load
- **Real-time Data**: Continuously updated metrics with 20-point history
- **Visual Indicators**: Progress bars and color-coded status indicators

### 📝 **Log Management**
- **Collapsible Log Viewer**: Expandable section showing process logs
- **Filtered Logs**: View logs for selected process or all system logs
- **Log Types**: Success, Warning, Error, and Info messages with icons
- **Auto-scroll**: Automatically scrolls to newest log entries
- **Log Clearing**: Clear all logs with one click

### 🎨 **User Interface**
- **Theme Support**: Consistent dark/light theme matching existing components
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Smooth Animations**: Framer Motion animations for enhanced UX
- **Navigation Integration**: Top navigation bar with links to all tools

## Technical Architecture

### 📁 **File Structure**
```
src/
├── hooks/
│   └── useProcessManager.js          # Main state management hook
├── components/
│   ├── layout/
│   │   └── TopNavigation.jsx         # Unified navigation component
│   └── features/
│       └── ProcessManager/
│           ├── ProcessManager.jsx    # Main component
│           ├── ProcessTable.jsx      # Process listing table
│           ├── SystemStats.jsx       # Charts and statistics
│           ├── LogViewer.jsx         # Log display component
│           └── index.js              # Export configuration
```

### 🔧 **Dependencies**
- **React**: Core framework with hooks
- **React Router**: Navigation between tools
- **Framer Motion**: Smooth animations and transitions
- **Recharts**: Charts for CPU/Memory visualization
- **Tailwind CSS**: Styling and responsive design

### 🎯 **Custom Hook: useProcessManager**
Centralized state management for:
- Process data and status updates
- System statistics and charts data
- Log management and filtering
- Theme state and utilities
- Process control actions

## Component Details

### ProcessManager.jsx
**Main container component that orchestrates all functionality**
- Renders navigation, stats, table, and logs
- Manages overall layout and responsive design
- Handles process selection and detail display
- Coordinates theme and state management

### ProcessTable.jsx
**Interactive table displaying process information**
- Sortable columns for PID, name, status, CPU, memory, uptime
- Action buttons for each process (Start/Stop/Restart/Kill)
- Visual status indicators with color coding
- CPU usage progress bars
- Click-to-select process functionality

### SystemStats.jsx
**Real-time system monitoring dashboard**
- Four stat cards showing current metrics
- Live updating line charts for CPU and Memory
- Responsive chart layout using Recharts
- Color-coded progress indicators
- Custom tooltips with detailed information

### LogViewer.jsx
**Collapsible log management interface**
- Expandable/collapsible log section
- Filtered view based on selected process
- Auto-scrolling to newest entries
- Log type icons and color coding
- Clear logs functionality

### TopNavigation.jsx
**Unified navigation bar for all tools**
- Links to Code Editor, DSA Lab, API Tester, Process Manager
- Active route highlighting with smooth transitions
- Theme toggle button
- Responsive design with mobile-friendly layout

## Integration Points

### 🔗 **Navigation Integration**
The Process Manager is accessible from:
- **Direct URL**: `/process-manager`
- **Code Editor**: Purple "Process Manager" button in header
- **MERN Lab**: Purple button in top menu bar
- **DSA Lab**: Purple buttons in overview and editor
- **Top Navigation**: Available on Process Manager page

### 🎨 **Theme Integration**
- Uses same theme state management pattern as other components
- Consistent color schemes and styling variables
- Theme toggle synchronization across components
- Dark/light mode support for all UI elements

### 📱 **Responsive Design**
- Mobile-first approach with Tailwind CSS
- Collapsible navigation on smaller screens
- Responsive charts and tables
- Touch-friendly buttons and interactions

## Mock Data and Simulation

### 🔄 **Process Simulation**
- **Mock Processes**: 12 simulated processes with realistic names
- **Dynamic Stats**: CPU and memory usage update every 2 seconds
- **Status Changes**: Processes respond to control actions
- **Realistic Data**: Process names include node.js, chrome.exe, vscode.exe, etc.

### 📈 **System Metrics**
- **CPU Usage**: Random values between 0-100%
- **Memory Usage**: Random values with realistic fluctuation
- **Uptime Tracking**: Incremental uptime for running processes
- **Historical Data**: 20-point history for chart visualization

### 📋 **Log Generation**
- **Action Logs**: Generated when processes are started/stopped/killed
- **System Logs**: General system messages and events
- **Timestamped Entries**: All logs include precise timestamps
- **Type Classification**: Success, Warning, Error, Info categories

## Usage Instructions

### 🚀 **Getting Started**
1. Navigate to `/process-manager` or click any "Process Manager" button
2. View the system stats dashboard at the top
3. Browse the process table to see running processes
4. Click on any process to view detailed information

### ⚙️ **Process Management**
- **Start Process**: Click green "Start" button (only for stopped processes)
- **Stop Process**: Click yellow "Stop" button (only for running processes)
- **Restart Process**: Click blue "Restart" button (only for running processes)
- **Kill Process**: Click red "Kill" button (sets process to zombie state)

### 📊 **Monitoring**
- **View Charts**: CPU and Memory usage charts update every 2 seconds
- **Check Stats**: Current system metrics displayed in stat cards
- **Process Details**: Click any table row to see detailed process information

### 📝 **Log Management**
- **View Logs**: Click the arrow to expand/collapse log viewer
- **Filter Logs**: Select a process to see only its logs
- **Clear Logs**: Click "Clear Logs" button to remove all entries
- **Auto-scroll**: Logs automatically scroll to show newest entries

## Customization Options

### 🎨 **Styling**
- Theme colors can be customized in Tailwind configuration
- Component styling uses CSS classes for easy modification
- Chart colors and styling configurable in SystemStats component

### 📊 **Data Configuration**
- Process names and count configurable in useProcessManager hook
- Update intervals adjustable (currently 2 seconds)
- Chart history length configurable (currently 20 points)

### 🔧 **Feature Extensions**
- Additional process properties can be added to mock data
- New chart types can be integrated using Recharts
- Custom log types and filtering options can be implemented

## Browser Compatibility
- **Modern Browsers**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **Mobile Support**: iOS Safari 12+, Chrome Mobile 60+
- **Features Used**: ES6+, CSS Grid, Flexbox, Fetch API

## Performance Considerations
- **Efficient Updates**: Only updates changed data to minimize re-renders
- **Memory Management**: Limits log history to prevent memory leaks
- **Chart Optimization**: Uses Recharts' built-in performance optimizations
- **Responsive Images**: No heavy images, uses emoji icons for lightweight UI

## Future Enhancements

### 🔮 **Potential Features**
- **Process Filtering**: Search and filter processes by name or status
- **Export Functionality**: Export process data and logs to CSV/JSON
- **Alert System**: Notifications for high CPU/memory usage
- **Process Grouping**: Group related processes together
- **Historical Analysis**: Longer-term trend analysis and reporting
- **Custom Dashboards**: User-configurable dashboard layouts
- **Real Backend Integration**: Connect to actual system APIs when available

### 🛠️ **Technical Improvements**
- **WebSocket Support**: Real-time updates from backend
- **State Persistence**: Save user preferences and settings
- **Advanced Charts**: More chart types and customization options
- **Accessibility**: Enhanced screen reader and keyboard navigation support
