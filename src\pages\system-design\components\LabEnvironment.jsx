import React, { useState } from "react";
import { EmbeddedCodeEditor } from "../../../components/ui"; // Using the simpler embedded version

const LabEnvironment = () => {
  const [selectedChallenge, setSelectedChallenge] = useState(null);
  const [code, setCode] = useState("");
  const [isRunning, setIsRunning] = useState(false);
  const [output, setOutput] = useState("");
  const [showOutput, setShowOutput] = useState(false);

  const challenges = [
    {
      id: "url-shortener",
      title: "URL Shortener API",
      difficulty: "Beginner",
      description: "Design and implement a basic API for a URL shortening service like TinyURL or bit.ly.",
      requirements: [
        "Create endpoint to shorten long URLs",
        "Create endpoint to redirect from short URLs",
        "Use efficient hashing algorithm",
        "Handle collisions appropriately"
      ],
      template: `// URL Shortener API Implementation
// Implement the following functions

function generateShortUrl(longUrl) {
  // TODO: Implement hash generation
  
}

function storeMappingInDatabase(shortUrl, longUrl) {
  // TODO: Store in database
  
}

function getLongUrlFromShort(shortUrl) {
  // TODO: Retrieve from database
  
}

// API Endpoints
app.post('/shorten', (req, res) => {
  // TODO: Implement shortening endpoint
  
});

app.get('/:shortUrl', (req, res) => {
  // TODO: Implement redirect endpoint
  
});`
    },
    {
      id: "rate-limiter",
      title: "Rate Limiter",
      difficulty: "Intermediate",
      description: "Design and implement a rate limiting system to prevent abuse of your API endpoints.",
      requirements: [
        "Implement token bucket algorithm",
        "Support different limits for different users",
        "Track requests per user/IP",
        "Return appropriate HTTP status codes"
      ],
      template: `// Rate Limiter Implementation
// Implement the following functions

class RateLimiter {
  constructor(maxRequests, timeWindow) {
    // TODO: Initialize rate limiter
    
  }
  
  allowRequest(userId) {
    // TODO: Check if request is allowed
    
  }
  
  resetQuota(userId) {
    // TODO: Reset quota for user
    
  }
}

// Middleware Implementation
function rateLimitMiddleware(req, res, next) {
  // TODO: Implement middleware
  
}`
    },
    {
      id: "cache",
      title: "Caching System",
      difficulty: "Intermediate",
      description: "Design an LRU (Least Recently Used) cache for a web application.",
      requirements: [
        "Implement get and put operations in O(1) time",
        "Automatically remove least recently used items when cache is full",
        "Support cache size configuration",
        "Handle cache invalidation"
      ],
      template: `// LRU Cache Implementation
class LRUCache {
  constructor(capacity) {
    // TODO: Initialize cache structure
    
  }
  
  get(key) {
    // TODO: Get item from cache and mark as recently used
    
  }
  
  put(key, value) {
    // TODO: Add or update item and remove LRU if needed
    
  }
  
  invalidate(key) {
    // TODO: Remove item from cache
    
  }
}`
    },
    {
      id: "load-balancer",
      title: "Load Balancer",
      difficulty: "Advanced",
      description: "Design a simple load balancing algorithm to distribute requests across multiple servers.",
      requirements: [
        "Implement round-robin algorithm",
        "Add weighted distribution based on server capacity",
        "Add health checking for servers",
        "Handle server addition/removal dynamically"
      ],
      template: `// Load Balancer Implementation
class LoadBalancer {
  constructor(servers) {
    // TODO: Initialize with array of server objects
    
  }
  
  nextServer() {
    // TODO: Return next server based on algorithm
    
  }
  
  markServerDown(serverId) {
    // TODO: Mark server as unavailable
    
  }
  
  markServerUp(serverId) {
    // TODO: Mark server as available
    
  }
  
  addServer(server) {
    // TODO: Add new server to pool
    
  }
  
  removeServer(serverId) {
    // TODO: Remove server from pool
    
  }
}`
    }
  ];

  const handleCodeChange = (newCode) => {
    setCode(newCode);
  };

  const handleSelectChallenge = (challenge) => {
    setSelectedChallenge(challenge);
    setCode(challenge.template);
    setOutput("");
    setShowOutput(false);
  };
  
  const handleRunCode = () => {
    if (isRunning) return;
    
    setIsRunning(true);
    setShowOutput(true); // Always show output
    
    // Simulate code execution with a delay
    setTimeout(() => {
      setIsRunning(false);
      // Generate simulated output based on challenge type
      const outputs = {
        "url-shortener": `// Execution successful!
// URL Shortener API Results

> Analyzing code structure...
> Validating implementation...
> Testing URL shortening function...

✅ generateShortUrl() - Functioning correctly
✅ storeMappingInDatabase() - Database operations simulated
✅ getLongUrlFromShort() - Retrieval successful

Performance metrics:
- Average shortening time: 12ms
- Storage efficiency: Good
- Collision handling: Implemented

Example shortened URL: http://short.url/a7Xp9`,

        "rate-limiter": `// Execution successful!
// Rate Limiter Results

> Analyzing implementation...
> Testing under load...

✅ Quota enforcement working
✅ User differentiation implemented
✅ Reset functionality operational

Performance under load:
- Requests handled: 10,000/sec
- False positives: 0.01%
- Memory usage: Efficient

Your implementation successfully blocked 98.7% of simulated attacks`,

        "cache": `// Execution successful!
// LRU Cache Results

> Testing cache operations...
> Validating LRU behavior...

✅ O(1) time complexity verified
✅ LRU eviction policy working correctly
✅ Cache hit ratio: 87.3%

Performance metrics:
- Cache hits: 872/1000
- Cache misses: 128/1000
- Memory usage: Optimal

Cache operations are working efficiently!`,

        "load-balancer": `// Execution successful!
// Load Balancer Results

> Testing distribution algorithm...
> Simulating server failures...
> Validating health checks...

✅ Round-robin implementation validated
✅ Weighted distribution working correctly
✅ Health check mechanism operational

Performance under load:
- Distribution evenness: 99.2%
- Failover time: 153ms
- Recovery detection: Working

Server pool is being managed effectively!`
      };
      
      // Set the appropriate output or fallback to a generic message
      setOutput(outputs[selectedChallenge.id] || "// Code execution completed successfully!");
    }, 1500);
  };
  
  const handleResetCode = () => {
    if (selectedChallenge) {
      setCode(selectedChallenge.template);
      setOutput("");
      setShowOutput(false);
    }
  };

  // CSS animation styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    @keyframes slideInRight {
      from { transform: translateX(20px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5); }
      70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
      100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
    }
    .fade-in {
      animation: fadeIn 0.5s forwards;
    }
    .slide-in {
      animation: slideInRight 0.5s forwards;
    }
    .pulse-animation {
      animation: pulse 2s infinite;
    }
    .code-container {
      transition: all 0.3s ease;
    }
    .editor-container {
      transition: all 0.5s ease-in-out;
    }
    .challenge-card {
      transition: all 0.2s ease;
    }
    .challenge-card:hover {
      transform: translateY(-2px);
    }
  `;

  return (
    <div className="bg-gray-800 rounded-xl shadow-md overflow-hidden border border-gray-700">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="p-6 bg-gradient-to-r from-green-900 to-blue-900">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-3xl font-bold text-white mb-2">Lab Environment</h2>
            <p className="text-gray-300">Practice system design concepts with interactive coding challenges</p>
          </div>
          <div className="hidden md:flex items-center space-x-2">
            <div className="px-3 py-2 bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full pulse-animation mr-2"></div>
                <span className="text-xs font-medium text-gray-600">Monaco Editor Ready</span>
              </div>
            </div>
            <div className="px-3 py-2 bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center">
                <i className="fas fa-save text-blue-500 mr-2"></i>
                <span className="text-xs font-medium text-gray-600">Auto-saving enabled</span>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-4 p-3 bg-blue-50 border-l-4 border-blue-500 rounded">
          <p className="text-sm text-blue-800">
            <i className="fas fa-info-circle mr-2"></i>
            Select a challenge from above and start coding. Use the side panel to run your code and see results in the terminal below.
          </p>
        </div>
      </div>

      <div className="flex flex-col h-[calc(100vh-250px)] min-h-[600px] sm:min-h-[700px]">
        {/* Challenges List - Now at the top */}
        <div className="w-full border-b border-gray-700 bg-gray-900 p-4">
          <h3 className="text-lg font-semibold mb-4 text-white">Challenges</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {challenges.map(challenge => (
              <div
                key={challenge.id}
                className={`p-3 rounded-lg cursor-pointer transition-all challenge-card ${
                  selectedChallenge && selectedChallenge.id === challenge.id
                    ? "bg-blue-900 border-l-4 border-blue-500 shadow-md"
                    : "bg-gray-700 hover:bg-gray-600 border border-gray-600"
                }`}
                onClick={(e) => {
                  e.preventDefault();
                  handleSelectChallenge(challenge);
                }}
              >
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-white">{challenge.title}</h4>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    challenge.difficulty === "Beginner" 
                      ? "bg-green-100 text-green-700" 
                      : challenge.difficulty === "Intermediate"
                        ? "bg-yellow-100 text-yellow-700"
                        : "bg-red-100 text-red-700"
                  }`}>
                    {challenge.difficulty}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Code Editor Area - Now below challenges */}
        <div className="flex-1 flex flex-col">
          {selectedChallenge ? (
            <div className="flex-1 flex flex-col h-full">
              <div className="flex h-[calc(100%-160px)]">
                {/* Main Editor Section */}
                <div className="flex-1 editor-container flex flex-col">
                  <div className="bg-gray-800 text-white py-2 px-4 text-xs flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="mr-2 font-medium">{selectedChallenge.title}</span>
                      <span className="px-2 py-1 bg-gray-700 rounded text-xs">{selectedChallenge.id}.js</span>
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                        selectedChallenge.difficulty === "Beginner" 
                          ? "bg-green-100 text-green-700" 
                          : selectedChallenge.difficulty === "Intermediate"
                            ? "bg-yellow-100 text-yellow-700"
                            : "bg-red-100 text-red-700"
                      }`}>
                        {selectedChallenge.difficulty}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                        <span className="text-xs text-gray-300">Auto-saved</span>
                      </div>
                    </div>
                  </div>
                  <div className="h-[calc(100%-32px)] flex-1 flex items-center justify-center">
                    <EmbeddedCodeEditor
                      language="javascript"
                      value={code}
                      onChange={handleCodeChange}
                      height="100%"
                      showSidebar={false}
                    />
                  </div>
                </div>
                
                {/* Side Control Panel */}
                <div className="w-48 border-l border-gray-300 bg-gray-50 flex flex-col">
                  <div className="p-3 border-b border-gray-200">
                    <h4 className="text-sm font-semibold mb-3 text-gray-700">Actions</h4>
                    <div className="flex flex-col space-y-2">
                      <button 
                        className={`w-full px-3 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:shadow-md transition-all text-xs relative ${isRunning ? 'pl-8' : ''} font-medium`}
                        onClick={handleRunCode}
                        disabled={isRunning}
                        title="Execute your code"
                      >
                        {isRunning ? (
                          <>
                            <span className="absolute left-2 top-1/2 transform -translate-y-1/2">
                              <div className="animate-spin h-3 w-3 border-2 border-white border-t-transparent rounded-full"></div>
                            </span>
                            Processing...
                          </>
                        ) : (
                          <div className="flex items-center justify-center">
                            <i className="fas fa-play mr-2"></i>Run Code
                          </div>
                        )}
                      </button>
                      
                      <button 
                        className="w-full px-3 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors text-xs flex items-center justify-center"
                        onClick={handleResetCode}
                        title="Reset code to original template"
                      >
                        <i className="fas fa-undo-alt mr-2"></i> Reset Code
                      </button>
                      
                      <button 
                        className="w-full px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-xs flex items-center justify-center"
                        title="View documentation for this challenge"
                      >
                        <i className="fas fa-book mr-2"></i> Docs
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-3">
                    <h4 className="text-sm font-semibold mb-2 text-gray-700">Challenge Info</h4>
                    <div className="text-xs text-gray-600">
                      <p><span className="font-medium">Difficulty:</span> {selectedChallenge.difficulty}</p>
                      <p className="mt-2"><span className="font-medium">Focus:</span> {
                        selectedChallenge.id === "url-shortener" 
                          ? "URL hashing & API design"
                          : selectedChallenge.id === "rate-limiter" 
                            ? "Request throttling"
                            : selectedChallenge.id === "cache" 
                              ? "Memory management"
                              : "Load distribution"
                      }</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Output Section - Always visible below */}
              <div className={`h-40 border-t border-gray-200 overflow-auto bg-gray-900 text-gray-100 ${showOutput ? 'slide-in' : ''}`}>
                <div className="bg-gray-800 py-2 px-4 text-xs flex justify-between items-center border-b border-gray-700">
                  <div className="flex items-center">
                    <i className="fas fa-terminal text-green-400 mr-2"></i>
                    <h4 className="text-sm font-semibold text-gray-300">Terminal Output</h4>
                    {showOutput && (
                      <span className="ml-3 px-2 py-1 bg-green-800 text-green-200 rounded-full text-xs">Success</span>
                    )}
                  </div>
                  <div>
                    {showOutput ? (
                      <span className="text-xs text-gray-400">
                        <i className="fas fa-check-circle mr-1"></i> Execution complete
                      </span>
                    ) : (
                      <span className="text-xs text-gray-400">
                        <i className="fas fa-info-circle mr-1"></i> Run code to see output
                      </span>
                    )}
                  </div>
                </div>
                <div className="p-4">
                  {showOutput ? (
                    <pre className="whitespace-pre-wrap font-mono text-sm text-green-400 leading-relaxed">{output}</pre>
                  ) : (
                    <div className="h-full flex items-center justify-center text-gray-500">
                      <i className="fas fa-terminal mr-2"></i> Run your code to see the results here
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-gray-50 p-8 text-center">
              <div>
                <i className="fas fa-code text-5xl text-gray-300 mb-4"></i>
                <h3 className="text-xl font-medium text-gray-500">Select a coding challenge above to begin</h3>
                <p className="mt-2 text-gray-400 max-w-md">Choose from URL shorteners, rate limiters, caching systems, and more to practice your system design skills.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LabEnvironment;
