import { useState, useCallback, useEffect } from "react";
import { problemsData } from "../data/problemsData";
import { languageTemplates } from "../data/languageTemplates";

export const useLeetCodeEditor = () => {
  // State management
  const [selectedProblem, setSelectedProblem] = useState(null);
  const [selectedLanguage, setSelectedLanguage] = useState('javascript');
  const [code, setCode] = useState('');
  const [customInput, setCustomInput] = useState('');
  const [output, setOutput] = useState('');
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [theme, setTheme] = useState('dark');
  const [activeTab, setActiveTab] = useState('testcases');

  // Static data
  const problems = problemsData;
  const languages = [
    { id: 'javascript', name: 'JavaScript', extension: 'js' },
    { id: 'python', name: 'Python', extension: 'py' },
    { id: 'java', name: 'Java', extension: 'java' },
    { id: 'cpp', name: 'C++', extension: 'cpp' }
  ];

  // Update code when language or problem changes
  useEffect(() => {
    if (selectedProblem && selectedLanguage) {
      const template = languageTemplates[selectedLanguage]?.[selectedProblem.id] || 
                     languageTemplates[selectedLanguage]?.default || '';
      setCode(template);
    }
  }, [selectedProblem, selectedLanguage]);

  // Initialize with first problem
  useEffect(() => {
    if (problems.length > 0 && !selectedProblem) {
      setSelectedProblem(problems[0]);
    }
  }, [problems, selectedProblem]);

  const toggleTheme = useCallback(() => {
    setTheme(prev => prev === 'dark' ? 'light' : 'dark');
  }, []);

  const runCode = useCallback(async () => {
    if (!selectedProblem) return;
    
    setIsRunning(true);
    setActiveTab('output');
    
    try {
      // Simulate code execution
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock execution result
      const mockOutput = generateMockOutput(selectedProblem, customInput, selectedLanguage);
      setOutput(mockOutput);
      
    } catch (error) {
      setOutput(`Error: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  }, [selectedProblem, customInput, selectedLanguage]);

  const submitCode = useCallback(async () => {
    if (!selectedProblem) return;
    
    setIsSubmitting(true);
    setActiveTab('output');
    
    try {
      // Simulate submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock test results
      const mockResults = generateMockTestResults(selectedProblem);
      setTestResults(mockResults);
      
      const passedTests = mockResults.filter(r => r.passed).length;
      const totalTests = mockResults.length;
      
      setOutput(`Submission Result:\n${passedTests}/${totalTests} test cases passed\n\n${
        passedTests === totalTests ? '✅ Accepted!' : '❌ Wrong Answer'
      }`);
      
    } catch (error) {
      setOutput(`Submission Error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  }, [selectedProblem]);

  const resetCode = useCallback(() => {
    if (selectedProblem && selectedLanguage) {
      const template = languageTemplates[selectedLanguage]?.[selectedProblem.id] || 
                     languageTemplates[selectedLanguage]?.default || '';
      setCode(template);
      setOutput('');
      setTestResults(null);
      setCustomInput('');
    }
  }, [selectedProblem, selectedLanguage]);

  return {
    // State
    selectedProblem,
    selectedLanguage,
    code,
    customInput,
    output,
    testResults,
    isRunning,
    isSubmitting,
    theme,
    activeTab,
    problems,
    languages,
    
    // Actions
    setSelectedProblem,
    setSelectedLanguage,
    setCode,
    setCustomInput,
    setActiveTab,
    toggleTheme,
    runCode,
    submitCode,
    resetCode
  };
};

// Helper functions
const generateMockOutput = (problem, input, language) => {
  const inputs = input.trim() || '[1,2,3,4,5]';
  
  switch (problem.id) {
    case 'two-sum':
      return `Input: ${inputs}\nOutput: [0, 1]\nExplanation: nums[0] + nums[1] = 2 + 7 = 9`;
    case 'reverse-integer':
      return `Input: 123\nOutput: 321`;
    case 'palindrome-number':
      return `Input: 121\nOutput: true\nExplanation: 121 reads as 121 from left to right and from right to left.`;
    case 'valid-parentheses':
      return `Input: "()"\nOutput: true`;
    case 'merge-sorted-arrays':
      return `Input: nums1 = [1,2,3,0,0,0], m = 3, nums2 = [2,5,6], n = 3\nOutput: [1,2,2,3,5,6]`;
    default:
      return `Code executed successfully!\nInput: ${inputs}\nOutput: [Sample output based on your implementation]`;
  }
};

const generateMockTestResults = (problem) => {
  const baseResults = [
    { input: '[2,7,11,15], target = 9', expected: '[0,1]', actual: '[0,1]', passed: true },
    { input: '[3,2,4], target = 6', expected: '[1,2]', actual: '[1,2]', passed: true },
    { input: '[3,3], target = 6', expected: '[0,1]', actual: '[0,1]', passed: true }
  ];
  
  // Randomly fail some tests to simulate real scenarios
  const results = baseResults.map((result, index) => ({
    ...result,
    passed: Math.random() > 0.2 // 80% pass rate
  }));
  
  return results;
};
