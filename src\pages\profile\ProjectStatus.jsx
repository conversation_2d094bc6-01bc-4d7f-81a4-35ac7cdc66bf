
import { useState } from "react";
import { FaLaptopCode, FaCheckCircle, FaRocket, FaFire } from "react-icons/fa";

// Import components
import StatsCard from "./components/StatsCard";
import NavigationTabs from "./components/NavigationTabs";
import ProjectCard from "./components/ProjectCard";
import EmptyState from "./components/EmptyState";
import QuickActions from "./components/QuickActions";

// Import utilities and data
import { calculateDaysRemaining, getProjectStats } from "./utils/projectUtils";
import { mockProjects } from "./data/mockProjects";

const ProjectStatus = () => {
  const [activeTab, setActiveTab] = useState("active");
  const [projects] = useState(mockProjects);
  const stats = getProjectStats(projects);

  const statsData = [
    {
      icon: FaLaptopCode,
      title: "Total Projects",
      value: stats.totalProjects,
      subtitle: "All time projects",
      gradientFrom: "from-purple-900/60",
      gradientTo: "to-slate-800/80",
      iconFrom: "from-purple-500",
      iconTo: "to-purple-600"
    },
    {
      icon: FaCheckCircle,
      title: "Completed",
      value: stats.completedProjects,
      subtitle: `${stats.completionRate}% completion rate`,
      gradientFrom: "from-green-900/60",
      gradientTo: "to-slate-800/80",
      iconFrom: "from-green-500",
      iconTo: "to-emerald-600"
    },
    {
      icon: FaRocket,
      title: "Active",
      value: stats.activeProjects,
      subtitle: "In development",
      gradientFrom: "from-blue-900/60",
      gradientTo: "to-slate-800/80",
      iconFrom: "from-blue-500",
      iconTo: "to-cyan-600"
    },
    {
      icon: FaFire,
      title: "Time Spent",
      value: `${stats.totalTimeSpent}h`,
      subtitle: "Total coding time",
      gradientFrom: "from-orange-900/60",
      gradientTo: "to-slate-800/80",
      iconFrom: "from-orange-500",
      iconTo: "to-amber-600"
    }
  ];

  return (
    <div className="min-h-screen w-full pt-4 pb-6 px-4 md:px-6 lg:px-8 overflow-y-auto">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-200 via-blue-200 to-cyan-200 bg-clip-text text-transparent mb-2">
            📊 Project Status Dashboard
          </h1>
          <p className="text-slate-400 text-lg">Track your development projects and progress</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statsData.map((stat, index) => (
            <StatsCard key={index} {...stat} />
          ))}
        </div>

        {/* Navigation Tabs */}
        <NavigationTabs
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          stats={stats}
        />

        {/* Projects Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {projects[activeTab].map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              calculateDaysRemaining={calculateDaysRemaining}
            />
          ))}
        </div>

        {/* Empty State */}
        {projects[activeTab].length === 0 && (
          <EmptyState activeTab={activeTab} />
        )}

        {/* Quick Actions */}
        <QuickActions />
      </div>
    </div>
  );
};

export default ProjectStatus;