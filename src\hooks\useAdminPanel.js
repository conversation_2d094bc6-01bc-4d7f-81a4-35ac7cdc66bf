import { useState, useCallback } from 'react';

export const useAdminPanel = () => {
  const [theme, setTheme] = useState('dark');
  const [activeSection, setActiveSection] = useState('user-logs');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleTheme = useCallback(() => {
    setTheme(prev => prev === 'dark' ? 'light' : 'dark');
  }, []);

  const toggleSidebar = useCallback(() => {
    setSidebarOpen(prev => !prev);
  }, []);

  const sections = [
    { id: 'user-logs', label: 'User Logs', icon: '📊' },
    { id: 'faq-manager', label: 'FAQ Manager', icon: '❓' },
    { id: 'intro-manager', label: 'Intro Section', icon: '📝' },
    { id: 'chapter-manager', label: 'Chapter Manager', icon: '📚' },
    { id: 'question-form', label: 'Questions', icon: '❔' },
    { id: 'explanation-form', label: 'Explanations', icon: '💡' },
    { id: 'payment-dashboard', label: 'Payments', icon: '💳' },
    { id: 'refund-tracker', label: 'Refunds', icon: '💰' },
    { id: 'ratings-display', label: 'Ratings', icon: '⭐' },
    { id: 'content-manager', label: 'Content Manager', icon: '📄' }
  ];

  return {
    theme,
    activeSection,
    sidebarOpen,
    sections,
    toggleTheme,
    setActiveSection,
    toggleSidebar
  };
};
