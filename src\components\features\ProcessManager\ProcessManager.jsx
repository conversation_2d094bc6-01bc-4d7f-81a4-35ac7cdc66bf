import React from 'react';
import { motion } from 'framer-motion';
import { useProcessManager } from '../../../hooks/useProcessManager';
import TopNavigation from '../../layout/TopNavigation';
import ProcessTable from './ProcessTable';
import SystemStats from './SystemStats';
import LogViewer from './LogViewer';

const ProcessManager = () => {
  const {
    theme,
    processes,
    logs,
    systemStats,
    selectedProcess,
    showLogs,
    toggleTheme,
    startProcess,
    stopProcess,
    restartProcess,
    killProcess,
    setSelectedProcess,
    setShowLogs,
    clearLogs,
    formatUptime,
    formatMemory,
    getStatusColor,
    getLogTypeColor
  } = useProcessManager();

  return (
    <div className={`min-h-screen ${
      theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      {/* Navigation */}
      <TopNavigation theme={theme} onToggleTheme={toggleTheme} />
      
      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-3xl font-bold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                Process Manager
              </h1>
              <p className={`mt-2 ${
                theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Monitor and manage system processes in real-time
              </p>
            </div>
            
            {/* Quick Stats */}
            <div className={`hidden md:flex items-center space-x-6 px-6 py-3 rounded-lg ${
              theme === 'dark' ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
            }`}>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  theme === 'dark' ? 'text-green-400' : 'text-green-600'
                }`}>
                  {processes.filter(p => p.status === 'running').length}
                </div>
                <div className={`text-xs ${
                  theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Running
                </div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600'
                }`}>
                  {processes.filter(p => p.status === 'stopped').length}
                </div>
                <div className={`text-xs ${
                  theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Stopped
                </div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  theme === 'dark' ? 'text-red-400' : 'text-red-600'
                }`}>
                  {processes.filter(p => p.status === 'zombie').length}
                </div>
                <div className={`text-xs ${
                  theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Zombie
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* System Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mb-8"
        >
          <SystemStats systemStats={systemStats} theme={theme} />
        </motion.div>

        {/* Process Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-8"
        >
          <ProcessTable
            processes={processes}
            theme={theme}
            onStart={startProcess}
            onStop={stopProcess}
            onRestart={restartProcess}
            onKill={killProcess}
            onSelectProcess={setSelectedProcess}
            formatUptime={formatUptime}
            formatMemory={formatMemory}
            getStatusColor={getStatusColor}
          />
        </motion.div>

        {/* Log Viewer */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <LogViewer
            logs={logs}
            theme={theme}
            showLogs={showLogs}
            onToggleLogs={() => setShowLogs(!showLogs)}
            onClearLogs={clearLogs}
            getLogTypeColor={getLogTypeColor}
            selectedProcess={selectedProcess}
          />
        </motion.div>

        {/* Selected Process Details */}
        {selectedProcess && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className={`mt-8 p-6 rounded-lg border ${
              theme === 'dark' 
                ? 'bg-gray-800 border-gray-700' 
                : 'bg-white border-gray-200'
            }`}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-semibold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                Process Details: {selectedProcess.name}
              </h3>
              <button
                onClick={() => setSelectedProcess(null)}
                className={`text-gray-400 hover:text-gray-600 transition-colors`}
              >
                ✕
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className={`block text-sm font-medium ${
                  theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Process ID
                </label>
                <p className={`mt-1 text-lg font-mono ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  {selectedProcess.pid}
                </p>
              </div>
              <div>
                <label className={`block text-sm font-medium ${
                  theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Status
                </label>
                <p className={`mt-1 text-lg font-semibold ${getStatusColor(selectedProcess.status)}`}>
                  {selectedProcess.status}
                </p>
              </div>
              <div>
                <label className={`block text-sm font-medium ${
                  theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Start Time
                </label>
                <p className={`mt-1 text-sm font-mono ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  {new Date(selectedProcess.startTime).toLocaleString()}
                </p>
              </div>
              <div>
                <label className={`block text-sm font-medium ${
                  theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Priority
                </label>
                <p className={`mt-1 text-lg font-mono ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  {selectedProcess.priority}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default ProcessManager;
