# API Tester Testing Guide

## Manual Testing Checklist

### 1. Navigation Testing
- [ ] Navigate to http://localhost:5173/code-editor/1/solve
- [ ] Verify "API Tester" button appears in the header (green button)
- [ ] Click "API Tester" button and verify navigation to /api-tester
- [ ] Verify direct navigation to http://localhost:5173/api-tester works

### 2. UI Components Testing
- [ ] Verify page loads with proper layout (Request panel on left, Response panel on right)
- [ ] Check theme toggle button works (sun/moon icon in top right)
- [ ] Verify all form elements are present:
  - HTTP method dropdown (GET, POST, PUT, DELETE, PATCH)
  - URL input field
  - Headers textarea
  - Request body textarea (appears for POST/PUT/PATCH)
  - Action buttons (Send, Save, Clear, Reset, History)

### 3. Theme Testing
- [ ] Toggle between dark and light themes
- [ ] Verify all components adapt to theme changes
- [ ] Check color consistency with main app theme

### 4. HTTP Method Testing
- [ ] Select GET method - verify request body section is hidden
- [ ] Select POST method - verify request body section appears
- [ ] Select PUT method - verify request body section appears
- [ ] Select PATCH method - verify request body section appears
- [ ] Select DELETE method - verify request body section is hidden

### 5. Quick Examples Testing
- [ ] Click "GET Post" button - verify URL and method are set correctly
- [ ] Click "POST Example" button - verify URL, method, and body are set
- [ ] Click "JSON Response" button - verify URL and method are set

### 6. API Request Testing

#### Test 1: Simple GET Request
1. Set method to GET
2. Enter URL: `https://jsonplaceholder.typicode.com/posts/1`
3. Click "Send GET" or press Ctrl+Enter
4. Verify response shows:
   - Status: 200 OK
   - Response time in milliseconds
   - Response headers
   - JSON response body with post data

#### Test 2: POST Request
1. Set method to POST
2. Enter URL: `https://jsonplaceholder.typicode.com/posts`
3. Set request body:
```json
{
  "title": "Test Post",
  "body": "This is a test post from API Tester",
  "userId": 1
}
```
4. Click "Send POST"
5. Verify response shows:
   - Status: 201 Created
   - Response with created post data including ID

#### Test 3: Error Handling
1. Enter invalid URL: `https://invalid-url-that-does-not-exist.com`
2. Click "Send GET"
3. Verify error message is displayed clearly

#### Test 4: JSON Validation
1. Set method to POST
2. Enter valid URL
3. Enter invalid JSON in headers: `{ invalid json }`
4. Click "Send POST"
5. Verify JSON validation error is shown

### 7. History Feature Testing
- [ ] Make a few different requests
- [ ] Click "Save" button after each request
- [ ] Click "History" button to show saved requests
- [ ] Click on a saved request to load it
- [ ] Verify request data is loaded correctly

### 8. Keyboard Shortcuts Testing
- [ ] Press Ctrl+Enter (or Cmd+Enter on Mac) to send request
- [ ] Press Ctrl+K to clear response
- [ ] Press Ctrl+R to reset form

### 9. Responsive Design Testing
- [ ] Resize browser window to mobile size
- [ ] Verify layout adapts properly
- [ ] Test on mobile device if available

### 10. Performance Testing
- [ ] Make multiple requests in succession
- [ ] Verify loading states work correctly
- [ ] Check for memory leaks (requests should complete properly)

## Expected Results

### Successful GET Request Response
```
Status: 200 OK • 150ms
Response Headers:
{
  "content-type": "application/json; charset=utf-8",
  "cache-control": "max-age=43200",
  ...
}

Response Body:
{
  "userId": 1,
  "id": 1,
  "title": "sunt aut facere repellat provident occaecati excepturi optio reprehenderit",
  "body": "quia et suscipit..."
}
```

### Error Response Example
```
❌ Error • 5000ms
Failed to fetch: Network error or CORS issue
```

## Common Issues and Solutions

### CORS Errors
- Expected for many APIs that don't support CORS
- Use CORS-enabled test APIs like JSONPlaceholder
- Error message should be clear and helpful

### Network Errors
- Check internet connection
- Verify URL is correct and accessible
- Some APIs may be down temporarily

### JSON Parse Errors
- Ensure headers and request body contain valid JSON
- Error messages should indicate the specific JSON issue

## Test APIs for Verification

### JSONPlaceholder (Recommended)
- Base URL: https://jsonplaceholder.typicode.com
- Endpoints: /posts, /users, /comments
- Supports GET, POST, PUT, DELETE
- CORS-enabled

### HTTPBin
- Base URL: https://httpbin.org
- Endpoints: /json, /post, /get, /status/200
- Great for testing different response types

### Public APIs
- Any CORS-enabled public API
- Check https://api.publicapis.org/entries for more options

## Success Criteria
- [ ] All navigation works correctly
- [ ] All HTTP methods function properly
- [ ] Responses are displayed correctly
- [ ] Error handling works as expected
- [ ] Theme switching works
- [ ] History feature saves and loads requests
- [ ] Keyboard shortcuts work
- [ ] UI is responsive and user-friendly
- [ ] No console errors or warnings
