import React, { useEffect, useState } from 'react';

const EmbeddedCodeEditor = ({ 
  language = 'javascript', 
  value, 
  onChange, 
  height = '400px'
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [lineNumbers, setLineNumbers] = useState([]);

  // Generate line numbers
  useEffect(() => {
    if (!value) return;
    const lines = value.split('\n').length;
    setLineNumbers(Array.from({ length: lines }, (_, i) => i + 1));
  }, [value]);

  // Simple syntax highlighting function (basic version)
  const highlightedValue = () => {
    // This is a very basic highlight - in a real app you'd want to use a library like Prism or highlight.js
    if (!value) return '';
    
    // Style comments
    const coloredValue = value
      .replace(/(\/\/.*$)/gm, '<span class="text-gray-400">$1</span>') // Single line comments
      .replace(/(\/\*[\s\S]*?\*\/)/g, '<span class="text-gray-400">$1</span>') // Multiline comments
      .replace(/(#.*$)/gm, '<span class="text-gray-400">$1</span>') // Python comments
      
      // Style strings
      .replace(/(['"`])(.*?)\1/g, '<span class="text-yellow-300">$1$2$1</span>')
      
      // Style keywords (JavaScript and Python)
      .replace(
        /\b(function|return|if|for|while|else|class|import|export|from|const|let|var|try|catch|async|await|def|in|as|with|is|not|and|or|True|False|None|import|from|pass)\b/g, 
        '<span class="text-purple-400">$1</span>'
      );
      
    return coloredValue;
  };

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div 
        className={`relative border ${isFocused ? 'border-blue-500' : 'border-gray-700'} rounded-md overflow-hidden transition-colors w-full mx-auto`} 
        style={{ height, maxHeight: "100%", maxWidth: "1200px" }}
      >
        {/* Line numbers */}
        <div className="absolute left-0 top-0 bottom-0 w-12 bg-gray-800 border-r border-gray-700 flex flex-col">
          {lineNumbers.map((num) => (
            <div 
              key={num} 
              className="text-xs text-gray-500 text-right pr-3 py-0.5 select-none"
              style={{ lineHeight: '1.5rem', paddingTop: '1px' }}
            >
              {num}
            </div>
          ))}
        </div>
        
        {/* Editor area */}
        <div className="flex justify-center w-full h-full">
          <textarea
            className="w-full h-full resize-none bg-gray-900 text-green-400 font-mono text-sm pt-4 pb-4 pl-16 pr-4 outline-none"
            style={{ 
              fontFamily: "'Fira Code', 'Monaco', 'Consolas', monospace",
              lineHeight: '1.5rem',
              tabSize: 2,
              overflowY: 'auto',
              maxWidth: "1200px"
            }}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            spellCheck="false"
          />
        </div>
        
        {/* Language indicator */}
        <div className="absolute top-2 right-2 text-xs bg-gray-800 text-gray-400 px-2 py-1 rounded">
          {language.toUpperCase()}
        </div>
      </div>
    </div>
  );
};

export default EmbeddedCodeEditor;
