const ethicsCaseStudies = [
  {
    title: "AI Bias Detection",
    objective: "Learn to identify and mitigate AI bias",
    scenario: "Analyze a machine learning model for potential biases",
    keyConcepts: ["Fairness Metrics", "Bias Detection", "Model Auditing", "Ethical AI"],
    solution: `# AI Bias Detection Example
import pandas as pd
from aif360.datasets import BinaryLabelDataset
from aif360.metrics import BinaryLabelDatasetMetric

def check_bias(dataset, protected_attribute):
    # Convert to AIF360 format
    aif_dataset = BinaryLabelDataset(
        df=dataset,
        label_names=['outcome'],
        protected_attribute_names=[protected_attribute]
    )
    
    # Calculate metrics
    metrics = BinaryLabelDatasetMetric(aif_dataset)
    
    # Get disparate impact
    di = metrics.disparate_impact()
    print(f"Disparate Impact: {di:.2f}")
    
    # Get statistical parity difference
    spd = metrics.statistical_parity_difference()
    print(f"Statistical Parity Difference: {spd:.2f}")
`
  }
];

export default ethicsCaseStudies;
