import React, { useState } from 'react';
import { <PERSON>, Filter, <PERSON>, <PERSON>Off, Trash2 } from 'lucide-react';

const RatingsDisplay = ({ theme }) => {
  const [ratings, setRatings] = useState([
    {
      id: 1,
      userId: 'user_001',
      userName: '<PERSON>',
      email: '<EMAIL>',
      course: 'React Fundamentals',
      rating: 5,
      review: 'Excellent course! The instructor explains concepts very clearly and the hands-on projects really helped me understand React better.',
      date: '2024-01-15',
      isVisible: true,
      isVerified: true
    },
    {
      id: 2,
      userId: 'user_002',
      userName: '<PERSON>',
      email: '<EMAIL>',
      course: 'JavaScript Advanced',
      rating: 4,
      review: 'Great content and well-structured lessons. Could use more advanced examples in some sections.',
      date: '2024-01-14',
      isVisible: true,
      isVerified: true
    },
    {
      id: 3,
      userId: 'user_003',
      userName: '<PERSON>',
      email: '<EMAIL>',
      course: 'Node.js Backend',
      rating: 5,
      review: 'Perfect for beginners and intermediate developers. The API building section was particularly helpful.',
      date: '2024-01-13',
      isVisible: true,
      isVerified: false
    },
    {
      id: 4,
      userId: 'user_004',
      userName: '<PERSON> Wilson',
      email: '<EMAIL>',
      course: 'Database Design',
      rating: 3,
      review: 'Good course but some topics could be explained in more detail. The examples were helpful though.',
      date: '2024-01-12',
      isVisible: false,
      isVerified: true
    },
    {
      id: 5,
      userId: 'user_005',
      userName: 'David Brown',
      email: '<EMAIL>',
      course: 'React Fundamentals',
      rating: 2,
      review: 'The course was okay but I expected more practical projects. Some sections felt rushed.',
      date: '2024-01-11',
      isVisible: false,
      isVerified: false
    }
  ]);

  const [filterRating, setFilterRating] = useState('');
  const [filterCourse, setFilterCourse] = useState('');
  const [showHidden, setShowHidden] = useState(false);

  const courses = [...new Set(ratings.map(r => r.course))];

  const toggleVisibility = (id) => {
    setRatings(prev => prev.map(rating => 
      rating.id === id 
        ? { ...rating, isVisible: !rating.isVisible }
        : rating
    ));
  };

  const deleteRating = (id) => {
    if (window.confirm('Are you sure you want to delete this rating?')) {
      setRatings(prev => prev.filter(rating => rating.id !== id));
    }
  };

  const filteredRatings = ratings.filter(rating => {
    const matchesRating = filterRating === '' || rating.rating.toString() === filterRating;
    const matchesCourse = filterCourse === '' || rating.course === filterCourse;
    const matchesVisibility = showHidden || rating.isVisible;
    
    return matchesRating && matchesCourse && matchesVisibility;
  });

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        size={16}
        className={`${
          index < rating 
            ? 'text-yellow-400 fill-current' 
            : theme === 'dark' ? 'text-gray-600' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getAverageRating = () => {
    const visibleRatings = ratings.filter(r => r.isVisible);
    if (visibleRatings.length === 0) return 0;
    return (visibleRatings.reduce((sum, r) => sum + r.rating, 0) / visibleRatings.length).toFixed(1);
  };

  const getRatingDistribution = () => {
    const visibleRatings = ratings.filter(r => r.isVisible);
    const distribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
    visibleRatings.forEach(r => distribution[r.rating]++);
    return distribution;
  };

  const distribution = getRatingDistribution();
  const totalVisibleRatings = ratings.filter(r => r.isVisible).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Ratings & Reviews
          </h2>
          <p className={`mt-1 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Monitor and manage user ratings and reviews
          </p>
        </div>
      </div>

      {/* Rating Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Average Rating */}
        <div className={`rounded-lg p-6 ${
          theme === 'dark' ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
        }`}>
          <div className="text-center">
            <div className={`text-4xl font-bold mb-2 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              {getAverageRating()}
            </div>
            <div className="flex justify-center mb-2">
              {renderStars(Math.round(getAverageRating()))}
            </div>
            <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              Average Rating ({totalVisibleRatings} reviews)
            </p>
          </div>
        </div>

        {/* Rating Distribution */}
        <div className={`rounded-lg p-6 lg:col-span-2 ${
          theme === 'dark' ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
        }`}>
          <h3 className={`text-lg font-semibold mb-4 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            Rating Distribution
          </h3>
          <div className="space-y-2">
            {[5, 4, 3, 2, 1].map(star => (
              <div key={star} className="flex items-center space-x-3">
                <div className="flex items-center space-x-1 w-12">
                  <span className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                    {star}
                  </span>
                  <Star size={14} className="text-yellow-400 fill-current" />
                </div>
                <div className={`flex-1 h-2 rounded-full ${
                  theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
                }`}>
                  <div
                    className="h-2 bg-yellow-400 rounded-full"
                    style={{
                      width: totalVisibleRatings > 0 
                        ? `${(distribution[star] / totalVisibleRatings) * 100}%` 
                        : '0%'
                    }}
                  />
                </div>
                <span className={`text-sm w-8 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                  {distribution[star]}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <select
            value={filterRating}
            onChange={(e) => setFilterRating(e.target.value)}
            className={`pl-10 pr-8 py-2 rounded-lg border ${
              theme === 'dark'
                ? 'bg-gray-800 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
          >
            <option value="">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="2">2 Stars</option>
            <option value="1">1 Star</option>
          </select>
        </div>

        <div className="relative">
          <select
            value={filterCourse}
            onChange={(e) => setFilterCourse(e.target.value)}
            className={`px-3 py-2 rounded-lg border ${
              theme === 'dark'
                ? 'bg-gray-800 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
          >
            <option value="">All Courses</option>
            {courses.map(course => (
              <option key={course} value={course}>{course}</option>
            ))}
          </select>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="showHidden"
            checked={showHidden}
            onChange={(e) => setShowHidden(e.target.checked)}
            className="mr-2"
          />
          <label htmlFor="showHidden" className={`text-sm ${
            theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
          }`}>
            Show hidden reviews
          </label>
        </div>
      </div>

      {/* Ratings List */}
      <div className="space-y-4">
        {filteredRatings.map((rating) => (
          <div
            key={rating.id}
            className={`rounded-lg border p-6 ${
              theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            } ${!rating.isVisible ? 'opacity-60' : ''}`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="flex items-center space-x-1">
                    {renderStars(rating.rating)}
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    theme === 'dark' ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-800'
                  }`}>
                    {rating.course}
                  </span>
                  {rating.isVerified && (
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      theme === 'dark' ? 'bg-green-900 text-green-300' : 'bg-green-100 text-green-800'
                    }`}>
                      Verified
                    </span>
                  )}
                  {!rating.isVisible && (
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      theme === 'dark' ? 'bg-red-900 text-red-300' : 'bg-red-100 text-red-800'
                    }`}>
                      Hidden
                    </span>
                  )}
                </div>

                <div className="mb-3">
                  <h4 className={`font-semibold ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {rating.userName}
                  </h4>
                  <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                    {rating.email} • {rating.date}
                  </p>
                </div>

                <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                  {rating.review}
                </p>
              </div>

              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => toggleVisibility(rating.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                      : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                  title={rating.isVisible ? 'Hide review' : 'Show review'}
                >
                  {rating.isVisible ? <Eye size={16} /> : <EyeOff size={16} />}
                </button>
                <button
                  onClick={() => deleteRating(rating.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                      : 'text-red-500 hover:bg-red-50 hover:text-red-700'
                  }`}
                  title="Delete review"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredRatings.length === 0 && (
        <div className={`text-center py-12 ${
          theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
        }`}>
          <p>No ratings found matching your filters.</p>
        </div>
      )}
    </div>
  );
};

export default RatingsDisplay;
