import React from "react";
import { motion } from "framer-motion";
import MernFullStackDropdown from "./MernFullStackDropdown";

const MernFullStackIntroduction = ({ onBackToCourse }) => {
  const mernFullStackTopics = {
    "Frontend Development (React)": [
      {
        title: "React Fundamentals",
        tags: ["Components", "JSX", "Props", "State"],
        keyPoints: [
          "Build interactive user interfaces with React",
          "Master component-based architecture"
        ]
      },
      {
        title: "React Hooks & State Management",
        tags: ["useState", "useEffect", "Context API", "Redux"],
        keyPoints: [
          "Manage application state effectively",
          "Implement advanced React patterns"
        ]
      },
      {
        title: "React Router & Navigation",
        tags: ["Routing", "Navigation", "Protected Routes"],
        keyPoints: [
          "Create single-page applications",
          "Implement client-side routing"
        ]
      }
    ],
    "Backend Development (Node.js & Express)": [
      {
        title: "Node.js Fundamentals",
        tags: ["Runtime", "Modules", "NPM", "Async Programming"],
        keyPoints: [
          "Build server-side applications with Node.js",
          "Handle asynchronous operations"
        ]
      },
      {
        title: "Express.js Framework",
        tags: ["Web Framework", "Middleware", "Routing", "REST APIs"],
        keyPoints: [
          "Create RESTful APIs with Express",
          "Implement middleware and error handling"
        ]
      },
      {
        title: "Authentication & Security",
        tags: ["JWT", "Bcrypt", "CORS", "Helmet"],
        keyPoints: [
          "Implement secure user authentication",
          "Apply security best practices"
        ]
      }
    ],
    "Database (MongoDB)": [
      {
        title: "MongoDB Fundamentals",
        tags: ["NoSQL", "Documents", "Collections", "CRUD"],
        keyPoints: [
          "Design and work with NoSQL databases",
          "Perform database operations"
        ]
      },
      {
        title: "Mongoose ODM",
        tags: ["Schema", "Models", "Validation", "Middleware"],
        keyPoints: [
          "Model data with Mongoose schemas",
          "Implement data validation and relationships"
        ]
      }
    ],
    "Full Stack Integration": [
      {
        title: "API Integration",
        tags: ["Axios", "Fetch", "Error Handling", "Loading States"],
        keyPoints: [
          "Connect frontend with backend APIs",
          "Handle API responses and errors"
        ]
      },
      {
        title: "Deployment & DevOps",
        tags: ["Heroku", "Netlify", "Environment Variables", "CI/CD"],
        keyPoints: [
          "Deploy MERN applications to production",
          "Set up continuous integration"
        ]
      }
    ]
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-900/70 to-blue-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">MERN Full Stack Development</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="mb-6">
            <h3 className="text-2xl font-bold text-white mb-4">
              Master the Complete MERN Stack
            </h3>
            <p className="text-gray-300 leading-relaxed">
              Learn to build modern, scalable web applications using MongoDB, Express.js, React, and Node.js. 
              This comprehensive course covers everything from frontend development with React to backend APIs 
              with Node.js and Express, plus database management with MongoDB.
            </p>
          </div>

          {/* Course Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-400">50+</div>
              <div className="text-sm text-gray-400">Projects</div>
            </div>
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-blue-400">200+</div>
              <div className="text-sm text-gray-400">Hours</div>
            </div>
            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-yellow-400">15+</div>
              <div className="text-sm text-gray-400">Technologies</div>
            </div>
            <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-purple-400">24/7</div>
              <div className="text-sm text-gray-400">Support</div>
            </div>
          </div>

          {/* Topics Dropdown */}
          <MernFullStackDropdown topics={mernFullStackTopics} />

          {/* Key Features */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-900/50 border border-gray-700/30 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-white mb-4">What You'll Build</h4>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                  Social Media Platform
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                  E-commerce Application
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                  Task Management System
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                  Real-time Chat App
                </li>
              </ul>
            </div>

            <div className="bg-gray-900/50 border border-gray-700/30 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-white mb-4">Skills You'll Gain</h4>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                  Full Stack Development
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                  RESTful API Design
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                  Database Management
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                  Modern JavaScript
                </li>
              </ul>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default MernFullStackIntroduction;
