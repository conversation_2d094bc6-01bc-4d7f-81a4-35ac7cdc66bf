import React from "react";
import { motion, AnimatePresence } from "framer-motion";

const DataSciencePremiumModal = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 50 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: { type: "spring", damping: 25, stiffness: 300 }
    },
    exit: { opacity: 0, scale: 0.8, y: 50 }
  };

  return (
    <AnimatePresence>
      <motion.div
        variants={backdropVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="bg-gradient-to-br from-[#1a3c50] to-[#010509] border border-white/20 rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative p-8 border-b border-white/10">
            <button
              onClick={onClose}
              className="absolute top-6 right-6 text-white/60 hover:text-white text-2xl transition-colors duration-200"
            >
              ✕
            </button>
            
            <div className="text-center">
              <div className="inline-block px-4 py-2 bg-gradient-to-r from-teal-600 to-blue-600 text-white rounded-full text-sm font-medium mb-4">
                🚀 Premium Access
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Unlock Your{" "}
                <span className="bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text text-transparent">
                  Data Science
                </span>{" "}
                Potential
              </h2>
              <p className="text-white/80 text-lg">
                Get unlimited access to advanced projects, datasets, and expert mentorship
              </p>
            </div>
          </div>

          {/* Content */}
          <div className="p-8">
            {/* Pricing Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {/* Monthly Plan */}
              <motion.div
                whileHover={{ y: -5, scale: 1.02 }}
                className="bg-white/5 border border-white/10 rounded-2xl p-6 backdrop-blur-lg"
              >
                <h3 className="text-xl font-bold text-white mb-2">Monthly Access</h3>
                <div className="text-3xl font-bold text-teal-400 mb-4">
                  $49<span className="text-lg text-white/60">/month</span>
                </div>
                <ul className="space-y-3 mb-6">
                  {[
                    "Complete data science curriculum",
                    "25+ hands-on projects",
                    "Live expert sessions",
                    "Premium datasets access",
                    "Community forum access",
                    "Basic career support"
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center text-white/80">
                      <span className="text-teal-400 mr-3">✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>
                <button className="w-full bg-gradient-to-r from-teal-600 to-blue-600 hover:from-teal-700 hover:to-blue-700 text-white py-3 rounded-xl font-bold transition-all duration-300">
                  Get Monthly Access
                </button>
              </motion.div>

              {/* Annual Plan */}
              <motion.div
                whileHover={{ y: -5, scale: 1.02 }}
                className="bg-gradient-to-br from-teal-600/10 to-blue-600/10 border-2 border-teal-400/30 rounded-2xl p-6 backdrop-blur-lg relative"
              >
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-teal-600 to-blue-600 text-white px-4 py-1 rounded-full text-sm font-bold">
                    BEST VALUE
                  </span>
                </div>
                
                <h3 className="text-xl font-bold text-white mb-2">Annual Access</h3>
                <div className="text-3xl font-bold text-teal-400 mb-1">
                  $399<span className="text-lg text-white/60">/year</span>
                </div>
                <p className="text-sm text-green-400 mb-4">Save $189 compared to monthly</p>
                
                <ul className="space-y-3 mb-6">
                  {[
                    "Everything in Monthly plan",
                    "1-on-1 mentorship sessions",
                    "Priority project reviews",
                    "Job placement assistance",
                    "Industry certifications",
                    "Lifetime access to updates",
                    "Advanced ML/AI modules"
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center text-white/80">
                      <span className="text-teal-400 mr-3">✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>
                <button className="w-full bg-gradient-to-r from-teal-600 to-blue-600 hover:from-teal-700 hover:to-blue-700 text-white py-3 rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl">
                  Get Annual Access
                </button>
              </motion.div>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {[
                {
                  icon: "📊",
                  title: "Real Datasets",
                  description: "Work with actual industry datasets from finance, healthcare, and tech companies"
                },
                {
                  icon: "🎯",
                  title: "Project Portfolio",
                  description: "Build 25+ projects that showcase your skills to potential employers"
                },
                {
                  icon: "👨‍🏫",
                  title: "Expert Mentorship",
                  description: "Get guidance from data scientists at Google, Netflix, and Spotify"
                },
                {
                  icon: "🏆",
                  title: "Certifications", 
                  description: "Earn industry-recognized certificates in Python, ML, and data analysis"
                },
                {
                  icon: "💼",
                  title: "Career Support",
                  description: "Resume reviews, interview prep, and job placement assistance"
                },
                {
                  icon: "🔄",
                  title: "Lifetime Updates",
                  description: "Stay current with new tools, techniques, and industry trends"
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05 }}
                  className="bg-white/5 border border-white/10 rounded-xl p-4 text-center backdrop-blur-lg"
                >
                  <div className="text-3xl mb-3">{feature.icon}</div>
                  <h4 className="font-bold text-white mb-2">{feature.title}</h4>
                  <p className="text-white/70 text-sm">{feature.description}</p>
                </motion.div>
              ))}
            </div>

            {/* Guarantee */}
            <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-6 text-center">
              <h4 className="text-lg font-bold text-green-400 mb-2">30-Day Money-Back Guarantee</h4>
              <p className="text-white/80">
                Not satisfied? Get a full refund within 30 days, no questions asked.
              </p>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default DataSciencePremiumModal;
