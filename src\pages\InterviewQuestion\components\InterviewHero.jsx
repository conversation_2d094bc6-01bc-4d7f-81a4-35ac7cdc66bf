
const InterviewHero = () => {
  // CSS animations defined in parent component
  const animationStyles = `
    @keyframes fadeInUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .hero-item {
      opacity: 0;
      animation: fadeInUp 0.5s forwards;
    }
    .delay-1 { animation-delay: 0.1s; }
    .delay-2 { animation-delay: 0.2s; }
    .delay-3 { animation-delay: 0.3s; }
    .delay-4 { animation-delay: 0.4s; }
  `;

  const stats = [
    { value: "60+", label: "Interview Questions" },
    { value: "10+", label: "Core Topics" },
    { value: "MNC", label: "Top Companies" },
    { value: "24/7", label: "Practice Access" },
  ];

  return (
    <div className="max-w-6xl mx-auto w-full py-20 px-6">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      <div className="grid md:grid-cols-2 gap-12 items-center">
        {/* Left Column - Text Content */}
        <div className="space-y-6 hero-item delay-1">
          <div className="flex items-center gap-2">
            <span className="bg-[#005249] bg-opacity-20 text-[#118c6e] px-4 py-2 rounded-full text-sm font-semibold">
              TOP INTERVIEWS
            </span>
          </div>

          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-white">
            Master the <br />
            <span className="text-yellow-300">Top 60 Interview</span> Questions
          </h1>

          <p className="text-white text-opacity-90 text-lg max-w-lg">
            Comprehensive guide to ace technical interviews with top multinational companies. 
            Learn essential interview techniques and practice with frequently asked questions.
          </p>

          <div className="flex flex-wrap gap-4">
            <a
              href="#interview-questions"
              className="bg-white text-[#118c6e] px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-2 hover:translate-y-[-2px]"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/>
                <path d="M10.97 4.97a.75.75 0 0 1 1.071 1.05l-3.992 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.235.235 0 0 1 .02-.022z"/>
              </svg>
              Start Practicing
            </a>
            <a
              href="/pythoncourse"
              className="bg-[#005249] bg-opacity-20 text-white border border-white border-opacity-30 px-6 py-3 rounded-lg font-semibold hover:bg-opacity-30 transition-all duration-200 flex items-center gap-2 hover:translate-y-[-2px]"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8.211 2.047a.5.5 0 0 0-.422 0l-7.5 3.5a.5.5 0 0 0 .025.917l7.5 3a.5.5 0 0 0 .372 0L14 7.14V13a1 1 0 0 0-1 1v2h3v-2a1 1 0 0 0-1-1V6.739l.686-.275a.5.5 0 0 0 .025-.917l-7.5-3.5ZM8 8.46 1.758 5.965 8 3.052l6.242 2.913L8 8.46Z"/>
                <path d="M4.176 9.032a.5.5 0 0 0-.656.327l-.5 1.7a.5.5 0 0 0 .294.605l4.5 1.8a.5.5 0 0 0 .372 0l4.5-1.8a.5.5 0 0 0 .294-.605l-.5-1.7a.5.5 0 0 0-.656-.327L8 10.466 4.176 9.032Zm-.068 1.873.22-.748 3.496 1.311a.5.5 0 0 0 .352 0l3.496-1.311.22.748L8 12.46l-3.892-1.556Z"/>
              </svg>
              Explore Courses
            </a>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8 bg-white bg-opacity-10 p-4 rounded-xl">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-yellow-300">{stat.value}</div>
                <div className="text-white text-opacity-80 text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Right Column - Image */}
        <div className="flex justify-center md:justify-end hero-item delay-2">
          <img
            src="/images/new-removebg-preview.png"
            alt="Interview Preparation"
            className="max-w-full rounded-lg hover:scale-[1.02] transition-transform duration-300"
            style={{ maxHeight: "400px" }}
          />
        </div>
      </div>
    </div>
  );
};

export default InterviewHero;
