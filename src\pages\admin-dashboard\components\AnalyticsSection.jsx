import React from 'react';
import { FaChartBar, FaChartLine, FaChartPie, FaUsers, FaBook, FaVideo } from 'react-icons/fa';

const AnalyticsSection = () => {
  // Mock analytics data
  const stats = [
    { id: 1, title: 'Total Students', value: '1,245', icon: <FaUsers className="text-blue-500" />, change: '+12%' },
    { id: 2, title: 'Total Courses', value: '32', icon: <FaBook className="text-green-500" />, change: '+5%' },
    { id: 3, title: 'Live Classes', value: '48', icon: <FaVideo className="text-purple-500" />, change: '+18%' },
    { id: 4, title: 'Completion Rate', value: '68%', icon: <FaChartPie className="text-orange-500" />, change: '+3%' },
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Analytics Dashboard</h2>
        <div className="flex space-x-2">
          <select className="border rounded-md px-3 py-1 text-sm">
            <option>Last 7 days</option>
            <option>Last 30 days</option>
            <option>Last 90 days</option>
            <option>All time</option>
          </select>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => (
          <div key={stat.id} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-gray-500 mb-1">{stat.title}</p>
                <h3 className="text-2xl font-bold text-gray-800">{stat.value}</h3>
                <span className="text-xs text-green-600">{stat.change} from last period</span>
              </div>
              <div className="p-3 bg-blue-50 rounded-full">
                {stat.icon}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Placeholder */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Enrollment Trends</h3>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <FaChartLine className="text-blue-300 text-5xl" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Course Popularity</h3>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <FaChartBar className="text-blue-300 text-5xl" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsSection;