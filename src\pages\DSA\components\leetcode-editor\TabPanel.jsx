import React from "react";
import { motion } from "framer-motion";

const TabPanel = ({
  activeTab,
  onTabChange,
  customInput,
  onCustomInputChange,
  output,
  testResults,
  theme,
  showPremiumOverlay
}) => {
  const tabs = [
    { id: 'testcases', label: 'Testcases', icon: '🧪' },
    { id: 'output', label: 'Output', icon: '📄' },
    { id: 'code', label: 'Code', icon: '💻' }
  ];

  const renderTestCases = () => (
    <div className="p-4 h-full overflow-y-auto">
      <div className="mb-4">
        <h3 className={`text-sm font-semibold mb-2 ${
          theme === 'dark' ? 'text-white' : 'text-gray-900'
        }`}>
          Custom Input
        </h3>
        <textarea
          value={customInput}
          onChange={(e) => onCustomInputChange(e.target.value)}
          placeholder="Enter your custom test input here..."
          className={`w-full h-24 p-3 rounded border font-mono text-sm resize-none ${
            theme === 'dark' 
              ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400' 
              : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
          }`}
        />
      </div>
      
      {/* Sample Test Cases */}
      <div className="mb-4">
        <h3 className={`text-sm font-semibold mb-2 ${
          theme === 'dark' ? 'text-white' : 'text-gray-900'
        }`}>
          Sample Test Cases
        </h3>
        <div className="space-y-2">
          {[1, 2, 3].map((num) => (
            <div
              key={num}
              className={`p-3 rounded border cursor-pointer transition-colors ${
                theme === 'dark' 
                  ? 'bg-gray-800 border-gray-600 hover:bg-gray-700' 
                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
              }`}
              onClick={() => onCustomInputChange(`[1,2,3,4,5]\n${num}`)}
            >
              <div className={`text-xs font-medium mb-1 ${
                theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Test Case {num}
              </div>
              <div className={`font-mono text-sm ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Input: [1,2,3,4,5], target = {num}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Premium Test Cases */}
      <div className={`p-3 rounded border-2 border-dashed ${
        theme === 'dark' 
          ? 'border-yellow-600/50 bg-yellow-900/20' 
          : 'border-yellow-400/50 bg-yellow-50'
      }`}>
        <div className="flex items-center mb-2">
          <span className="text-yellow-500 mr-2">⭐</span>
          <span className={`text-sm font-medium ${
            theme === 'dark' ? 'text-yellow-400' : 'text-yellow-700'
          }`}>
            Premium Test Cases
          </span>
        </div>
        <p className={`text-xs mb-2 ${
          theme === 'dark' ? 'text-yellow-300' : 'text-yellow-600'
        }`}>
          Access 50+ additional test cases including edge cases
        </p>
        <button
          onClick={showPremiumOverlay}
          className="px-3 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded text-xs font-medium hover:from-yellow-600 hover:to-orange-600 transition-all"
        >
          Unlock Premium
        </button>
      </div>
    </div>
  );

  const renderOutput = () => (
    <div className="p-4 h-full overflow-y-auto">
      {output ? (
        <div>
          <h3 className={`text-sm font-semibold mb-2 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            Execution Result
          </h3>
          <div className={`p-3 rounded border font-mono text-sm whitespace-pre-wrap ${
            theme === 'dark' 
              ? 'bg-gray-800 border-gray-600 text-green-400' 
              : 'bg-gray-50 border-gray-200 text-green-600'
          }`}>
            {output}
          </div>
        </div>
      ) : (
        <div className={`flex items-center justify-center h-full ${
          theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
        }`}>
          <div className="text-center">
            <div className="text-2xl mb-2">📄</div>
            <p>Run your code to see the output</p>
          </div>
        </div>
      )}

      {/* Test Results */}
      {testResults && (
        <div className="mt-4">
          <h3 className={`text-sm font-semibold mb-2 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            Test Results
          </h3>
          <div className="space-y-2">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-3 rounded border ${
                  result.passed
                    ? theme === 'dark' 
                      ? 'bg-green-900/30 border-green-700 text-green-400' 
                      : 'bg-green-50 border-green-200 text-green-700'
                    : theme === 'dark' 
                      ? 'bg-red-900/30 border-red-700 text-red-400' 
                      : 'bg-red-50 border-red-200 text-red-700'
                }`}
              >
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs font-medium">
                    Test Case {index + 1}
                  </span>
                  <span className={`text-xs px-2 py-1 rounded ${
                    result.passed 
                      ? 'bg-green-500 text-white' 
                      : 'bg-red-500 text-white'
                  }`}>
                    {result.passed ? 'PASS' : 'FAIL'}
                  </span>
                </div>
                <div className="font-mono text-xs">
                  <div>Input: {result.input}</div>
                  <div>Expected: {result.expected}</div>
                  <div>Output: {result.actual}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  const renderCode = () => (
    <div className="p-4 h-full overflow-y-auto">
      <div className={`flex items-center justify-center h-full ${
        theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
      }`}>
        <div className="text-center">
          <div className="text-2xl mb-2">💻</div>
          <p className="mb-2">Code analysis and suggestions</p>
          <div className={`p-3 rounded border-2 border-dashed ${
            theme === 'dark' 
              ? 'border-yellow-600/50 bg-yellow-900/20' 
              : 'border-yellow-400/50 bg-yellow-50'
          }`}>
            <div className="flex items-center mb-2">
              <span className="text-yellow-500 mr-2">⭐</span>
              <span className={`text-sm font-medium ${
                theme === 'dark' ? 'text-yellow-400' : 'text-yellow-700'
              }`}>
                Premium Feature
              </span>
            </div>
            <p className={`text-xs mb-2 ${
              theme === 'dark' ? 'text-yellow-300' : 'text-yellow-600'
            }`}>
              Get AI-powered code analysis, complexity insights, and optimization suggestions
            </p>
            <button
              onClick={showPremiumOverlay}
              className="px-3 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded text-xs font-medium hover:from-yellow-600 hover:to-orange-600 transition-all"
            >
              Upgrade Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      {/* Tab Headers */}
      <div className={`flex border-b ${
        theme === 'dark' ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
      }`}>
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`px-4 py-2 text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? theme === 'dark'
                  ? 'text-blue-400 border-b-2 border-blue-400 bg-gray-900'
                  : 'text-blue-600 border-b-2 border-blue-600 bg-white'
                : theme === 'dark'
                  ? 'text-gray-400 hover:text-gray-300'
                  : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
          className="h-full"
        >
          {activeTab === 'testcases' && renderTestCases()}
          {activeTab === 'output' && renderOutput()}
          {activeTab === 'code' && renderCode()}
        </motion.div>
      </div>
    </div>
  );
};

export default TabPanel;
