import { motion } from "framer-motion";
import { FaDatabase, FaTable, FaSearch, FaChartBar } from "react-icons/fa";

const SQLHero = ({ showPremiumOverlay }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <div className="relative overflow-hidden min-h-[80vh] flex items-center">
      {/* Background Elements */}
      <div className="absolute inset-0 z-0">
        {/* Animated gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a2e44] to-[#0d1b2a]"></div>
        
        {/* Animated SQL pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 right-0 bottom-0 text-xs md:text-sm overflow-hidden text-gray-400 font-mono">
            {Array.from({ length: 20 }).map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0.3, x: -10 }}
                animate={{ opacity: [0.1, 0.3, 0.1], x: 0 }}
                transition={{ duration: 10, repeat: Infinity, delay: i * 0.2 }}
                className="whitespace-nowrap"
                style={{ position: 'absolute', top: `${i * 5}%` }}
              >
                {`SELECT employee_name, department, salary FROM employees WHERE department = 'Engineering' ORDER BY salary DESC;`.repeat(2)}
              </motion.div>
            ))}
          </div>
        </div>
        
        {/* Glowing orbs */}
        <motion.div
          animate={{ scale: [1, 1.2, 1], opacity: [0.2, 0.3, 0.2] }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
          className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-blue-500/20 blur-3xl"
        />
        <motion.div
          animate={{ scale: [1, 1.3, 1], opacity: [0.1, 0.2, 0.1] }}
          transition={{ duration: 10, repeat: Infinity, ease: "easeInOut", delay: 1 }}
          className="absolute bottom-1/3 right-1/4 w-96 h-96 rounded-full bg-purple-500/20 blur-3xl"
        />
      </div>
      
      {/* Content */}
      <div className="container mx-auto px-4 py-16 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="flex flex-col lg:flex-row items-center gap-12"
        >
          {/* Left side - Text content */}
          <div className="lg:w-1/2 text-center lg:text-left">
            <motion.div variants={itemVariants} className="inline-block mb-2 px-3 py-1 bg-blue-900/30 border border-blue-700/30 rounded-full">
              <span className="text-blue-400 text-sm font-medium">Master SQL Queries</span>
            </motion.div>
            
            <motion.h1 variants={itemVariants} className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              <span className="text-white">SQL Mastery &</span>
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-500 bg-clip-text text-transparent">Interview Prep</span>
            </motion.h1>
            
            <motion.p variants={itemVariants} className="text-gray-300 text-lg mb-8 max-w-xl">
              Master SQL with our comprehensive course. From basic queries to advanced 
              database concepts, learn everything you need to excel in technical interviews 
              and real-world database management.
            </motion.p>
            
            <motion.div variants={itemVariants} className="flex flex-wrap gap-4 justify-center lg:justify-start">
              <button 
                onClick={showPremiumOverlay}
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-500/25"
              >
                Start Learning
              </button>
              <button className="px-6 py-3 border border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 font-medium rounded-lg transition-all duration-300 bg-blue-900/20 hover:bg-blue-900/30">
                View Curriculum
              </button>
            </motion.div>
          </div>
          
          {/* Right side - Visual elements */}
          <div className="lg:w-1/2">
            <motion.div 
              variants={itemVariants}
              className="relative"
            >
              {/* SQL Query Editor */}
              <div className="bg-gray-900/70 border border-gray-700/50 rounded-xl overflow-hidden shadow-xl">
                <div className="flex items-center gap-2 px-4 py-2 bg-gray-800/70 border-b border-gray-700/50">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="ml-2 text-xs text-gray-400">sql-query.sql</span>
                </div>
                <div className="p-5 font-mono text-sm">
                  <div className="text-purple-400">SELECT</div>
                  <div className="text-yellow-400 ml-4">e.employee_name,</div>
                  <div className="text-yellow-400 ml-4">d.department_name,</div>
                  <div className="text-yellow-400 ml-4">e.salary</div>
                  <div className="text-purple-400">FROM</div>
                  <div className="text-blue-400 ml-4">employees e</div>
                  <div className="text-purple-400">JOIN</div>
                  <div className="text-blue-400 ml-4">departments d</div>
                  <div className="text-white ml-4">ON e.department_id = d.id</div>
                  <div className="text-purple-400">WHERE</div>
                  <div className="text-green-400 ml-4">-- Filter by location</div>
                  <div className="text-yellow-400 ml-4">d.location = 'New York'</div>
                  <div className="text-purple-400">ORDER BY</div>
                  <div className="text-blue-400 ml-4">e.salary DESC;</div>
                </div>
              </div>
              
              {/* SQL Topics */}
              <div className="grid grid-cols-2 gap-3 mt-4">
                {[
                  { icon: <FaDatabase size={18} />, title: "Basic Queries", color: "from-blue-600/40 to-blue-800/40" },
                  { icon: <FaTable size={18} />, title: "Joins & Relations", color: "from-indigo-600/40 to-indigo-800/40" },
                  { icon: <FaChartBar size={18} />, title: "Aggregations", color: "from-purple-600/40 to-purple-800/40" },
                  { icon: <FaSearch size={18} />, title: "Advanced SQL", color: "from-violet-600/40 to-violet-800/40" }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ y: -3 }}
                    className={`p-3 rounded-lg bg-gradient-to-br ${item.color} border border-white/10 shadow-md backdrop-blur-sm`}
                  >
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-white/10 rounded-md">
                        {item.icon}
                      </div>
                      <h3 className="text-sm font-medium">{item.title}</h3>
                    </div>
                  </motion.div>
                ))}
              </div>
              
              {/* Decorative elements */}
              <motion.div 
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="absolute -z-10 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full border-2 border-dashed border-blue-500/20"
              />
              <motion.div 
                animate={{ rotate: -360 }}
                transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
                className="absolute -z-10 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 rounded-full border-2 border-dashed border-indigo-500/10"
              />
            </motion.div>
          </div>
        </motion.div>
        
        {/* Stats Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6"
        >
          {[
            { value: "50+", label: "SQL Questions" },
            { value: "15+", label: "Core Topics" },
            { value: "100+", label: "Practice Examples" },
            { value: "24/7", label: "Learning Support" },
          ].map((stat, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-gradient-to-br from-gray-800/40 to-gray-900/40 border border-gray-700/30 rounded-xl p-4 text-center backdrop-blur-sm"
            >
              <div className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent mb-1">
                {stat.value}
              </div>
              <div className="text-gray-400 text-sm">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default SQLHero;