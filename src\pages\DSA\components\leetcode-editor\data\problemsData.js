export const problemsData = [
  {
    id: 'two-sum',
    title: '1. Two Sum',
    difficulty: 'Easy',
    tags: ['Array', 'Hash Table'],
    description: `Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.

You may assume that each input would have exactly one solution, and you may not use the same element twice.

You can return the answer in any order.`,
    examples: [
      {
        input: 'nums = [2,7,11,15], target = 9',
        output: '[0,1]',
        explanation: 'Because nums[0] + nums[1] == 9, we return [0, 1].'
      },
      {
        input: 'nums = [3,2,4], target = 6',
        output: '[1,2]',
        explanation: 'Because nums[1] + nums[2] == 6, we return [1, 2].'
      },
      {
        input: 'nums = [3,3], target = 6',
        output: '[0,1]',
        explanation: 'Because nums[0] + nums[1] == 6, we return [0, 1].'
      }
    ],
    constraints: [
      '2 <= nums.length <= 10^4',
      '-10^9 <= nums[i] <= 10^9',
      '-10^9 <= target <= 10^9',
      'Only one valid answer exists.'
    ],
    hints: [
      'A really brute force way would be to search for all possible pairs of numbers but that would be too slow. Again, the best way to approach this problem is to think about the data structures that you can use to optimize the time complexity.',
      'So, if we fix one of the numbers, say x, we have to scan the entire array to find the next number y which is value - x where value is the input parameter. Can we change our array somehow so that this search becomes faster?',
      'The second train of thought is, without changing the array, can we use additional space somehow? Like maybe a hash map to speed up the search?'
    ]
  },
  {
    id: 'reverse-integer',
    title: '7. Reverse Integer',
    difficulty: 'Medium',
    tags: ['Math'],
    description: `Given a signed 32-bit integer x, return x with its digits reversed. If reversing x causes the value to go outside the signed 32-bit integer range [-2^31, 2^31 - 1], then return 0.

Assume the environment does not allow you to store 64-bit integers (signed or unsigned).`,
    examples: [
      {
        input: 'x = 123',
        output: '321',
        explanation: ''
      },
      {
        input: 'x = -123',
        output: '-321',
        explanation: ''
      },
      {
        input: 'x = 120',
        output: '21',
        explanation: ''
      }
    ],
    constraints: [
      '-2^31 <= x <= 2^31 - 1'
    ],
    hints: [
      'If overflow exists, the new result will be greater than 214748364 or smaller than -214748364'
    ]
  },
  {
    id: 'palindrome-number',
    title: '9. Palindrome Number',
    difficulty: 'Easy',
    tags: ['Math'],
    description: `Given an integer x, return true if x is palindrome integer.

An integer is a palindrome when it reads the same backward as forward.

For example, 121 is a palindrome while 123 is not.`,
    examples: [
      {
        input: 'x = 121',
        output: 'true',
        explanation: '121 reads as 121 from left to right and from right to left.'
      },
      {
        input: 'x = -121',
        output: 'false',
        explanation: 'From left to right, it reads -121. From right to left, it becomes 121-. Therefore it is not a palindrome.'
      },
      {
        input: 'x = 10',
        output: 'false',
        explanation: 'Reads 01 from right to left. Therefore it is not a palindrome.'
      }
    ],
    constraints: [
      '-2^31 <= x <= 2^31 - 1'
    ],
    hints: [
      'Beware of overflow when you reverse the integer.'
    ]
  },
  {
    id: 'valid-parentheses',
    title: '20. Valid Parentheses',
    difficulty: 'Easy',
    tags: ['String', 'Stack'],
    description: `Given a string s containing just the characters '(', ')', '{', '}', '[' and ']', determine if the input string is valid.

An input string is valid if:
1. Open brackets must be closed by the same type of brackets.
2. Open brackets must be closed in the correct order.
3. Every close bracket has a corresponding open bracket of the same type.`,
    examples: [
      {
        input: 's = "()"',
        output: 'true',
        explanation: ''
      },
      {
        input: 's = "()[]{}"',
        output: 'true',
        explanation: ''
      },
      {
        input: 's = "(]"',
        output: 'false',
        explanation: ''
      }
    ],
    constraints: [
      '1 <= s.length <= 10^4',
      's consists of parentheses only \'()[]{}\''
    ],
    hints: [
      'An interesting property about a valid parenthesis expression is that a sub-expression of a valid expression should also be a valid expression. (Not every sub-expression) e.g. { { } [ ] [ [ ] ] } is VALID expression { { } [ ] is VALID sub-expression { { } [ ] [ [ ] is NOT a valid sub-expression',
      'What if whenever we encounter a matching pair of parenthesis in the expression, we simply remove it from the expression? This would keep on shortening the expression. e.g. { { } [ ] [ [ ] ] } ==> { { } [ ] [ ] } ==> { { } [ ] } ==> { { } } ==> { } ==> VALID EXPRESSION',
      'The stack data structure can come in handy here in representing this recursive structure of the problem. We can\'t really process this from the inside out because we don\'t have an idea about the overall structure. But, the stack can help us process this recursively i.e. from outside to inwards.'
    ]
  },
  {
    id: 'merge-sorted-arrays',
    title: '88. Merge Sorted Array',
    difficulty: 'Easy',
    tags: ['Array', 'Two Pointers', 'Sorting'],
    description: `You are given two integer arrays nums1 and nums2, sorted in non-decreasing order, and two integers m and n, representing the number of elements in nums1 and nums2 respectively.

Merge nums1 and nums2 into a single array sorted in non-decreasing order.

The final sorted array should not be returned by the function, but instead be stored inside the array nums1. To accommodate this, nums1 has a length of m + n, where the first m elements denote the elements that should be merged, and the last n elements are set to 0 and should be ignored. nums2 has a length of n.`,
    examples: [
      {
        input: 'nums1 = [1,2,3,0,0,0], m = 3, nums2 = [2,5,6], n = 3',
        output: '[1,2,2,3,5,6]',
        explanation: 'The arrays we are merging are [1,2,3] and [2,5,6]. The result of the merge is [1,2,2,3,5,6] with the underlined elements coming from nums1.'
      },
      {
        input: 'nums1 = [1], m = 1, nums2 = [], n = 0',
        output: '[1]',
        explanation: 'The arrays we are merging are [1] and []. The result of the merge is [1].'
      },
      {
        input: 'nums1 = [0], m = 0, nums2 = [1], n = 1',
        output: '[1]',
        explanation: 'The arrays we are merging are [] and [1]. The result of the merge is [1]. Note that because m = 0, there are no elements in nums1. The 0 is only there to ensure the array has a length of 1.'
      }
    ],
    constraints: [
      'nums1.length == m + n',
      'nums2.length == n',
      '0 <= m, n <= 200',
      '1 <= m + n <= 200',
      '-10^9 <= nums1[i], nums2[j] <= 10^9'
    ],
    hints: [
      'You can easily solve this problem if you simply think about two elements at a time rather than two arrays. We know that each of the individual arrays is sorted. What we don\'t know is how they will intertwine. Can we take a local decision and arrive at an optimal solution?',
      'If you simply consider one element each at a time from the two arrays and make a decision and proceed accordingly, you will arrive at the optimal solution.'
    ]
  }
];
