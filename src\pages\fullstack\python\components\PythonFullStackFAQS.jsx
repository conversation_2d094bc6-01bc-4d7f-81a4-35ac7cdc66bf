import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaChevronDown } from "react-icons/fa";

const PythonFullStackFAQS = ({ onBackToCourse }) => {
  const [openFaq, setOpenFaq] = useState(null);
  
  const faqs = [
    {
      question: "What is Python Full Stack Development?",
      answer: "Python Full Stack Development involves using Python for both backend and frontend development. Typically, it includes using Python frameworks like Django or Flask for the backend, and integrating with frontend technologies like JavaScript frameworks (React, Vue, Angular) or template engines (Jinja2, Django Templates) to create complete web applications."
    },
    {
      question: "Which Python frameworks are best for full stack development?",
      answer: "The most popular Python frameworks for full stack development are Django and Flask. Django is a high-level framework with many built-in features like ORM, admin panel, and authentication. Flask is a lightweight micro-framework that gives you more flexibility but requires additional libraries for certain features. FastAPI is also gaining popularity for its high performance and modern features."
    },
    {
      question: "How does Python integrate with frontend technologies?",
      answer: "Python can integrate with frontend technologies in several ways: 1) Using template engines like Jinja2 or Django Templates to render HTML directly from Python, 2) Building RESTful APIs with Python that frontend JavaScript frameworks can consume, 3) Using Python to serve static files generated by frontend build tools, or 4) Implementing WebSockets for real-time communication between Python backend and JavaScript frontend."
    },
    {
      question: "What databases work well with Python full stack applications?",
      answer: "Python works well with virtually all databases. For relational databases, PostgreSQL, MySQL, and SQLite are commonly used with ORMs like SQLAlchemy or Django ORM. For NoSQL databases, MongoDB (with PyMongo or MongoEngine), Redis, and Elasticsearch are popular choices. The choice depends on your application's specific requirements for data structure, scalability, and performance."
    },
    {
      question: "How do I deploy a Python full stack application?",
      answer: "Common deployment options for Python full stack applications include: 1) Traditional hosting with services like Heroku, PythonAnywhere, or DigitalOcean, 2) Containerization with Docker and deployment to Kubernetes clusters, 3) Cloud platforms like AWS, Google Cloud, or Azure using their managed services, or 4) Serverless architectures using AWS Lambda or Google Cloud Functions for certain components."
    },
    {
      question: "What are the advantages of using Python for full stack development?",
      answer: "Advantages of Python for full stack development include: 1) Consistent language across the stack (with some JavaScript for frontend), 2) Rapid development with Python's clean syntax and powerful libraries, 3) Excellent framework options like Django and Flask, 4) Strong community support and extensive documentation, 5) Great for prototyping and MVPs, and 6) Seamless integration with data science and machine learning capabilities if needed."
    }
  ];

  const toggleFaq = (index) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-900/70 to-blue-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">Frequently Asked Questions</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Common Python Full Stack Questions</h3>
          <p className="text-gray-300">
            Find answers to frequently asked questions about Python full stack development, frameworks, 
            deployment strategies, and best practices.
          </p>
        </div>
        
        {/* FAQs */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              className="border border-gray-700/30 rounded-lg overflow-hidden"
            >
              <button
                onClick={() => toggleFaq(index)}
                className="w-full flex items-center justify-between p-4 text-left bg-gray-800/30 hover:bg-gray-700/40 transition-colors"
              >
                <h4 className="font-medium text-white">{faq.question}</h4>
                <motion.div
                  animate={{ rotate: openFaq === index ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <FaChevronDown className="text-gray-300" />
                </motion.div>
              </button>
              
              <AnimatePresence>
                {openFaq === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="p-4 bg-gray-800/20">
                      <p className="text-gray-300">{faq.answer}</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PythonFullStackFAQS;