import React from "react";
import { motion } from "framer-motion";

const CaseStudyHeader = ({ itemVariants }) => {
  return (
    <motion.div variants={itemVariants} className="text-center mb-16">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2, duration: 0.5 }}
        className="inline-block px-6 py-3 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg"
      >
        📚 Python Case Studies
      </motion.div>
      
      <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
        Practical{" "}
        <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
          Case Studies
        </span>
      </h2>
      
      <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
        Learn Python through real-world scenarios and hands-on practice
      </p>
    </motion.div>
  );
};

export default CaseStudyHeader;
