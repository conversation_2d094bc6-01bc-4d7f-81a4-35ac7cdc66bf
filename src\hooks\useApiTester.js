import { useState, useCallback } from 'react';

export const useApiTester = () => {
  // State management
  const [method, setMethod] = useState('GET');
  const [url, setUrl] = useState('');
  const [headers, setHeaders] = useState('{\n  "Content-Type": "application/json"\n}');
  const [requestBody, setRequestBody] = useState('{\n  \n}');
  const [response, setResponse] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [theme, setTheme] = useState('dark');

  // HTTP methods that support request body
  const methodsWithBody = ['POST', 'PUT', 'PATCH'];

  const toggleTheme = useCallback(() => {
    setTheme(prev => prev === 'dark' ? 'light' : 'dark');
  }, []);

  const validateUrl = (url) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const parseHeaders = (headersString) => {
    try {
      const parsed = JSON.parse(headersString);
      if (typeof parsed !== 'object' || Array.isArray(parsed)) {
        throw new Error('Headers must be a JSON object');
      }
      return parsed;
    } catch (error) {
      throw new Error(`Invalid headers JSON: ${error.message}`);
    }
  };

  const parseRequestBody = (bodyString) => {
    if (!bodyString.trim()) return null;
    
    try {
      return JSON.parse(bodyString);
    } catch (error) {
      throw new Error(`Invalid request body JSON: ${error.message}`);
    }
  };

  const sendRequest = useCallback(async () => {
    if (!url.trim()) {
      setResponse({
        error: 'URL is required',
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (!validateUrl(url)) {
      setResponse({
        error: 'Invalid URL format. Please include protocol (http:// or https://)',
        timestamp: new Date().toISOString()
      });
      return;
    }

    setIsLoading(true);
    setResponse(null); // Clear previous response
    const startTime = performance.now();

    try {
      // Parse headers with better error handling
      let parsedHeaders = {};
      try {
        if (headers.trim()) {
          parsedHeaders = parseHeaders(headers);
        }
      } catch (headerError) {
        setResponse({
          error: `Header parsing error: ${headerError.message}`,
          timestamp: new Date().toISOString()
        });
        setIsLoading(false);
        return;
      }

      // Prepare request options
      const requestOptions = {
        method,
        headers: {
          // Default headers
          'Accept': 'application/json, text/plain, */*',
          ...parsedHeaders
        },
        // Add CORS mode for better cross-origin support
        mode: 'cors',
        // Add credentials if needed
        credentials: 'omit'
      };

      // Add body for methods that support it
      if (methodsWithBody.includes(method)) {
        if (requestBody.trim()) {
          try {
            const parsedBody = parseRequestBody(requestBody);
            if (parsedBody !== null) {
              requestOptions.body = JSON.stringify(parsedBody);
              // Ensure Content-Type is set for JSON
              if (!requestOptions.headers['Content-Type'] && !requestOptions.headers['content-type']) {
                requestOptions.headers['Content-Type'] = 'application/json';
              }
            }
          } catch (bodyError) {
            setResponse({
              error: `Request body parsing error: ${bodyError.message}`,
              timestamp: new Date().toISOString()
            });
            setIsLoading(false);
            return;
          }
        } else {
          // For POST/PUT/PATCH without body, set empty object
          requestOptions.body = JSON.stringify({});
          if (!requestOptions.headers['Content-Type'] && !requestOptions.headers['content-type']) {
            requestOptions.headers['Content-Type'] = 'application/json';
          }
        }
      }

      console.log('Making request:', { url, options: requestOptions });

      // Make the request with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      requestOptions.signal = controller.signal;

      const response = await fetch(url, requestOptions);
      clearTimeout(timeoutId);

      const endTime = performance.now();
      const duration = Math.round(endTime - startTime);

      // Get response headers
      const responseHeaders = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      // Get response body with better error handling
      let responseBody = '';
      let bodyError = null;

      try {
        const contentType = response.headers.get('content-type') || '';
        const responseText = await response.text();

        if (responseText) {
          if (contentType.includes('application/json')) {
            try {
              responseBody = JSON.parse(responseText);
            } catch (jsonError) {
              // If JSON parsing fails, return as text
              responseBody = responseText;
              bodyError = `JSON parsing failed: ${jsonError.message}`;
            }
          } else {
            responseBody = responseText;
          }
        } else {
          responseBody = '(Empty response)';
        }
      } catch (bodyParseError) {
        responseBody = '(Failed to read response body)';
        bodyError = bodyParseError.message;
      }

      console.log('Response received:', {
        status: response.status,
        headers: responseHeaders,
        body: responseBody
      });

      setResponse({
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
        body: responseBody,
        bodyError,
        duration,
        timestamp: new Date().toISOString(),
        url: response.url,
        ok: response.ok,
        type: response.type,
        redirected: response.redirected
      });

    } catch (error) {
      const endTime = performance.now();
      const duration = Math.round(endTime - startTime);

      console.error('Request failed:', error);

      let errorMessage = error.message;
      let errorType = 'Unknown Error';

      // Provide more specific error messages
      if (error.name === 'AbortError') {
        errorMessage = 'Request timed out (30 seconds)';
        errorType = 'Timeout Error';
      } else if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error - check your internet connection or CORS policy';
        errorType = 'Network Error';
      } else if (error.name === 'TypeError' && error.message.includes('NetworkError')) {
        errorMessage = 'Network error - the server may be unreachable';
        errorType = 'Network Error';
      } else if (error.message.includes('CORS')) {
        errorMessage = 'CORS error - the server does not allow cross-origin requests from this domain';
        errorType = 'CORS Error';
      }

      setResponse({
        error: errorMessage,
        errorType,
        originalError: error.message,
        duration,
        timestamp: new Date().toISOString(),
        url
      });
    } finally {
      setIsLoading(false);
    }
  }, [method, url, headers, requestBody]);

  const clearResponse = useCallback(() => {
    setResponse(null);
  }, []);

  const resetForm = useCallback(() => {
    setMethod('GET');
    setUrl('');
    setHeaders('{\n  "Content-Type": "application/json"\n}');
    setRequestBody('{\n  \n}');
    setResponse(null);
  }, []);

  const saveRequest = useCallback(() => {
    const requestData = {
      method,
      url,
      headers,
      requestBody,
      timestamp: new Date().toISOString()
    };

    const savedRequests = JSON.parse(localStorage.getItem('apiTesterRequests') || '[]');
    savedRequests.unshift(requestData);

    // Keep only the last 10 requests
    if (savedRequests.length > 10) {
      savedRequests.splice(10);
    }

    localStorage.setItem('apiTesterRequests', JSON.stringify(savedRequests));
    return savedRequests;
  }, [method, url, headers, requestBody]);

  const loadRequest = useCallback((requestData) => {
    setMethod(requestData.method);
    setUrl(requestData.url);
    setHeaders(requestData.headers);
    setRequestBody(requestData.requestBody);
    setResponse(null);
  }, []);

  const getSavedRequests = useCallback(() => {
    return JSON.parse(localStorage.getItem('apiTesterRequests') || '[]');
  }, []);

  return {
    // State
    method,
    url,
    headers,
    requestBody,
    response,
    isLoading,
    theme,
    methodsWithBody,

    // Actions
    setMethod,
    setUrl,
    setHeaders,
    setRequestBody,
    sendRequest,
    clearResponse,
    resetForm,
    toggleTheme,
    saveRequest,
    loadRequest,
    getSavedRequests
  };
};
