import { useState, useCallback } from 'react';

export const useApiTester = () => {
  // State management
  const [method, setMethod] = useState('GET');
  const [url, setUrl] = useState('');
  const [headers, setHeaders] = useState('{\n  "Content-Type": "application/json"\n}');
  const [requestBody, setRequestBody] = useState('{\n  \n}');
  const [response, setResponse] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [theme, setTheme] = useState('dark');

  // HTTP methods that support request body
  const methodsWithBody = ['POST', 'PUT', 'PATCH'];

  const toggleTheme = useCallback(() => {
    setTheme(prev => prev === 'dark' ? 'light' : 'dark');
  }, []);

  const validateUrl = (url) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const parseHeaders = (headersString) => {
    try {
      const parsed = JSON.parse(headersString);
      if (typeof parsed !== 'object' || Array.isArray(parsed)) {
        throw new Error('Headers must be a JSON object');
      }
      return parsed;
    } catch (error) {
      throw new Error(`Invalid headers JSON: ${error.message}`);
    }
  };

  const parseRequestBody = (bodyString) => {
    if (!bodyString.trim()) return null;
    
    try {
      return JSON.parse(bodyString);
    } catch (error) {
      throw new Error(`Invalid request body JSON: ${error.message}`);
    }
  };

  const sendRequest = useCallback(async () => {
    if (!url.trim()) {
      setResponse({
        error: 'URL is required',
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (!validateUrl(url)) {
      setResponse({
        error: 'Invalid URL format',
        timestamp: new Date().toISOString()
      });
      return;
    }

    setIsLoading(true);
    const startTime = performance.now();

    try {
      // Parse headers
      let parsedHeaders = {};
      if (headers.trim()) {
        parsedHeaders = parseHeaders(headers);
      }

      // Prepare request options
      const requestOptions = {
        method,
        headers: parsedHeaders,
      };

      // Add body for methods that support it
      if (methodsWithBody.includes(method) && requestBody.trim()) {
        const parsedBody = parseRequestBody(requestBody);
        if (parsedBody !== null) {
          requestOptions.body = JSON.stringify(parsedBody);
        }
      }

      // Make the request
      const response = await fetch(url, requestOptions);
      const endTime = performance.now();
      const duration = Math.round(endTime - startTime);

      // Get response headers
      const responseHeaders = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      // Get response body
      let responseBody;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        try {
          responseBody = await response.json();
        } catch {
          responseBody = await response.text();
        }
      } else {
        responseBody = await response.text();
      }

      setResponse({
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
        body: responseBody,
        duration,
        timestamp: new Date().toISOString(),
        url: response.url,
        ok: response.ok
      });

    } catch (error) {
      const endTime = performance.now();
      const duration = Math.round(endTime - startTime);

      setResponse({
        error: error.message,
        duration,
        timestamp: new Date().toISOString(),
        url
      });
    } finally {
      setIsLoading(false);
    }
  }, [method, url, headers, requestBody]);

  const clearResponse = useCallback(() => {
    setResponse(null);
  }, []);

  const resetForm = useCallback(() => {
    setMethod('GET');
    setUrl('');
    setHeaders('{\n  "Content-Type": "application/json"\n}');
    setRequestBody('{\n  \n}');
    setResponse(null);
  }, []);

  const saveRequest = useCallback(() => {
    const requestData = {
      method,
      url,
      headers,
      requestBody,
      timestamp: new Date().toISOString()
    };

    const savedRequests = JSON.parse(localStorage.getItem('apiTesterRequests') || '[]');
    savedRequests.unshift(requestData);

    // Keep only the last 10 requests
    if (savedRequests.length > 10) {
      savedRequests.splice(10);
    }

    localStorage.setItem('apiTesterRequests', JSON.stringify(savedRequests));
    return savedRequests;
  }, [method, url, headers, requestBody]);

  const loadRequest = useCallback((requestData) => {
    setMethod(requestData.method);
    setUrl(requestData.url);
    setHeaders(requestData.headers);
    setRequestBody(requestData.requestBody);
    setResponse(null);
  }, []);

  const getSavedRequests = useCallback(() => {
    return JSON.parse(localStorage.getItem('apiTesterRequests') || '[]');
  }, []);

  return {
    // State
    method,
    url,
    headers,
    requestBody,
    response,
    isLoading,
    theme,
    methodsWithBody,

    // Actions
    setMethod,
    setUrl,
    setHeaders,
    setRequestBody,
    sendRequest,
    clearResponse,
    resetForm,
    toggleTheme,
    saveRequest,
    loadRequest,
    getSavedRequests
  };
};
