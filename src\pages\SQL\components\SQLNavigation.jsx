import React from 'react';
import { motion } from 'framer-motion';

const SQLNavigation = () => {
  const navItems = [
    {
      title: "SQL Projects",
      description: "Build real-world SQL projects and applications",
      icon: "fas fa-folder",
      color: "from-blue-400 to-cyan-500",
      badge: "Practical",
      badgeColor: "bg-blue-500"
    },
    {
      title: "SQL Topics Questions",
      description: "Comprehensive interview questions by topics",
      icon: "fas fa-book",
      color: "from-purple-400 to-pink-500",
      badge: "Essential",
      badgeColor: "bg-purple-500"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const cardVariants = {
    hidden: { y: 60, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div 
      className="py-16"
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
    >
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {navItems.map((item, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
              className="group cursor-pointer"
            >
              <div className="relative h-full bg-white rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 transform hover:-translate-y-1">
                <div
                  className={`absolute top-4 right-4 ${item.badgeColor} text-white px-3 py-1 rounded-full text-xs font-bold z-10`}
                >
                  {item.badge}
                </div>

                <div className={`h-32 bg-gradient-to-r ${item.color} p-6 flex items-center justify-center`}>
                  <i className={`${item.icon} text-4xl text-white`}></i>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                    {item.title}
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {item.description}
                  </p>
                  
                  <div className="flex items-center text-blue-600 text-sm font-medium">
                    Explore Now
                    <svg className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default SQLNavigation;
