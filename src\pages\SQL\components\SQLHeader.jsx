import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const SQLHeader = ({ toggleSidebar }) => {
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setScrolled(scrollTop > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <motion.nav 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        scrolled 
          ? 'bg-white shadow-lg backdrop-blur-sm' 
          : 'bg-transparent'
      }`}
    >
      <div className="max-w-full px-6 py-4 flex items-center justify-between">
        {/* Sidebar & Logo */}
        <div className="flex items-center gap-4">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleSidebar}
            className={`p-3 rounded-xl transition-all duration-300 ${
              scrolled 
                ? 'bg-gray-100 hover:bg-gray-200' 
                : 'bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20'
            }`}
          >
            <svg
              className={`w-5 h-5 transition-colors duration-300 ${
                scrolled ? 'text-gray-600' : 'text-white'
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </motion.button>
          <Link to="/" className="flex items-center gap-3">
            <motion.img 
              src="/images/logonew.png" 
              alt="Logo" 
              className="w-10 h-10"
              whileHover={{ 
                scale: 1.1,
                rotate: [0, 5, -5, 0]
              }}
              transition={{ duration: 0.3 }}
            />
            <span className={`font-bold text-xl transition-colors duration-300 ${
              scrolled ? 'text-blue-600' : 'text-white'
            }`}>
              UPCODING
            </span>
          </Link>
        </div>

        {/* SQL Page Title */}
        <div className="hidden md:block">
          <span className={`font-semibold text-lg transition-colors duration-300 ${
            scrolled ? 'text-gray-700' : 'text-white/90'
          }`}>
            SQL Interview Mastery
          </span>
        </div>

        {/* Navigation Links */}
        <div className="flex items-center gap-4">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              to="/"
              className={`px-4 py-2 rounded-xl transition-all duration-300 text-sm font-medium ${
                scrolled 
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg' 
                  : 'bg-white/10 border border-white/20 text-white hover:bg-white/20 backdrop-blur-sm'
              }`}
            >
              ← Back to Home
            </Link>
          </motion.div>
        </div>
      </div>
    </motion.nav>
  );
};

export default SQLHeader;
