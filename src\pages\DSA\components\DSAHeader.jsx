import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import toast from "react-hot-toast";
import { useSelector, useDispatch } from "react-redux";
import { userLoggedIn, userLoggedOut } from "../../../features/authSlice";
import axiosInstance from "../../../utils/axiosInstance";

const DSAHeader = ({ toggleSidebar }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isRegisterOpen, setIsRegisterOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { data } = await axiosInstance.get(
          "http://localhost:8000/api/v1/auth/me",
          {
            withCredentials: true,
          }
        );
        dispatch(
          userLoggedIn({ accessToken: data.accessToken, user: data.user })
        );
      } catch (error) {
        console.error(
          "Error fetching user:",
          error.response?.data || error.message
        );
      }
    };

    // Scroll detection for navbar appearance
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setScrolled(scrollTop > 50);
    };

    window.addEventListener('scroll', handleScroll);
    fetchUser();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [dispatch]);

  const handleLogout = async () => {
    try {
      const { data } = await axiosInstance.post(
        "http://localhost:8000/api/v1/auth/logout",
        {},
        { withCredentials: true }
      );

      if (data.success) {
        dispatch(userLoggedOut());
        toast.success(data.message);
        navigate("/log-In");
        setIsProfileOpen(false);
      }
    } catch (error) {
      console.error(
        "Logout failed:",
        error.response?.data?.message || error.message
      );
    }
  };

  return (
    <motion.nav
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        scrolled 
          ? 'bg-white shadow-lg backdrop-blur-sm' 
          : 'bg-transparent'
      }`}
    >
      <div className="max-w-full px-6 py-4 flex items-center justify-between">
        {/* Sidebar Toggle & Logo */}
        <div className="flex items-center gap-4">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleSidebar}
            className={`p-3 rounded-xl transition-all duration-300 ${
              scrolled 
                ? 'bg-gray-100 hover:bg-gray-200' 
                : 'bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20'
            }`}
          >
            <svg
              className={`w-5 h-5 transition-colors duration-300 ${
                scrolled ? 'text-gray-600' : 'text-white'
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </motion.button>
          
          <Link to="/" className="flex items-center gap-3">
            <motion.img
              src="/images/logonew.png"
              alt="Logo"
              className="w-14 h-14"
              whileHover={{ 
                scale: 1.1,
                rotate: [0, 5, -5, 0]
              }}
              transition={{ duration: 0.3 }}
            />
            <span className={`font-bold text-xl transition-colors duration-300 ${
              scrolled ? 'text-blue-600' : 'text-white'
            }`}>
              UPCODING
            </span>
          </Link>
        </div>

        {/* Register & Profile buttons */}
        <div className="flex items-center gap-4">
          {/* Register Dropdown */}
          <div className="relative">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsRegisterOpen(!isRegisterOpen)}
              className={`flex items-center gap-2 px-4 py-2 h-[48px] rounded-xl transition-all duration-300 text-sm font-bold ${
                scrolled 
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg' 
                  : 'bg-white/10 border border-white/20 text-white hover:bg-white/20 backdrop-blur-sm'
              }`}
            >
              Register
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </motion.button>

            <AnimatePresence>
              {isRegisterOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-xl border border-gray-200 z-20"
                >
                  <Link
                    to="/internship-registration"
                    className="block px-4 py-3 hover:bg-blue-50 text-gray-700 rounded-t-xl transition-colors duration-200"
                    onClick={() => setIsRegisterOpen(false)}
                  >
                    💼 Internship Register
                  </Link>
                  <Link
                    to="/project-registration"
                    className="block px-4 py-3 hover:bg-blue-50 text-gray-700 rounded-b-xl transition-colors duration-200"
                    onClick={() => setIsRegisterOpen(false)}
                  >
                    📋 Project Register
                  </Link>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Profile Dropdown */}
          <div className="relative">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsProfileOpen(!isProfileOpen)}
              className={`flex items-center gap-2 px-4 py-2 h-[48px] rounded-xl transition-all duration-300 text-sm font-medium ${
                scrolled 
                  ? 'bg-gray-100 text-gray-800 hover:bg-gray-200' 
                  : 'bg-white/10 border border-white/20 text-white hover:bg-white/20 backdrop-blur-sm'
              }`}
            >
              <img
                src={user?.image || "/images/user.png"}
                alt="User"
                className={`w-8 h-8 rounded-full object-cover border-2 transition-colors duration-300 ${
                  scrolled ? 'border-gray-300' : 'border-white/30'
                }`}
              />
              <span>{user?.name || "Guest"}</span>
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </motion.button>

            <AnimatePresence>
              {isProfileOpen && (
                <>
                  <div
                    className="fixed inset-0 z-10"
                    onClick={() => setIsProfileOpen(false)}
                  />
                  <motion.div
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                    className="absolute right-0 mt-2 w-72 bg-white rounded-xl shadow-xl border border-gray-200 z-20"
                  >
                    <div className="p-4 border-b border-gray-100">
                      <div className="flex items-center gap-3">
                        <img
                          src={user?.image || "/images/user.png"}
                          alt="User"
                          className="w-12 h-12 rounded-xl object-cover border-2 border-blue-200"
                        />
                        <div>
                          <div className="font-semibold text-gray-800">
                            {user?.name || "Guest User"}
                          </div>
                          <div className="text-sm text-gray-500">
                            {user?.email || "<EMAIL>"}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="p-2">
                      <Link
                        to="/profile"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        My Profile
                      </Link>
                      <Link
                        to="/settings"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        Settings
                      </Link>
                      <Link
                        to="/Sign-Up"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        Log In / Sign Up
                      </Link>
                      <button
                        onClick={handleLogout}
                        className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                      >
                        Log Out
                      </button>
                    </div>
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </motion.nav>
  );
};

export default DSAHeader;
