import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../utils/axiosInstance";

const TwoFactorSuccess = () => {
  const [message, setMessage] = useState("Verifying...");
  const navigate = useNavigate();

  useEffect(() => {
    const verifyTwoFactor = async () => {
      try {
        const { data } = await axiosInstance.post(
          "http://localhost:8000/api/v1/auth/verify-two-fa",
          {},
          { withCredentials: true }
        );

        if (data.success) {
          setMessage(
            "🎉 Two-Factor Authentication has been successfully enabled!"
          );
        } else {
          setMessage("❌ Failed to enable Two-Factor Authentication.");
        }
      } catch (error) {
        if (
          error.response?.data?.message === "Token expired" ||
          error.response?.data?.message === "Cookie not found"
        ) {
          setMessage(
            "🎉 Two-Factor Authentication has been successfully enabled!"
          );
        } else {
          setMessage("❌ Something went wrong. Please try again.");
        }
      }
    };

    verifyTwoFactor();
  }, []);

  const handleGoBack = () => {
    navigate("/");
  };

  return (
    <div className="two-factor-container">
      <div className="message-box">
        <h2>{message}</h2>
        <button onClick={handleGoBack} className="go-back-btn">
          Go Back to Home
        </button>
      </div>
    </div>
  );
};

export default TwoFactorSuccess;
