import aiCaseStudies from './aiCaseStudies.js';
import nlpCaseStudies from './nlpCaseStudies.js';

export { aiCaseStudies };
export { nlpCaseStudies };

export const pythonCaseStudies = [
  {
    title: "Python Data Structures",
    difficulty: "Beginner",
    objective: "Learn basic Python data structures",
    scenario: "Implement common data structures",
    keyConcepts: ["Lists", "Dictionaries", "Sets", "Tuples"],
    solution: `# Python Data Structures Example
# Lists
my_list = [1, 2, 3, 4, 5]
print("List:", my_list)

# Dictionaries
my_dict = {"name": "<PERSON>", "age": 30}
print("Dictionary:", my_dict)

# Sets
my_set = {1, 2, 3, 4, 5}
print("Set:", my_set)

# Tuples
my_tuple = (1, 2, 3)
print("Tuple:", my_tuple)`
  }
];

export const cvCaseStudies = [
  {
    title: "Image Processing Basics",
    difficulty: "Intermediate",
    objective: "Learn computer vision fundamentals",
    scenario: "Process digital images using OpenCV",
    keyConcepts: ["Filters", "Edge Detection", "Image Enhancement"],
    solution: `import cv2
import numpy as np

# Load image
img = cv2.imread('image.jpg')

# Convert to grayscale
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

# Apply Gaussian blur
blurred = cv2.GaussianBlur(gray, (15, 15), 0)

# Edge detection
edges = cv2.Canny(blurred, 50, 150)

# Display results
cv2.imshow('Original', img)
cv2.imshow('Edges', edges)
cv2.waitKey(0)`
  }
];

export const rlCaseStudies = [
  {
    title: "Q-Learning Agent",
    difficulty: "Advanced",
    objective: "Build a reinforcement learning agent",
    scenario: "Train an agent to navigate a grid world",
    keyConcepts: ["Q-Learning", "States", "Actions", "Rewards"],
    solution: `import numpy as np
import random

class QLearningAgent:
    def __init__(self, states, actions, learning_rate=0.1, discount=0.9):
        self.q_table = np.zeros((states, actions))
        self.lr = learning_rate
        self.gamma = discount
        
    def choose_action(self, state, epsilon=0.1):
        if random.random() < epsilon:
            return random.randint(0, len(self.q_table[state]) - 1)
        return np.argmax(self.q_table[state])
    
    def update(self, state, action, reward, next_state):
        old_value = self.q_table[state, action]
        next_max = np.max(self.q_table[next_state])
        new_value = old_value + self.lr * (reward + self.gamma * next_max - old_value)
        self.q_table[state, action] = new_value`
  }
];

export const roboticsCaseStudies = [
  {
    title: "Robot Control System",
    difficulty: "Advanced",
    objective: "Control a robotic system",
    scenario: "Program a robot to navigate obstacles",
    keyConcepts: ["Sensors", "Actuators", "Control Systems", "Path Planning"],
    solution: `import time
import math

class Robot:
    def __init__(self):
        self.x = 0
        self.y = 0
        self.angle = 0
        
    def move_forward(self, distance):
        self.x += distance * math.cos(self.angle)
        self.y += distance * math.sin(self.angle)
        
    def turn(self, angle_change):
        self.angle += angle_change
        
    def get_position(self):
        return (self.x, self.y, self.angle)
        
    def navigate_to_goal(self, goal_x, goal_y):
        while True:
            dx = goal_x - self.x
            dy = goal_y - self.y
            distance = math.sqrt(dx**2 + dy**2)
            
            if distance < 0.1:
                break
                
            target_angle = math.atan2(dy, dx)
            angle_diff = target_angle - self.angle
            
            self.turn(angle_diff * 0.1)
            self.move_forward(min(distance, 1.0))
            time.sleep(0.1)`
  }
];

export const ethicsCaseStudies = [
  {
    title: "AI Bias Detection",
    difficulty: "Intermediate",
    objective: "Identify and mitigate AI bias",
    scenario: "Analyze a hiring algorithm for bias",
    keyConcepts: ["Bias", "Fairness", "Transparency", "Ethical AI"],
    solution: `import pandas as pd
import numpy as np
from sklearn.metrics import confusion_matrix

def analyze_bias(predictions, actual, protected_attribute):
    # Calculate accuracy by group
    groups = np.unique(protected_attribute)
    bias_metrics = {}
    
    for group in groups:
        mask = protected_attribute == group
        group_pred = predictions[mask]
        group_actual = actual[mask]
        
        accuracy = np.mean(group_pred == group_actual)
        bias_metrics[group] = {
            'accuracy': accuracy,
            'count': len(group_pred)
        }
    
    # Calculate disparate impact
    accuracies = [metrics['accuracy'] for metrics in bias_metrics.values()]
    disparate_impact = min(accuracies) / max(accuracies)
    
    return {
        'group_metrics': bias_metrics,
        'disparate_impact': disparate_impact,
        'is_biased': disparate_impact < 0.8
    }

# Example usage
predictions = np.array([1, 0, 1, 1, 0, 1, 0, 1])
actual = np.array([1, 0, 1, 0, 0, 1, 1, 1])
gender = np.array(['M', 'F', 'M', 'F', 'M', 'F', 'M', 'F'])

bias_analysis = analyze_bias(predictions, actual, gender)
print("Bias Analysis:", bias_analysis)`
  }
];

export const healthcareCaseStudies = [
  {
    title: "Medical Image Analysis",
    difficulty: "Advanced",
    objective: "Analyze medical images using AI",
    scenario: "Detect anomalies in X-ray images",
    keyConcepts: ["Medical AI", "Image Classification", "Diagnosis", "Healthcare"],
    solution: `import tensorflow as tf
from tensorflow.keras import layers, models
import numpy as np

def create_medical_classifier():
    model = models.Sequential([
        layers.Conv2D(32, (3, 3), activation='relu', input_shape=(224, 224, 1)),
        layers.MaxPooling2D((2, 2)),
        layers.Conv2D(64, (3, 3), activation='relu'),
        layers.MaxPooling2D((2, 2)),
        layers.Conv2D(64, (3, 3), activation='relu'),
        layers.Flatten(),
        layers.Dense(64, activation='relu'),
        layers.Dropout(0.5),
        layers.Dense(2, activation='softmax')  # Normal vs Abnormal
    ])
    
    model.compile(
        optimizer='adam',
        loss='categorical_crossentropy',
        metrics=['accuracy', 'precision', 'recall']
    )
    
    return model

def preprocess_xray(image_path):
    # Load and preprocess X-ray image
    image = tf.io.read_file(image_path)
    image = tf.image.decode_image(image, channels=1)
    image = tf.image.resize(image, [224, 224])
    image = tf.cast(image, tf.float32) / 255.0
    return image

# Create and compile model
model = create_medical_classifier()
print("Medical AI model created successfully!")
print("Model summary:")
model.summary()`
  }
];

export const bigDataCaseStudies = [
  {
    title: "Big Data Processing with Spark",
    difficulty: "Advanced",
    objective: "Process large datasets efficiently",
    scenario: "Analyze customer behavior from massive datasets",
    keyConcepts: ["Apache Spark", "Distributed Computing", "Data Mining", "Analytics"],
    solution: `from pyspark.sql import SparkSession
from pyspark.sql.functions import col, count, avg, max, min

# Initialize Spark session
spark = SparkSession.builder.appName("CustomerAnalysis").getOrCreate()

# Load large dataset
df = spark.read.csv("customer_data.csv", header=True, inferSchema=True)

# Basic analytics
customer_stats = df.groupBy("customer_segment").agg(
    count("customer_id").alias("total_customers"),
    avg("purchase_amount").alias("avg_purchase"),
    max("purchase_amount").alias("max_purchase"),
    min("purchase_amount").alias("min_purchase")
)

# Show results
customer_stats.show()

# Advanced analysis - Customer lifetime value
clv_df = df.groupBy("customer_id").agg(
    count("transaction_id").alias("transaction_count"),
    avg("purchase_amount").alias("avg_purchase"),
    max("transaction_date").alias("last_purchase")
)

# Calculate CLV
clv_df = clv_df.withColumn(
    "estimated_clv", 
    col("transaction_count") * col("avg_purchase") * 12
)

# Top customers by CLV
top_customers = clv_df.orderBy(col("estimated_clv").desc()).limit(10)
top_customers.show()

spark.stop()`
  }
];

export const iotCaseStudies = [
  {
    title: "IoT Sensor Data Processing",
    difficulty: "Intermediate",
    objective: "Process and analyze IoT sensor data",
    scenario: "Monitor environmental conditions using IoT sensors",
    keyConcepts: ["IoT", "Sensor Networks", "Data Collection", "Real-time Processing"],
    solution: `import json
import time
import random
from datetime import datetime
import paho.mqtt.client as mqtt

class IoTSensorSimulator:
    def __init__(self, sensor_id):
        self.sensor_id = sensor_id
        self.client = mqtt.Client()
        
    def generate_sensor_data(self):
        return {
            "sensor_id": self.sensor_id,
            "timestamp": datetime.now().isoformat(),
            "temperature": round(random.uniform(20, 35), 2),
            "humidity": round(random.uniform(30, 80), 2),
            "pressure": round(random.uniform(1000, 1020), 2),
            "air_quality": random.randint(50, 200)
        }
    
    def publish_data(self, topic="sensors/environmental"):
        data = self.generate_sensor_data()
        message = json.dumps(data)
        self.client.publish(topic, message)
        return data

class IoTDataProcessor:
    def __init__(self):
        self.data_buffer = []
        
    def process_sensor_data(self, data):
        self.data_buffer.append(data)
        
        # Alert conditions
        if data["temperature"] > 30:
            print(f"HIGH TEMPERATURE ALERT: {data['temperature']}°C")
        
        if data["air_quality"] > 150:
            print(f"POOR AIR QUALITY ALERT: {data['air_quality']} AQI")
            
        # Calculate averages every 10 readings
        if len(self.data_buffer) >= 10:
            avg_temp = sum(d["temperature"] for d in self.data_buffer) / len(self.data_buffer)
            avg_humidity = sum(d["humidity"] for d in self.data_buffer) / len(self.data_buffer)
            
            print(f"Average Temperature: {avg_temp:.2f}°C")
            print(f"Average Humidity: {avg_humidity:.2f}%")
            
            self.data_buffer = []  # Reset buffer

# Simulate IoT system
sensor = IoTSensorSimulator("ENV_001")
processor = IoTDataProcessor()

print("Starting IoT sensor simulation...")
for i in range(20):
    data = sensor.publish_data()
    processor.process_sensor_data(data)
    time.sleep(1)

print("IoT simulation completed!")`
  }
];