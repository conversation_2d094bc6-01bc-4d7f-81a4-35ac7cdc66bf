import React from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Re<PERSON>s
} from "./DataScienceLabEnvironment.icons";

const DataScienceLabEnvironment = ({ showPremiumOverlay, onBackToCourse }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <div className="py-8">
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => {
              if (onBackToCourse) {
                onBackToCourse();
              }
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <span className="text-xl">←</span>
            <span>Back to Course</span>
          </button>
        </div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-teal-600 to-blue-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
            🧪 Lab Environment Setup
          </div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Your Data Science{" "}
            <span className="bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text text-transparent">
              Workspace
            </span>
          </h2>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Set up your complete data science environment with industry-standard tools and platforms
          </p>
        </motion.div>

        {/* Environment Options */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
        >
          {/* Jupyter Notebook */}
          <motion.div
            variants={itemVariants}
            whileHover={{ y: -5, scale: 1.02 }}
            className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300"
          >
            <div className="text-4xl mb-4">📓</div>
            <h3 className="text-2xl font-bold text-white mb-4">Jupyter Notebook</h3>
            <p className="text-white/70 mb-6">Interactive coding environment perfect for data exploration and analysis.</p>
            
            <div className="space-y-3 mb-6">
              <div className="flex items-center text-sm text-white/60">
                <div className="w-2 h-2 bg-teal-400 rounded-full mr-3"></div>
                Live code execution
              </div>
              <div className="flex items-center text-sm text-white/60">
                <div className="w-2 h-2 bg-teal-400 rounded-full mr-3"></div>
                Rich output display
              </div>
              <div className="flex items-center text-sm text-white/60">
                <div className="w-2 h-2 bg-teal-400 rounded-full mr-3"></div>
                Markdown documentation
              </div>
            </div>
            
            <button 
              onClick={showPremiumOverlay}
              className="w-full bg-gradient-to-r from-teal-600/30 to-blue-600/30 hover:from-teal-600/40 hover:to-blue-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium transition-all duration-300 border border-white/10"
            >
              Launch Environment
            </button>
          </motion.div>

          {/* Google Colab */}
          <motion.div
            variants={itemVariants}
            whileHover={{ y: -5, scale: 1.02 }}
            className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300"
          >
            <div className="text-4xl mb-4">☁️</div>
            <h3 className="text-2xl font-bold text-white mb-4">Google Colab</h3>
            <p className="text-white/70 mb-6">Cloud-based platform with free GPU access for machine learning projects.</p>
            
            <div className="space-y-3 mb-6">
              <div className="flex items-center text-sm text-white/60">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                Free GPU/TPU access
              </div>
              <div className="flex items-center text-sm text-white/60">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                Pre-installed libraries
              </div>
              <div className="flex items-center text-sm text-white/60">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                Easy sharing & collaboration
              </div>
            </div>
            
            <button 
              onClick={showPremiumOverlay}
              className="w-full bg-gradient-to-r from-green-600/30 to-teal-600/30 hover:from-green-600/40 hover:to-teal-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium transition-all duration-300 border border-white/10"
            >
              Open in Colab
            </button>
          </motion.div>

          {/* Local Setup */}
          <motion.div
            variants={itemVariants}
            whileHover={{ y: -5, scale: 1.02 }}
            className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300"
          >
            <div className="text-4xl mb-4">💻</div>
            <h3 className="text-2xl font-bold text-white mb-4">Local Setup</h3>
            <p className="text-white/70 mb-6">Complete guide to set up your own data science environment locally.</p>
            
            <div className="space-y-3 mb-6">
              <div className="flex items-center text-sm text-white/60">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                Anaconda installation
              </div>
              <div className="flex items-center text-sm text-white/60">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                Package management
              </div>
              <div className="flex items-center text-sm text-white/60">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                Virtual environments
              </div>
            </div>
            
            <button 
              onClick={showPremiumOverlay}
              className="w-full bg-gradient-to-r from-blue-600/30 to-purple-600/30 hover:from-blue-600/40 hover:to-purple-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium transition-all duration-300 border border-white/10"
            >
              Setup Guide
            </button>
          </motion.div>
        </motion.div>

        {/* Essential Libraries */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl mb-16"
        >
          <h3 className="text-2xl font-bold text-white mb-8 flex items-center">
            <span className="text-3xl mr-3">📦</span>
            Essential Python Libraries
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            { [
              { name: "Python", icon: <SiPython className="text-[#3776AB] text-4xl" /> },
              { name: "Pandas", icon: <SiPandas className="text-[#150458] text-4xl" /> },
              { name: "NumPy", icon: <SiNumpy className="text-[#013243] text-4xl" /> },
              { name: "Matplotlib", icon: <SiMatplotlib className="text-[#11557C] text-4xl" /> },
              { name: "Seaborn", icon: <SiSeaborn className="text-[#4C72B0] text-4xl" /> },
              { name: "Scikit-learn", icon: <SiScikitlearn className="text-[#F7931E] text-4xl" /> },
              { name: "TensorFlow", icon: <SiTensorflow className="text-[#FF6F00] text-4xl" /> },
              { name: "Plotly", icon: <SiPlotly className="text-[#3F4F75] text-4xl" /> },
              { name: "Jupyter", icon: <SiJupyter className="text-[#F37626] text-4xl" /> },
              { name: "Requests", icon: <SiRequests className="text-[#000000] text-4xl" /> },
            ].map((tool, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05 }}
                className="bg-white/5 border border-white/10 rounded-xl p-4 text-center hover:bg-white/10 transition-all duration-300"
              >
                <div className="text-3xl mb-2">{tool.icon}</div>
                <h4 className="font-bold text-white mb-1">{tool.name}</h4>
              </motion.div>
            )) }
          </div>
        </motion.div>

        {/* Sample Datasets */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl mb-16"
        >
          <h3 className="text-2xl font-bold text-white mb-8 flex items-center">
            <span className="text-3xl mr-3">🗂️</span>
            Practice Datasets
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
              { 
                name: "Customer Analytics", 
                size: "10K rows", 
                type: "Business", 
                topics: ["Segmentation", "Churn Analysis", "RFM"] 
              },
              { 
                name: "Financial Markets", 
                size: "50K rows", 
                type: "Finance", 
                topics: ["Time Series", "Risk Analysis", "Forecasting"] 
              },
              { 
                name: "Healthcare Data", 
                size: "25K rows", 
                type: "Medical", 
                topics: ["Diagnostics", "Drug Discovery", "Epidemiology"] 
              },
              { 
                name: "Social Media", 
                size: "100K rows", 
                type: "Text", 
                topics: ["Sentiment Analysis", "NLP", "Engagement"] 
              }
            ].map((dataset, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.02 }}
                className="bg-white/5 border border-white/10 rounded-xl p-6 hover:bg-white/10 transition-all duration-300"
              >
                <div className="flex justify-between items-start mb-4">
                  <h4 className="font-bold text-white">{dataset.name}</h4>
                  <div className="flex gap-2">
                    <span className="text-xs bg-teal-500/20 text-teal-300 px-2 py-1 rounded">{dataset.size}</span>
                    <span className="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded">{dataset.type}</span>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  {dataset.topics.map((topic, i) => (
                    <span key={i} className="text-xs text-white/60 bg-white/5 px-2 py-1 rounded">
                      {topic}
                    </span>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Quick Start Guide */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.7 }}
          className="text-center bg-gradient-to-r from-teal-600/20 to-blue-600/20 backdrop-blur-lg border border-white/10 rounded-3xl p-12"
        >
          <h3 className="text-3xl font-bold text-white mb-4">Ready to Start Coding?</h3>
          <p className="text-white/80 text-lg mb-8 max-w-2xl mx-auto">
            Choose your preferred environment and begin your data science journey with our guided tutorials.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={showPremiumOverlay}
              className="bg-gradient-to-r from-teal-600 to-blue-600 text-white px-8 py-4 rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Launch Lab Environment
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="border-2 border-white/30 backdrop-blur-lg text-white px-8 py-4 rounded-xl font-bold hover:bg-white/10 transition-all duration-300"
            >
              Download Setup Guide
            </motion.button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default DataScienceLabEnvironment;
