import React, { useState } from 'react';
import { FaUserTie, FaUserPlus, FaStar, FaTimes } from 'react-icons/fa';

const InstructorsSection = () => {
  // Mock instructor data
  const [instructors, setInstructors] = useState([
    { id: 1, name: '<PERSON>', expertise: 'Python, Data Science', courses: 4, rating: 4.8 },
    { id: 2, name: '<PERSON>', expertise: 'React, JavaScript', courses: 3, rating: 4.9 },
    { id: 3, name: '<PERSON>', expertise: 'Machine Learning, AI', courses: 2, rating: 4.7 },
  ]);
  
  const [showModal, setShowModal] = useState(false);
  const [editingInstructor, setEditingInstructor] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    expertise: '',
    courses: 0,
    rating: 5.0
  });
  
  const openModal = (instructor = null) => {
    if (instructor) {
      // Edit mode
      setFormData({
        name: instructor.name,
        expertise: instructor.expertise,
        courses: instructor.courses,
        rating: instructor.rating
      });
      setEditingInstructor(instructor);
    } else {
      // Add mode
      setFormData({
        name: '',
        expertise: '',
        courses: 0,
        rating: 5.0
      });
      setEditingInstructor(null);
    }
    setShowModal(true);
  };
  
  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'number' ? parseFloat(value) : value
    });
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    const formattedData = {
      ...formData,
      courses: parseInt(formData.courses),
      rating: parseFloat(formData.rating)
    };
    
    if (editingInstructor) {
      // Update existing instructor
      setInstructors(instructors.map(instructor => 
        instructor.id === editingInstructor.id 
          ? { ...instructor, ...formattedData } 
          : instructor
      ));
    } else {
      // Add new instructor
      const newInstructor = {
        id: instructors.length + 1,
        ...formattedData
      };
      setInstructors([...instructors, newInstructor]);
    }
    
    setShowModal(false);
    setEditingInstructor(null);
  };
  
  const deleteInstructor = (instructorId) => {
    setInstructors(instructors.filter(instructor => instructor.id !== instructorId));
    setShowDeleteConfirm(null);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Instructors Management</h2>
        <button 
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
          onClick={() => openModal()}
        >
          <FaUserPlus className="mr-2" />
          Add New Instructor
        </button>
      </div>

      {/* Instructors Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {instructors.map((instructor) => (
          <div key={instructor.id} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <FaUserTie className="text-blue-500 text-xl" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">{instructor.name}</h3>
                  <p className="text-sm text-gray-600">{instructor.expertise}</p>
                </div>
              </div>
              
              <div className="flex justify-between items-center mb-4">
                <div className="text-sm text-gray-600">
                  <span className="font-medium">{instructor.courses}</span> courses
                </div>
                <div className="flex items-center text-sm">
                  <FaStar className="text-yellow-500 mr-1" />
                  <span>{instructor.rating}</span>
                </div>
              </div>
              
              <div className="flex justify-end">
                <button 
                  className="text-blue-600 hover:text-blue-800 mr-3"
                  onClick={() => openModal(instructor)}
                >
                  Edit
                </button>
                <button 
                  className="text-red-600 hover:text-red-800"
                  onClick={() => setShowDeleteConfirm(instructor.id)}
                >
                  Remove
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add/Edit Instructor Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center border-b border-gray-200 px-6 py-4">
              <h3 className="text-lg font-semibold text-gray-800">
                {editingInstructor ? 'Edit Instructor' : 'Add New Instructor'}
              </h3>
              <button 
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimes />
              </button>
            </div>
            
            <form onSubmit={handleSubmit} className="p-6">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
                  Full Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="expertise">
                  Areas of Expertise
                </label>
                <input
                  type="text"
                  id="expertise"
                  name="expertise"
                  value={formData.expertise}
                  onChange={handleInputChange}
                  placeholder="e.g. Python, Data Science, JavaScript"
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="courses">
                  Number of Courses
                </label>
                <input
                  type="number"
                  id="courses"
                  name="courses"
                  value={formData.courses}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  min="0"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="rating">
                  Rating
                </label>
                <input
                  type="number"
                  id="rating"
                  name="rating"
                  value={formData.rating}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  min="1"
                  max="5"
                  step="0.1"
                  required
                />
              </div>
              
              <div className="flex justify-end">
                <button
                  type="button"
                  className="bg-gray-300 text-gray-800 px-4 py-2 rounded-md mr-2"
                  onClick={() => setShowModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md"
                >
                  {editingInstructor ? 'Update Instructor' : 'Add Instructor'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Confirm Deletion</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to remove this instructor? This action cannot be undone.
            </p>
            <div className="flex justify-end">
              <button
                className="bg-gray-300 text-gray-800 px-4 py-2 rounded-md mr-2"
                onClick={() => setShowDeleteConfirm(null)}
              >
                Cancel
              </button>
              <button
                className="bg-red-600 text-white px-4 py-2 rounded-md"
                onClick={() => deleteInstructor(showDeleteConfirm)}
              >
                Remove
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InstructorsSection;