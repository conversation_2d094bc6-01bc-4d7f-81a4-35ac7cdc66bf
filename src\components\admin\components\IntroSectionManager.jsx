import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { Plus, Edit, Trash2, Save, Eye } from 'lucide-react';

const IntroSectionManager = ({ theme }) => {
  const [introSections, setIntroSections] = useState([
    {
      id: 1,
      title: 'Welcome to Our Platform',
      content: '<h2>Getting Started</h2><p>Welcome to our comprehensive learning platform. Here you\'ll find everything you need to advance your skills in programming and technology.</p><ul><li>Interactive courses</li><li>Hands-on projects</li><li>Expert guidance</li></ul>',
      isActive: true,
      position: 1,
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      title: 'Course Overview',
      content: '<h3>What You\'ll Learn</h3><p>Our courses are designed to take you from beginner to advanced level with practical, real-world examples.</p><blockquote>Learn by doing, not just reading.</blockquote>',
      isActive: true,
      position: 2,
      createdAt: '2024-01-14'
    }
  ]);

  const [showEditor, setShowEditor] = useState(false);
  const [editingSection, setEditingSection] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    isActive: true,
    position: 1
  });

  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['link', 'image'],
      [{ 'align': [] }],
      [{ 'color': [] }, { 'background': [] }],
      ['blockquote', 'code-block'],
      ['clean']
    ]
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'indent', 'link', 'image',
    'align', 'color', 'background', 'blockquote', 'code-block'
  ];

  const handleAdd = () => {
    setEditingSection(null);
    setFormData({
      title: '',
      content: '',
      isActive: true,
      position: introSections.length + 1
    });
    setShowEditor(true);
  };

  const handleEdit = (section) => {
    setEditingSection(section);
    setFormData({
      title: section.title,
      content: section.content,
      isActive: section.isActive,
      position: section.position
    });
    setShowEditor(true);
  };

  const handleSave = () => {
    if (editingSection) {
      setIntroSections(prev => prev.map(section => 
        section.id === editingSection.id 
          ? { ...section, ...formData }
          : section
      ));
    } else {
      const newSection = {
        id: Date.now(),
        ...formData,
        createdAt: new Date().toISOString().split('T')[0]
      };
      setIntroSections(prev => [...prev, newSection]);
    }
    setShowEditor(false);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this intro section?')) {
      setIntroSections(prev => prev.filter(section => section.id !== id));
    }
  };

  const toggleStatus = (id) => {
    setIntroSections(prev => prev.map(section => 
      section.id === id 
        ? { ...section, isActive: !section.isActive }
        : section
    ));
  };

  const moveSection = (id, direction) => {
    setIntroSections(prev => {
      const sections = [...prev];
      const index = sections.findIndex(s => s.id === id);
      if (index === -1) return sections;

      const newIndex = direction === 'up' ? index - 1 : index + 1;
      if (newIndex < 0 || newIndex >= sections.length) return sections;

      [sections[index], sections[newIndex]] = [sections[newIndex], sections[index]];
      
      // Update positions
      sections.forEach((section, idx) => {
        section.position = idx + 1;
      });

      return sections;
    });
  };

  const sortedSections = [...introSections].sort((a, b) => a.position - b.position);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Intro Section Manager
          </h2>
          <p className={`mt-1 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Manage introduction content with rich text editing
          </p>
        </div>
        
        <button
          onClick={handleAdd}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus size={16} />
          <span>Add Section</span>
        </button>
      </div>

      {/* Sections List */}
      <div className="space-y-4">
        {sortedSections.map((section, index) => (
          <div
            key={section.id}
            className={`rounded-lg border p-6 ${
              theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className={`text-lg font-semibold ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {section.title}
                  </h3>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    section.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {section.isActive ? 'Active' : 'Inactive'}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800`}>
                    Position {section.position}
                  </span>
                </div>
                <p className={`text-sm ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'} mb-3`}>
                  Created: {section.createdAt}
                </p>
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => moveSection(section.id, 'up')}
                  disabled={index === 0}
                  className={`p-2 rounded-lg transition-colors ${
                    index === 0 
                      ? 'opacity-50 cursor-not-allowed'
                      : theme === 'dark'
                        ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                        : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                >
                  ↑
                </button>
                <button
                  onClick={() => moveSection(section.id, 'down')}
                  disabled={index === sortedSections.length - 1}
                  className={`p-2 rounded-lg transition-colors ${
                    index === sortedSections.length - 1
                      ? 'opacity-50 cursor-not-allowed'
                      : theme === 'dark'
                        ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                        : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                >
                  ↓
                </button>
                <button
                  onClick={() => toggleStatus(section.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                      : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                >
                  <Eye size={16} />
                </button>
                <button
                  onClick={() => handleEdit(section)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                      : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                >
                  <Edit size={16} />
                </button>
                <button
                  onClick={() => handleDelete(section.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                      : 'text-red-500 hover:bg-red-50 hover:text-red-700'
                  }`}
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>

            {/* Content Preview */}
            <div className={`prose max-w-none ${
              theme === 'dark' ? 'prose-invert' : ''
            }`}>
              <div dangerouslySetInnerHTML={{ __html: section.content }} />
            </div>
          </div>
        ))}
      </div>

      {/* Editor Modal */}
      {showEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className={`rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto ${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-lg font-semibold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {editingSection ? 'Edit Section' : 'Add New Section'}
              </h3>
              <button
                onClick={() => setShowEditor(false)}
                className={`p-2 rounded-lg transition-colors ${
                  theme === 'dark'
                    ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                    : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                }`}
              >
                ×
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Title
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                    placeholder="Enter section title..."
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Position
                  </label>
                  <input
                    type="number"
                    value={formData.position}
                    onChange={(e) => setFormData(prev => ({ ...prev, position: parseInt(e.target.value) }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                    min="1"
                  />
                </div>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Content
                </label>
                <div className={`${theme === 'dark' ? 'quill-dark' : ''}`}>
                  <ReactQuill
                    theme="snow"
                    value={formData.content}
                    onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                    modules={quillModules}
                    formats={quillFormats}
                    style={{ height: '300px', marginBottom: '50px' }}
                  />
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="mr-2"
                />
                <label htmlFor="isActive" className={`text-sm ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Active
                </label>
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowEditor(false)}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  theme === 'dark'
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <Save size={16} />
                <span>{editingSection ? 'Update' : 'Create'}</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Custom styles for dark theme */}
      <style jsx global>{`
        .quill-dark .ql-toolbar {
          background: #374151;
          border-color: #4b5563;
        }
        .quill-dark .ql-container {
          background: #374151;
          border-color: #4b5563;
          color: white;
        }
        .quill-dark .ql-editor {
          color: white;
        }
        .quill-dark .ql-stroke {
          stroke: #9ca3af;
        }
        .quill-dark .ql-fill {
          fill: #9ca3af;
        }
      `}</style>
    </div>
  );
};

export default IntroSectionManager;
