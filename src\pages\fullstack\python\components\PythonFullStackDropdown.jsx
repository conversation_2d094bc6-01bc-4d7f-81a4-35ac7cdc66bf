import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaChevronDown, FaPython, FaReact, FaDatabase, FaServer } from "react-icons/fa";

const PythonFullStackDropdown = ({ title, items, defaultOpen = false }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const getIcon = (category) => {
    switch (category.toLowerCase()) {
      case "python backend development":
        return <FaPython className="text-green-400" />;
      case "frontend integration":
        return <FaReact className="text-blue-400" />;
      case "database management":
        return <FaDatabase className="text-purple-400" />;
      default:
        return <FaServer className="text-indigo-400" />;
    }
  };

  return (
    <div className="mb-6 bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-md overflow-hidden">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-green-900/20 to-blue-900/20 hover:from-green-900/30 hover:to-blue-900/30 transition-colors text-white"
        aria-expanded={isOpen}
      >
        <div className="flex items-center">
          {getIcon(title)}
          <h3 className="ml-3 text-lg font-semibold text-white">{title}</h3>
        </div>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <FaChevronDown className="text-gray-300" />
        </motion.div>
      </button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="p-4 space-y-4">
              {items.map((item, index) => (
                <motion.div
                  key={index}
                  whileHover={{ x: 5 }}
                  className="bg-gray-800/30 rounded-lg p-4 hover:bg-gray-700/40 transition-colors border border-gray-700/30"
                >
                  <h4 className="text-md font-medium text-white mb-2">{item.title}</h4>
                  
                  <div className="flex flex-wrap gap-2 mb-3">
                    {item.tags.map((tag, tIndex) => (
                      <span
                        key={tIndex}
                        className="px-2 py-1 bg-green-900/30 text-green-300 text-xs font-medium rounded-md border border-green-700/30"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  
                  <ul className="space-y-2">
                    {item.keyPoints.map((point, pIndex) => (
                      <li key={pIndex} className="flex items-start">
                        <div className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-300 text-sm">{point}</span>
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PythonFullStackDropdown;