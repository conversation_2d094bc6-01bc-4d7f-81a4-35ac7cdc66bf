import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Sidebar from './components/Sidebar';
import Navbar from './components/Navbar';
import CoursesSection from './components/CoursesSection';
import LiveClassesSection from './components/LiveClassesSection';
import StudentsSection from './components/StudentsSection';
import InstructorsSection from './components/InstructorsSection';
import AnalyticsSection from './components/AnalyticsSection';
import FeedbackSection from './components/FeedbackSection';
import ReportsSection from './components/ReportsSection';
import SettingsSection from './components/SettingsSection';

// Menu items definition for the sidebar
const menuItems = [
  { id: 'courses', label: 'Courses' },
  { id: 'liveClasses', label: 'Live Classes' },
  { id: 'students', label: 'Students' },
  { id: 'instructors', label: 'Instructors' },
  { id: 'analytics', label: 'Analytics' },
  { id: 'feedback', label: 'Feedback' },
  { id: 'reports', label: 'Reports' },
  { id: 'settings', label: 'Settings' }
];

const AdminDashboard = () => {
  const [activeSection, setActiveSection] = useState('courses');
  const [activeContent, setActiveContent] = useState('courses');
  const { user } = useSelector((state) => state.auth);
  const navigate = useNavigate();
  
  // Mock admin check - in a real app, this would come from the user object
  const isAdmin = true; // For demo purposes
  
  useEffect(() => {
    // Redirect non-admin users
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);
  
  // If not admin, don't render the dashboard
  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h2>
          <p className="text-gray-700 mb-4">You do not have permission to access this page.</p>
          <button 
            onClick={() => navigate('/')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md"
          >
            Return to Home
          </button>
        </div>
      </div>
    );
  }
  
  // Handle section change
  const handleSectionChange = (section) => {
    setActiveSection(section);
    setActiveContent(section);
  };



  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar activeSection={activeSection} setActiveSection={handleSectionChange} />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />
        
        <main className="flex-1 overflow-y-auto bg-gray-50 p-4">
          <div className="container mx-auto">
            {activeSection === 'courses' && <CoursesSection />}
            {activeSection === 'liveClasses' && <LiveClassesSection />}
            {activeSection === 'students' && <StudentsSection />}
            {activeSection === 'instructors' && <InstructorsSection />}
            {activeSection === 'analytics' && <AnalyticsSection />}
            {activeSection === 'feedback' && <FeedbackSection />}
            {activeSection === 'reports' && <ReportsSection />}
            {activeSection === 'settings' && <SettingsSection />}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;