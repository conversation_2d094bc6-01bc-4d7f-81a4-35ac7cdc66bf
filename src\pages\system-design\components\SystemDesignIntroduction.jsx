import React, { useState } from "react";

const SystemDesignIntroduction = () => {
  const [expandedSection, setExpandedSection] = useState(null);

  const toggleSection = (section) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  // Topics for system design introduction
  const topics = [
    {
      id: "fundamentals",
      title: "System Design Fundamentals",
      icon: "fas fa-layer-group",
      color: "bg-blue-100 text-blue-700",
      content: [
        {
          subTitle: "What is System Design?",
          text: "System design is the process of defining the architecture, interfaces, and data for a system that satisfies specific requirements. It involves understanding how to structure different components to work together efficiently, addressing concerns like scalability, reliability, and maintainability."
        },
        {
          subTitle: "Why is System Design Important?",
          text: "Strong system design skills are crucial for building scalable applications that can handle growth, ensuring systems remain reliable under different conditions, and creating maintainable codebases that can evolve over time. These skills are highly valued in senior engineering roles and technical interviews."
        }
      ]
    },
    {
      id: "key-concepts",
      title: "Key Concepts",
      icon: "fas fa-key",
      color: "bg-green-100 text-green-700",
      content: [
        {
          subTitle: "Scalability",
          text: "Scalability refers to a system's ability to handle growing amounts of work by adding resources. Horizontal scaling (adding more machines) and vertical scaling (adding more power to existing machines) are two primary approaches."
        },
        {
          subTitle: "Availability",
          text: "Availability measures the percentage of time that a system is operational and accessible when required. It's typically measured in 'nines' - 99.9% (three nines), 99.99% (four nines), etc."
        },
        {
          subTitle: "Reliability",
          text: "Reliability refers to a system's ability to perform its required functions under stated conditions for a specified period. This includes handling failures gracefully and recovering from errors."
        },
        {
          subTitle: "Performance",
          text: "Performance metrics include latency (response time), throughput (requests per second), and resource utilization. Optimizing performance often involves trade-offs with other system qualities."
        }
      ]
    },
    {
      id: "components",
      title: "Common System Components",
      icon: "fas fa-cubes",
      color: "bg-purple-100 text-purple-700",
      content: [
        {
          subTitle: "Load Balancers",
          text: "Load balancers distribute incoming network traffic across multiple servers to ensure no single server becomes overwhelmed. They improve responsiveness and increase availability."
        },
        {
          subTitle: "Caching",
          text: "Caching stores copies of data in a faster storage layer to reduce database load and improve read performance. Common caching strategies include write-through, write-behind, and cache-aside."
        },
        {
          subTitle: "Databases",
          text: "Systems may use relational databases (RDBMS) for structured data with relationships, NoSQL databases for flexible schema and horizontal scaling, or a combination based on needs."
        },
        {
          subTitle: "Message Queues",
          text: "Message queues enable asynchronous communication between services, helping with decoupling, buffering, and handling traffic spikes."
        }
      ]
    },
    {
      id: "methodologies",
      title: "Design Methodologies",
      icon: "fas fa-sitemap",
      color: "bg-yellow-100 text-yellow-700",
      content: [
        {
          subTitle: "Monolithic Architecture",
          text: "A traditional unified model where all components are interconnected and interdependent. Simple to develop but can become unwieldy as the application grows."
        },
        {
          subTitle: "Microservices Architecture",
          text: "Breaking down an application into small, independent services that communicate through well-defined APIs. Offers flexibility and scalability but adds complexity to deployment and monitoring."
        },
        {
          subTitle: "Event-Driven Architecture",
          text: "Components communicate through events, enabling loose coupling and high responsiveness. Well-suited for applications with complex workflows and real-time requirements."
        },
        {
          subTitle: "Domain-Driven Design (DDD)",
          text: "An approach to software development that connects complex design to an evolving model, focusing on the core domain and domain logic."
        }
      ]
    }
  ];

  // CSS animation styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .content-animate {
      animation: fadeIn 0.3s ease-out forwards;
    }
  `;

  return (
    <div className="bg-gray-800 rounded-xl shadow-md overflow-hidden border border-gray-700">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="p-6 bg-gradient-to-r from-blue-900 to-indigo-900 border-b border-gray-700">
        <h2 className="text-3xl font-bold text-white">System Design Introduction</h2>
        <p className="text-gray-300 mt-2">Master the fundamentals of designing scalable and efficient systems</p>
      </div>

      <div className="p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          {topics.map((topic) => (
            <div 
              key={topic.id}
              className="border rounded-lg overflow-hidden shadow-sm"
            >
              <button
                onClick={() => toggleSection(topic.id)}
                className="w-full flex items-center justify-between p-4 text-left bg-gray-700 hover:bg-gray-600 transition-colors text-white"
              >
                <div className="flex items-center">
                  <span className={`w-10 h-10 rounded-full ${topic.color} flex items-center justify-center mr-3`}>
                    <i className={`${topic.icon}`}></i>
                  </span>
                  <h3 className="text-xl font-semibold">{topic.title}</h3>
                </div>
                <span className="text-xl">
                  {expandedSection === topic.id ? '−' : '+'}
                </span>
              </button>

              {expandedSection === topic.id && (
                <div className="p-4 content-animate bg-gray-900">
                  {topic.content.map((item, index) => (
                    <div key={index} className="mb-4 last:mb-0">
                      <h4 className="text-lg font-medium text-white mb-2">{item.subTitle}</h4>
                      <p className="text-gray-300">{item.text}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="mt-8 bg-gray-700 rounded-lg p-6 border border-gray-600">
          <h3 className="text-xl font-semibold text-blue-300 mb-3">Ready to Practice?</h3>
          <p className="text-gray-300 mb-4">
            Now that you understand the basics, put your knowledge to work in our Lab Environment or check out real-world projects.
          </p>
          <div className="flex flex-wrap gap-3">
            <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:shadow-md transition-shadow">
              <i className="fas fa-code mr-2"></i>
              Go to Lab Environment
            </button>
            <button className="px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:shadow-md transition-shadow">
              <i className="fas fa-project-diagram mr-2"></i>
              View Projects
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemDesignIntroduction;
