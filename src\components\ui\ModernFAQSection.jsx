import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { faqService } from "../../utils/faqService";

const ModernFAQSection = ({ 
  category = "general", 
  title = "Frequently Asked Questions",
  subtitle = "Everything you need to know about our courses.",
  themeColor = "teal",
  showContactSupport = true 
}) => {
  const [activeIndex, setActiveIndex] = useState(null);
  const [faqs, setFaqs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Enhanced color themes to match the teal homepage theme
  const colorThemes = {
    teal: {
      primary: "#118c6e",
      secondary: "#0f7a5f", 
      tertiary: "#0e7c62",
      quaternary: "#149b77",
      background: "from-slate-900 via-gray-900 to-slate-800",
      cardBg: "from-slate-800/90 to-slate-700/90",
      accent: "from-[#118c6e] to-[#0f7a5f]",
      text: "text-emerald-400",
      lightText: "text-emerald-300",
      border: "border-emerald-500/30",
      hover: "hover:border-emerald-400/50"
    },
    blue: {
      primary: "#3b82f6",
      secondary: "#2563eb",
      tertiary: "#1d4ed8",
      quaternary: "#1e40af",
      background: "from-slate-900 via-blue-950 to-slate-800",
      cardBg: "from-blue-900/90 to-slate-800/90",
      accent: "from-blue-600 to-blue-700",
      text: "text-blue-400",
      lightText: "text-blue-300",
      border: "border-blue-500/30",
      hover: "hover:border-blue-400/50"
    },
    purple: {
      primary: "#8b5cf6",
      secondary: "#7c3aed",
      tertiary: "#6d28d9",
      quaternary: "#5b21b6",
      background: "from-slate-900 via-purple-950 to-slate-800",
      cardBg: "from-purple-900/90 to-slate-800/90",
      accent: "from-purple-600 to-purple-700",
      text: "text-purple-400",
      lightText: "text-purple-300",
      border: "border-purple-500/30",
      hover: "hover:border-purple-400/50"
    },
    orange: {
      primary: "#f59e0b",
      secondary: "#d97706",
      tertiary: "#b45309",
      quaternary: "#92400e",
      background: "from-slate-900 via-orange-950 to-slate-800",
      cardBg: "from-orange-900/90 to-slate-800/90",
      accent: "from-orange-600 to-orange-700",
      text: "text-orange-400",
      lightText: "text-orange-300",
      border: "border-orange-500/30",
      hover: "hover:border-orange-400/50"
    }
  };

  const theme = colorThemes[themeColor] || colorThemes.teal;

  // Fetch FAQs on component mount
  useEffect(() => {
    const fetchFAQs = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await faqService.getFAQsByCategory(category);
        setFaqs(response.data || []);
      } catch (err) {
        console.error('Error fetching FAQs:', err);
        setError('Failed to load FAQs from server. Please try again later.');
        setFaqs([]);
      } finally {
        setLoading(false);
      }
    };

    fetchFAQs();
  }, [category]);

  const toggleFAQ = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      },
    },
  };

  const headerVariants = {
    hidden: { y: -50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      },
    },
  };

  // Loading state
  if (loading) {
    return (
      <div className={`min-h-screen bg-gradient-to-br ${theme.background} relative overflow-hidden`}>
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-emerald-400/10 to-teal-400/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-emerald-600/10 to-teal-600/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>
        
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${theme.accent} mb-6`}>
              <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
            </div>
            <p className="text-xl text-gray-300 font-medium">Loading FAQs...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br ${theme.background} relative overflow-hidden`}>
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-emerald-400/10 to-teal-400/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-emerald-600/10 to-teal-600/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-emerald-500/5 to-teal-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-6 py-20">
        {/* Header Section */}
        <motion.div
          variants={headerVariants}
          initial="hidden"
          animate="visible"
          className="text-center mb-20"
        >
          <motion.div
            variants={headerVariants}
            className="inline-block mb-6"
          >
            <span className={`inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r ${theme.accent} text-white text-sm font-semibold`}>
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
              FAQ Section
            </span>
          </motion.div>

          <motion.h1
            variants={headerVariants}
            className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6"
          >
            <span className="text-white">{title.split(" ").slice(0, -1).join(" ")}</span>{" "}
            <span className={`bg-gradient-to-r ${theme.accent} bg-clip-text text-transparent`}>
              {title.split(" ").slice(-1)[0]}
            </span>
          </motion.h1>

          <motion.p
            variants={headerVariants}
            className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
          >
            {subtitle}
          </motion.p>

          {error && (
            <motion.div
              variants={headerVariants}
              className="mt-8 max-w-2xl mx-auto"
            >
              <div className="bg-red-900/50 border border-red-500/50 rounded-xl p-6 backdrop-blur-sm">
                <div className="flex items-center">
                  <svg className="w-6 h-6 text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-300 font-medium">{error}</p>
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* FAQ Content */}
        {faqs.length > 0 ? (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {faqs.map((faq, index) => (
              <motion.div
                key={faq._id || index}
                variants={itemVariants}
                className={`group relative bg-gradient-to-r ${theme.cardBg} backdrop-blur-xl rounded-2xl border ${theme.border} ${theme.hover} overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-emerald-500/10`}
              >
                {/* Gradient overlay on hover */}
                <div className={`absolute inset-0 bg-gradient-to-r ${theme.accent} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>
                
                <button
                  onClick={() => toggleFAQ(index)}
                  className="relative w-full px-8 py-8 text-left transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:ring-offset-2 focus:ring-offset-transparent"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1 pr-6">
                      <h3 className="text-xl md:text-2xl font-bold text-white group-hover:text-emerald-300 transition-colors duration-300 leading-relaxed">
                        {faq.question}
                      </h3>
                    </div>
                    
                    <motion.div
                      animate={{ rotate: activeIndex === index ? 180 : 0 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="flex-shrink-0 ml-4"
                    >
                      <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${theme.accent} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                        <svg
                          className="w-6 h-6 text-white"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          strokeWidth={2}
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </div>
                    </motion.div>
                  </div>
                </button>
                
                <AnimatePresence>
                  {activeIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.4, ease: "easeInOut" }}
                      className={`border-t border-emerald-500/20`}
                    >
                      <div className="px-8 py-8">
                        <div className="text-lg text-gray-300 leading-relaxed space-y-4">
                          {faq.answer.split('\n').map((paragraph, idx) => (
                            <p key={idx} className="first:mt-0">
                              {paragraph}
                            </p>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div
            variants={itemVariants}
            initial="hidden"
            animate="visible"
            className="text-center py-20"
          >
            <div className={`inline-flex items-center justify-center w-24 h-24 rounded-full bg-gradient-to-r ${theme.cardBg} backdrop-blur-xl border ${theme.border} mb-8`}>
              <svg className="w-12 h-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">No FAQs Available</h3>
            <p className="text-xl text-gray-400">
              No FAQs are available for this category yet. Check back later!
            </p>
          </motion.div>
        )}

        {/* Contact Support Section */}
        {showContactSupport && (
          <motion.div
            variants={itemVariants}
            initial="hidden"
            animate="visible"
            className="mt-24"
          >
            <div className={`relative bg-gradient-to-r ${theme.cardBg} backdrop-blur-xl rounded-3xl p-12 border ${theme.border} overflow-hidden`}>
              {/* Background decoration */}
              <div className="absolute inset-0">
                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-emerald-400/10 to-teal-400/10 rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-emerald-600/10 to-teal-600/10 rounded-full blur-2xl"></div>
              </div>
              
              <div className="relative z-10 text-center">
                <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r ${theme.accent} mb-8`}>
                  <svg className="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                
                <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
                  Still have questions?
                </h3>
                <p className="text-xl text-gray-300 mb-10 max-w-2xl mx-auto leading-relaxed">
                  Our dedicated support team is here to help you succeed in your learning journey. Get personalized assistance and expert guidance.
                </p>
                
                <button className={`group relative inline-flex items-center justify-center px-10 py-4 bg-gradient-to-r ${theme.accent} text-white font-bold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-emerald-500/25 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:ring-offset-2 focus:ring-offset-transparent`}>
                  <span className="relative z-10">Contact Support</span>
                  <svg className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                  
                  {/* Button background animation */}
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-emerald-600 to-teal-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default ModernFAQSection;
