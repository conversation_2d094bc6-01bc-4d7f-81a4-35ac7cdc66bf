import { ImCross } from "react-icons/im";
import { FaArrowAltCircleRight } from "react-icons/fa";

const CrossIcon = ({ onClick, isSidebarOpen }) => {
  return (
    <div className="cross-icon" onClick={onClick}>
      {isSidebarOpen ? (
        <ImCross size={20} style={{ color: "#009cff" }} title="Close" />
      ) : (
        <FaArrowAltCircleRight
          size={20}
          style={{ color: "#009cff" }}
          title="Close"
        />
      )}
    </div>
  );
};

export default CrossIcon;
