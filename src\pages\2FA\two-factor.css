/* Modern 2FA Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

/* Modern 2FA Container */
.modern-two-fa-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #1e3a8a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

/* Background blur elements */
.modern-two-fa-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 100%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* Modern card styling */
.modern-two-fa-card {
  background: rgba(30, 41, 59, 0.9);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 24px;
  padding: 2rem;
  width: 100%;
  max-width: 28rem;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.modern-two-fa-card:hover {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(59, 130, 246, 0.2);
}

/* Logo container */
.modern-logo-container {
  width: 6rem;
  height: 6rem;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  transition: transform 0.3s ease;
}

.modern-logo-container:hover {
  transform: scale(1.05);
}

.modern-logo-container img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: brightness(0) invert(1);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #ddd6fe, #a5b4fc, #7dd3fc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Benefit cards */
.benefit-card {
  background: rgba(15, 23, 42, 0.3);
  border: 1px solid rgba(71, 85, 105, 0.3);
  border-radius: 0.75rem;
  padding: 1rem;
  transition: all 0.3s ease;
}

.benefit-card:hover {
  border-color: rgba(71, 85, 105, 0.5);
  background: rgba(15, 23, 42, 0.5);
}

.benefit-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* Modern buttons */
.modern-btn-primary {
  width: 100%;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1.125rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}

.modern-btn-primary:hover {
  transform: scale(1.02);
  box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.4);
}

.modern-btn-secondary {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: rgba(51, 65, 85, 0.5);
  color: #cbd5e1;
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.modern-btn-secondary:hover {
  background: rgba(51, 65, 85, 0.7);
  border-color: rgba(71, 85, 105, 0.7);
  color: white;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Legacy styles for backward compatibility */
.two-fa-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #cbdae9;
}

.two-fa-card {
  background: #ffffff;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 450px;
}

.two-fa-logo {
  width: 100px;
  margin-bottom: 20px;
}
  
  .two-fa-main-heading {
    font-size: 26px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }
  
  .two-fa-sub-heading {
    font-size: 16px;
    color: #555;
    margin-bottom: 20px;
  }

  .enable-two-fa{
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .two-fa-benefits-list {
    list-style: none;
    text-align: left;
    margin-bottom: 30px;
  }
  
  .two-fa-benefits-list li {
    font-size: 14px;
    color: #444;
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
  }
  
  .two-fa-benefits-list li::before {
    content: "✔️";
    position: absolute;
    left: 0;
  }
  
  .two-fa-enable-btn {
    background-color: #007bff;
    color: #fff;
    padding: 14px 25px;
    font-size: 16px;
    font-weight: bold;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
  }
  
  .two-fa-enable-btn:hover {
    background-color: #0056b3;
  }


  .two-factor-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f4f4f4;
  }
  
  .message-box {
    padding: 2rem;
    border: 1px solid #ddd;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
  }
  
  .go-back-btn {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .go-back-btn:hover {
    background-color: #0056b3;
  }
  
  