import React from "react";
import { FaTerminal, FaEye } from "react-icons/fa";

const BottomStatusBar = ({
  currentFile,
  layout,
  onToggleConsole,
  onOpenPreview,
  onSubmitSolution
}) => {
  return (
    <div className="bg-gray-800 border-t border-gray-700 px-4 py-2 flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <span className="text-xs text-gray-400">
          {currentFile ? `${currentFile.name} • ${currentFile.language}` : 'No file selected'}
        </span>
        {currentFile && !currentFile.saved && (
          <span className="text-xs text-orange-400">• Unsaved changes</span>
        )}
      </div>
      
      <div className="flex items-center space-x-2">
        <button
          onClick={onToggleConsole}
          className={`px-3 py-1 text-xs rounded transition-colors ${
            layout.showConsole 
              ? 'bg-yellow-600 text-white' 
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
          }`}
        >
          <FaTerminal className="inline mr-1" />
          Console
        </button>
        
        <button
          onClick={onOpenPreview}
          className="px-3 py-1 text-xs rounded transition-colors bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
          title="Quick Preview (also available in top toolbar)"
        >
          <FaEye className="inline mr-1" />
          Quick Preview
        </button>
        
        <button 
          onClick={onSubmitSolution}
          className="px-4 py-1 bg-gradient-to-r from-green-500 to-blue-500 text-white text-xs rounded font-medium hover:from-green-600 hover:to-blue-600 transition-all duration-300"
        >
          Submit Solution
        </button>
      </div>
    </div>
  );
};

export default BottomStatusBar;
