const DeleteModal = ({ isOpen, onClose, onConfirm, title, message }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-70 flex items-center justify-center backdrop-blur-sm">
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gradient-to-br from-white to-gray-200 p-6 rounded-lg text-center w-96 shadow-lg border-2 border-black border-opacity-10 animate-modal-pop">
        <p className="text-blue-500 font-bold text-2xl drop-shadow-md">
          {title}
        </p>
        <p className="text-gray-700 mt-2">{message}</p>

        <div className="mt-5 flex justify-between gap-5">
          <button
            className="px-6 py-3 text-lg font-bold rounded-md uppercase tracking-wider transition-all duration-300 shadow-md bg-gradient-to-br from-red-600 to-red-800 text-white border-2 border-red-700 shadow-red-500/40 hover:from-red-800 hover:to-red-900 hover:scale-105 hover:shadow-red-500/50"
            onClick={onConfirm}
          >
            Yes
          </button>
          <button
            className="px-6 py-3 text-lg font-bold rounded-md uppercase tracking-wider transition-all duration-300 shadow-md bg-gradient-to-br from-gray-600 to-gray-800 text-white border-2 border-gray-700 shadow-black/30 hover:from-gray-800 hover:to-black hover:scale-105 hover:shadow-black/40"
            onClick={onClose}
          >
            No
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteModal;
