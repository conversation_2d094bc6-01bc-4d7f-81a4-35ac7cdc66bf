const pythonCaseStudies = [
  {
    title: "Case Study 1: Linear Regression",
    objective: "Understand the basics of linear regression and implement it in Python.",
    scenario: "Write a Python script to perform simple linear regression using a dataset of house prices and square footage. Use libraries like scikit-learn to train the model and predict prices.",
    keyConcepts: ["Linear Regression", "Training and Testing Data", "Model Prediction", "Mean Squared Error"],
    solution: `import numpy as np
from sklearn.linear_model import LinearRegression

X = np.array([[1200], [1500], [1800], [2000], [2200]])  
y = np.array([200000, 250000, 300000, 350000, 400000])  

model = LinearRegression()
model.fit(X, y)

new_square_footage = np.array([[1600]])
predicted_price = model.predict(new_square_footage)
print(f"Predicted price: {predicted_price}")`
  },
  // Add more Python case studies here as needed
];

export default pythonCaseStudies;
