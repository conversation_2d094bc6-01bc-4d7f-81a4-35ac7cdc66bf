import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaEye, FaExpand, FaTimes } from "react-icons/fa";

const PreviewModal = ({
  isOpen,
  onClose,
  currentFile,
  generatePreviewContent,
  addConsoleMessage
}) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-lg shadow-2xl w-full max-w-6xl h-full max-h-[90vh] flex flex-col"
          >
            {/* Modal Header */}
            <div className="bg-gray-800 text-white px-6 py-4 rounded-t-lg flex items-center justify-between border-b">
              <h3 className="text-lg font-semibold flex items-center">
                <FaEye className="mr-2 text-green-400" />
                Live Preview - MERN Stack Application
              </h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => {
                    const previewContent = generatePreviewContent();
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(previewContent);
                    newWindow.document.close();
                  }}
                  className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors"
                  title="Open in New Window"
                >
                  <FaExpand className="inline mr-1" />
                  Pop Out
                </button>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-700 rounded transition-colors"
                  title="Close Preview"
                >
                  <FaTimes className="text-gray-400 hover:text-white" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="flex-1 bg-gray-100 relative overflow-hidden">
              {/* Preview Controls */}
              <div className="absolute top-0 left-0 right-0 bg-gray-200 px-4 py-2 border-b flex items-center justify-between z-10">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">
                    🌐 localhost:3000
                  </span>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        // Refresh preview
                        const iframe = document.getElementById('preview-iframe');
                        if (iframe) {
                          iframe.src = iframe.src;
                        }
                      }}
                      className="px-2 py-1 bg-gray-300 hover:bg-gray-400 text-gray-700 text-xs rounded transition-colors"
                    >
                      🔄 Refresh
                    </button>
                    <span className="text-xs text-gray-500">
                      Auto-refresh: ON
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">
                    {currentFile ? `Editing: ${currentFile.name}` : 'No file selected'}
                  </span>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Live Preview Active"></div>
                </div>
              </div>

              {/* Preview Iframe */}
              <div className="pt-12 h-full">
                <iframe
                  id="preview-iframe"
                  srcDoc={generatePreviewContent()}
                  className="w-full h-full border-none bg-white"
                  title="Live Preview"
                  sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                />
              </div>
            </div>

            {/* Modal Footer */}
            <div className="bg-gray-800 text-white px-6 py-3 rounded-b-lg flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-300">
                  ✅ Live preview is running
                </span>
                <span className="text-xs text-gray-400">
                  Changes will appear automatically
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => {
                    addConsoleMessage('info', 'Preview refreshed manually');
                    const iframe = document.getElementById('preview-iframe');
                    if (iframe) {
                      iframe.contentWindow.location.reload();
                    }
                  }}
                  className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition-colors"
                >
                  🔄 Refresh Preview
                </button>
                <button
                  onClick={onClose}
                  className="px-4 py-1 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default PreviewModal;
