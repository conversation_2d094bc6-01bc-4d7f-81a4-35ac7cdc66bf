import React from "react";
import { motion } from "framer-motion";

const JavaFullStackLabEnvironment = ({ onBackToCourse, showPremiumOverlay }) => {
  const projectExamples = [
    {
      title: "Spring Boot Blog",
      difficulty: "Beginner",
      description: "Build a blog with Spring Boot, JPA, and Thymeleaf",
      tags: ["Spring Boot", "JPA", "Thymeleaf"]
    },
    {
      title: "E-Commerce Platform",
      difficulty: "Intermediate",
      description: "Create a full-featured online store with Spring and React",
      tags: ["Spring Boot", "React", "MySQL"]
    },
    {
      title: "RESTful API Service",
      difficulty: "Intermediate",
      description: "Develop a robust API with Spring Boot and Spring Security",
      tags: ["REST API", "Spring Security", "JWT"]
    },
    {
      title: "Microservices Architecture",
      difficulty: "Advanced",
      description: "Build a system of microservices with Spring Cloud",
      tags: ["Microservices", "Spring Cloud", "Docker"]
    }
  ];

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-900/70 to-red-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">Java Full Stack Lab</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Hands-on Java Full Stack Projects</h3>
          <p className="text-gray-300">
            Apply your knowledge by building real-world full stack applications with Java. 
            Each project includes step-by-step guidance, code examples, and best practices.
          </p>
        </div>
        
        {/* Project Examples */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {projectExamples.map((project, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="border border-gray-700/30 bg-gray-800/30 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={showPremiumOverlay}
            >
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-white">{project.title}</h4>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  project.difficulty === "Beginner" ? "bg-green-900/50 text-green-300 border border-green-700/30" :
                  project.difficulty === "Intermediate" ? "bg-yellow-900/50 text-yellow-300 border border-yellow-700/30" :
                  "bg-red-900/50 text-red-300 border border-red-700/30"
                }`}>
                  {project.difficulty}
                </span>
              </div>
              <p className="text-sm text-gray-300 mb-3">{project.description}</p>
              <div className="flex flex-wrap gap-2">
                {project.tags.map((tag, idx) => (
                  <span key={idx} className="px-2 py-1 bg-orange-900/30 text-orange-300 text-xs rounded-md border border-orange-700/30">
                    {tag}
                  </span>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* Development Environment */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-8 bg-gray-900/50 border border-gray-700/30 rounded-lg p-4"
        >
          <h3 className="text-lg font-semibold text-white mb-4">Java Full Stack Development Environment</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="bg-gray-900 border border-gray-700 rounded-lg p-4">
              <h4 className="font-medium text-white mb-2">Backend Setup</h4>
              <div className="font-mono text-sm text-orange-300">
                <div>// Create Spring Boot project</div>
                <div>// Using Spring Initializr</div>
                <div>// Add dependencies:</div>
                <div>// - Spring Web</div>
                <div>// - Spring Data JPA</div>
                <div>// - Spring Security</div>
              </div>
            </div>
            <div className="bg-gray-900 border border-gray-700 rounded-lg p-4">
              <h4 className="font-medium text-white mb-2">Frontend Setup</h4>
              <div className="font-mono text-sm text-blue-300">
                <div># Create React app</div>
                <div>npx create-react-app frontend</div>
                <div>cd frontend</div>
                <div># Install dependencies</div>
                <div>npm install axios react-router-dom</div>
              </div>
            </div>
          </div>
          <button 
            onClick={showPremiumOverlay}
            className="px-4 py-2 bg-orange-600 text-white rounded-lg font-medium hover:bg-orange-700 transition-colors"
          >
            Access Development Environment
          </button>
        </motion.div>
      </div>
    </div>
  );
};

export default JavaFullStackLabEnvironment;