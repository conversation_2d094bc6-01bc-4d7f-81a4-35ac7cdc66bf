import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const useCourseModal = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const location = useLocation();

  // Close modal when route changes
  useEffect(() => {
    setIsModalOpen(false);
  }, [location.pathname]);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return {
    isModalOpen,
    openModal,
    closeModal
  };
};

export default useCourseModal;