// Simple mock review service for development
const mockReviews = [
  {
    id: 1,
    courseId: 'javascript-course',
    user: 'Alice',
    rating: 5,
    comment: 'Great course! Learned a lot.',
    date: '2025-07-01',
  },
  {
    id: 2,
    courseId: 'javascript-course',
    user: 'Bob',
    rating: 4,
    comment: 'Challenging and fun.',
    date: '2025-07-10',
  },
];

function getCourseReviews(courseId, newestFirst = true) {
  const reviews = mockReviews.filter(r => r.courseId === courseId);
  return newestFirst ? [...reviews].sort((a, b) => new Date(b.date) - new Date(a.date)) : reviews;
}

function getCourseStats(courseId) {
  const reviews = mockReviews.filter(r => r.courseId === courseId);
  const totalReviews = reviews.length;
  const averageRating = totalReviews ? (reviews.reduce((sum, r) => sum + r.rating, 0) / totalReviews).toFixed(1) : 0;
  return {
    totalReviews,
    averageRating,
  };
}

export default {
  getCourseReviews,
  getCourseStats,
};
