import React from "react";
import TopMenuBar from "./lab-components/TopMenuBar";
import InstructionsBanner from "./lab-components/InstructionsBanner";
import Sidebar from "./lab-components/Sidebar";
import EditorArea from "./lab-components/EditorArea";
import ConsolePanel from "./lab-components/ConsolePanel";
import BottomStatusBar from "./lab-components/BottomStatusBar";
import PreviewModal from "./lab-components/PreviewModal";
import { useLabEnvironment } from "./lab-components/useLabEnvironment";

const MernFullStackLabEnvironment = ({ onBackToCourse, showPremiumOverlay }) => {
  const {
    // State
    theme,
    selectedChallenge,
    files,
    activeFileId,
    openTabs,
    layout,
    isPreviewModalOpen,
    consoleOutput,
    challenges,
    
    // Functions
    addConsoleMessage,
    openPreviewModal,
    closePreviewModal,
    createNewFile,
    deleteFile,
    renameFile,
    saveAllFiles,
    clearConsole,
    toggleTheme,
    handleEditorDidMount,
    handleEditorChange,
    togglePanel,
    runCode,
    handleChallengeChange,
    handleTabClose,
    generatePreview,
    getFileIcon,
    setActiveFileId
  } = useLabEnvironment();

  const currentFile = files[activeFileId];

  return (
    <div className="bg-gray-900 text-white min-h-screen flex flex-col">
      {/* Top Menu Bar */}
      <TopMenuBar
        onBackToCourse={onBackToCourse}
        selectedChallenge={selectedChallenge}
        challenges={challenges}
        onChallengeChange={handleChallengeChange}
        theme={theme}
        onToggleTheme={toggleTheme}
        onSaveAllFiles={saveAllFiles}
        onRunCode={runCode}
        onOpenPreview={openPreviewModal}
        onToggleSidebar={() => togglePanel('showSidebar')}
      />

      {/* Instructions Banner */}
      <InstructionsBanner onToggleSidebar={() => togglePanel('showSidebar')} />

      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        {layout.showSidebar && (
          <Sidebar
            files={files}
            activeFileId={activeFileId}
            onFileSelect={setActiveFileId}
            onCreateNewFile={createNewFile}
            onRenameFile={renameFile}
            onDeleteFile={deleteFile}
            getFileIcon={getFileIcon}
          />
        )}

        {/* Editor Area */}
        <EditorArea
          files={files}
          activeFileId={activeFileId}
          openTabs={openTabs}
          theme={theme}
          onFileSelect={setActiveFileId}
          onTabClose={handleTabClose}
          onEditorChange={handleEditorChange}
          onEditorMount={handleEditorDidMount}
          getFileIcon={getFileIcon}
        />
      </div>

      {/* Console Panel */}
      <ConsolePanel
        consoleOutput={consoleOutput}
        layout={layout}
        onClearConsole={clearConsole}
        onToggleConsole={() => togglePanel('showConsole')}
      />

      {/* Bottom Status Bar */}
      <BottomStatusBar
        currentFile={currentFile}
        layout={layout}
        onToggleConsole={() => togglePanel('showConsole')}
        onOpenPreview={openPreviewModal}
        onSubmitSolution={showPremiumOverlay}
      />

      {/* Preview Modal */}
      <PreviewModal
        isOpen={isPreviewModalOpen}
        onClose={closePreviewModal}
        currentFile={currentFile}
        generatePreviewContent={generatePreview}
        addConsoleMessage={addConsoleMessage}
      />
    </div>
  );
};

export default MernFullStackLabEnvironment;
