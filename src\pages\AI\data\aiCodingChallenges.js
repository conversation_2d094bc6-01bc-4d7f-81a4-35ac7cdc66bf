const aiCodingChallenges = [
  {
    id: "ai-challenge-1",
    title: "Simple Neural Network",
    difficulty: "Beginner",
    description: "Create a simple neural network with TensorFlow/Keras.",
    requirements: [
      "Import the necessary libraries",
      "Create a sequential model",
      "Add layers to the model",
      "Compile the model"
    ],
    template: `# Simple Neural Network Challenge
# Create a basic neural network model using TensorFlow/Keras

import tensorflow as tf
from tensorflow import keras

# TODO: Create a sequential model

# TODO: Add a dense layer with 128 neurons and ReLU activation, input shape of (784,)

# TODO: Add another dense layer with 64 neurons and ReLU activation

# TODO: Add an output layer with 10 neurons and softmax activation

# TODO: Compile the model with appropriate loss function, optimizer, and metrics

# Print model summary
`
  },
  {
    id: "ai-challenge-2",
    title: "Sentiment Analysis",
    difficulty: "Intermediate",
    description: "Implement a basic sentiment analysis function using NLTK.",
    requirements: [
      "Process text input",
      "Analyze sentiment using NLTK",
      "Return sentiment score and label"
    ],
    template: `# Sentiment Analysis Challenge
# Implement a function to analyze sentiment of text input

import nltk
from nltk.sentiment import SentimentIntensityAnalyzer

# You might need to download these resources
# nltk.download('vader_lexicon')

def analyze_sentiment(text):
    # TODO: Initialize the NLTK sentiment analyzer
    
    # TODO: Get sentiment scores for the input text
    
    # TODO: Determine if sentiment is positive, negative, or neutral
    
    # TODO: Return a dictionary with scores and sentiment label
    
    pass

# Test your function
test_text = "I really enjoyed this course. The content was excellent!"
result = analyze_sentiment(test_text)
print(result)
`
  },
  {
    id: "ai-challenge-3",
    title: "Image Classification",
    difficulty: "Advanced",
    description: "Build a CNN model for image classification using TensorFlow.",
    requirements: [
      "Create a CNN architecture",
      "Add convolutional layers",
      "Add pooling layers",
      "Add fully connected layers",
      "Compile the model"
    ],
    template: `# Image Classification Challenge
# Build a CNN for image classification

import tensorflow as tf
from tensorflow.keras import layers, models

def create_cnn_model(input_shape=(28, 28, 1), num_classes=10):
    # TODO: Initialize a sequential model
    
    # TODO: Add first convolutional layer with 32 filters, 3x3 kernel, and ReLU activation
    
    # TODO: Add max pooling layer
    
    # TODO: Add second convolutional layer with 64 filters, 3x3 kernel, and ReLU activation
    
    # TODO: Add another max pooling layer
    
    # TODO: Flatten the output
    
    # TODO: Add a dense layer with 128 neurons and ReLU activation
    
    # TODO: Add output layer with appropriate number of neurons and activation
    
    # TODO: Compile the model
    
    return model

# Create and display the model
model = create_cnn_model()
model.summary()
`
  },
  {
    id: "ai-challenge-4",
    title: "Text Generation with RNN",
    difficulty: "Advanced",
    description: "Implement a simple character-level text generation model using RNNs.",
    requirements: [
      "Create a character-level RNN model",
      "Process text data",
      "Build LSTM layers",
      "Generate text from the model"
    ],
    template: `# Text Generation Challenge
# Create a character-level RNN for text generation

import tensorflow as tf
import numpy as np
from tensorflow.keras import layers

def build_text_generation_model(vocab_size, embedding_dim=256, rnn_units=1024):
    # TODO: Create a sequential model
    
    # TODO: Add an embedding layer
    
    # TODO: Add an LSTM layer
    
    # TODO: Add a dense output layer with vocab_size units
    
    # TODO: Return the model
    
    pass

# Example usage:
vocab_size = 65  # Example: ASCII printable chars
model = build_text_generation_model(vocab_size)
model.summary()
`
  }
];

export default aiCodingChallenges;