const aiCaseStudies = [
  {
    title: "Case Study 1: Neural Networks",
    objective: "Learn the basics of neural networks and deep learning.",
    scenario: "Implement a simple neural network using TensorFlow to classify handwritten digits from the MNIST dataset.",
    keyConcepts: ["Neural Networks", "Deep Learning", "TensorFlow", "MNIST"],
    solution: `import tensorflow as tf
from tensorflow.keras import layers, models

# Load and preprocess MNIST data
mnist = tf.keras.datasets.mnist
(x_train, y_train), (x_test, y_test) = mnist.load_data()
x_train, x_test = x_train / 255.0, x_test / 255.0

# Create the model
model = models.Sequential([
    layers.Flatten(input_shape=(28, 28)),
    layers.Dense(128, activation='relu'),
    layers.Dropout(0.2),
    layers.Dense(10, activation='softmax')
])

model.compile(optimizer='adam',
              loss='sparse_categorical_crossentropy',
              metrics=['accuracy'])

model.fit(x_train, y_train, epochs=5)
model.evaluate(x_test, y_test)`
  },
  // Add more AI case studies here as needed
];

export default aiCaseStudies;
