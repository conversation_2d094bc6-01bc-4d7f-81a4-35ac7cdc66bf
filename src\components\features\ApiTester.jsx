import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { useApiTester } from '../../hooks/useApiTester';
import ReusableNavbar from '../layout/ReusableNavbar';

const ApiTester = () => {
  const {
    method,
    url,
    headers,
    requestBody,
    response,
    isLoading,
    theme,
    methodsWithBody,
    setMethod,
    setUrl,
    setHeaders,
    setRequestBody,
    sendRequest,
    cancelRequest,
    clearResponse,
    resetForm,
    toggleTheme,
    saveRequest,
    loadRequest,
    getSavedRequests
  } = useApiTester();

  const [showHistory, setShowHistory] = useState(false);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'Enter':
            e.preventDefault();
            if (!isLoading) sendRequest();
            break;
          case 'k':
            e.preventDefault();
            clearResponse();
            break;
          case 'r':
            e.preventDefault();
            resetForm();
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [sendRequest, clearResponse, resetForm, isLoading]);

  const httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];

  const getMethodColor = (methodName) => {
    const colors = {
      GET: 'bg-green-500 hover:bg-green-600',
      POST: 'bg-blue-500 hover:bg-blue-600',
      PUT: 'bg-yellow-500 hover:bg-yellow-600',
      DELETE: 'bg-red-500 hover:bg-red-600',
      PATCH: 'bg-purple-500 hover:bg-purple-600'
    };
    return colors[methodName] || 'bg-gray-500 hover:bg-gray-600';
  };

  const getStatusColor = (status) => {
    if (status >= 200 && status < 300) return 'text-green-500';
    if (status >= 300 && status < 400) return 'text-yellow-500';
    if (status >= 400 && status < 500) return 'text-orange-500';
    if (status >= 500) return 'text-red-500';
    return 'text-gray-500';
  };

  const formatJson = (obj) => {
    try {
      return JSON.stringify(obj, null, 2);
    } catch {
      return obj;
    }
  };

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* Navbar */}
      <ReusableNavbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <Link to="/" className={`${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'} hover:underline flex items-center gap-1 mb-2`}>
                <span>← Back to Home</span>
              </Link>
              <h1 className={`text-3xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                API Tester
              </h1>
              <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
                Test and debug your APIs with ease
              </p>
              <div className={`text-xs mt-2 ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                Shortcuts: Ctrl+Enter (Send), Ctrl+K (Clear), Ctrl+R (Reset)
              </div>
              <details className={`text-xs mt-2 ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                <summary className="cursor-pointer">Troubleshooting Tips</summary>
                <div className="mt-2 space-y-1">
                  <div>• CORS errors: Use APIs that support cross-origin requests</div>
                  <div>• Network errors: Check your internet connection</div>
                  <div>• JSON errors: Validate your headers and request body format</div>
                  <div>• Timeout: Requests timeout after 30 seconds</div>
                </div>
              </details>
            </div>
            
            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className={`p-2 rounded-lg transition-colors ${
                theme === 'dark' 
                  ? 'bg-gray-800 hover:bg-gray-700 text-yellow-400' 
                  : 'bg-white hover:bg-gray-100 text-gray-600 shadow-md'
              }`}
              title="Toggle Theme"
            >
              {theme === 'dark' ? '☀️' : '🌙'}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          {/* Request Panel */}
          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-xl shadow-lg p-6`}
          >
            <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Request
            </h2>

            {/* Method and URL */}
            <div className="flex gap-2 mb-4">
              <select
                value={method}
                onChange={(e) => setMethod(e.target.value)}
                className={`px-3 py-2 rounded-lg border font-semibold min-w-[100px] ${
                  theme === 'dark'
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              >
                {httpMethods.map(m => (
                  <option key={m} value={m}>{m}</option>
                ))}
              </select>
              
              <input
                type="text"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="https://jsonplaceholder.typicode.com/posts"
                className={`flex-1 px-3 py-2 rounded-lg border ${
                  theme === 'dark'
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              />
            </div>

            {/* Quick Examples */}
            <div className="mb-4">
              <label className={`block text-sm font-medium mb-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                Quick Examples
              </label>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => {
                    setMethod('GET');
                    setUrl('https://jsonplaceholder.typicode.com/posts/1');
                    setHeaders('{\n  "Accept": "application/json"\n}');
                  }}
                  className={`px-3 py-1 text-xs rounded-lg border transition-colors ${
                    theme === 'dark'
                      ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  GET Post
                </button>
                <button
                  onClick={() => {
                    setMethod('POST');
                    setUrl('https://jsonplaceholder.typicode.com/posts');
                    setHeaders('{\n  "Content-Type": "application/json",\n  "Accept": "application/json"\n}');
                    setRequestBody('{\n  "title": "Test Post from API Tester",\n  "body": "This is a test post created using the API Tester",\n  "userId": 1\n}');
                  }}
                  className={`px-3 py-1 text-xs rounded-lg border transition-colors ${
                    theme === 'dark'
                      ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  POST Example
                </button>
                <button
                  onClick={() => {
                    setMethod('GET');
                    setUrl('https://httpbin.org/get');
                    setHeaders('{\n  "Accept": "application/json"\n}');
                  }}
                  className={`px-3 py-1 text-xs rounded-lg border transition-colors ${
                    theme === 'dark'
                      ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  HTTPBin GET
                </button>
                <button
                  onClick={() => {
                    setMethod('GET');
                    setUrl('https://reqres.in/api/users/2');
                    setHeaders('{\n  "Accept": "application/json"\n}');
                  }}
                  className={`px-3 py-1 text-xs rounded-lg border transition-colors ${
                    theme === 'dark'
                      ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  ReqRes API
                </button>
                <button
                  onClick={() => {
                    setMethod('GET');
                    setUrl('https://httpbin.org/delay/5');
                    setHeaders('{\n  "Accept": "application/json"\n}');
                  }}
                  className={`px-3 py-1 text-xs rounded-lg border transition-colors ${
                    theme === 'dark'
                      ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Slow Response (5s)
                </button>
              </div>
            </div>

            {/* Headers */}
            <div className="mb-4">
              <label className={`block text-sm font-medium mb-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                Headers (JSON)
              </label>
              <textarea
                value={headers}
                onChange={(e) => setHeaders(e.target.value)}
                rows={4}
                className={`w-full px-3 py-2 rounded-lg border font-mono text-sm ${
                  theme === 'dark'
                    ? 'bg-gray-700 border-gray-600 text-green-400 placeholder-gray-400'
                    : 'bg-gray-50 border-gray-300 text-green-600 placeholder-gray-500'
                } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                placeholder='{\n  "Content-Type": "application/json",\n  "Authorization": "Bearer token"\n}'
              />
            </div>

            {/* Request Body (for POST, PUT, PATCH) */}
            {methodsWithBody.includes(method) && (
              <div className="mb-4">
                <label className={`block text-sm font-medium mb-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                  Request Body (JSON)
                </label>
                <textarea
                  value={requestBody}
                  onChange={(e) => setRequestBody(e.target.value)}
                  rows={6}
                  className={`w-full px-3 py-2 rounded-lg border font-mono text-sm ${
                    theme === 'dark'
                      ? 'bg-gray-700 border-gray-600 text-blue-400 placeholder-gray-400'
                      : 'bg-gray-50 border-gray-300 text-blue-600 placeholder-gray-500'
                  } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  placeholder='{\n  "key": "value",\n  "data": {\n    "nested": "object"\n  }\n}'
                />
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2 flex-wrap">
              {!isLoading ? (
                <button
                  onClick={sendRequest}
                  className={`px-6 py-2 rounded-lg text-white font-semibold transition-colors ${getMethodColor(method)} transform hover:scale-105`}
                >
                  Send {method}
                </button>
              ) : (
                <button
                  onClick={cancelRequest}
                  className="px-6 py-2 rounded-lg bg-red-600 hover:bg-red-700 text-white font-semibold transition-colors flex items-center gap-2"
                >
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Cancel Request
                </button>
              )}

              <button
                onClick={() => {
                  saveRequest();
                  clearResponse();
                }}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  theme === 'dark'
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Save
              </button>

              <button
                onClick={clearResponse}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  theme === 'dark'
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Clear
              </button>

              <button
                onClick={resetForm}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  theme === 'dark'
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Reset
              </button>

              <button
                onClick={() => setShowHistory(!showHistory)}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  theme === 'dark'
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                {showHistory ? 'Hide' : 'History'}
              </button>
            </div>

            {/* History Section */}
            {showHistory && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 pt-4 border-t border-gray-600"
              >
                <h3 className={`text-sm font-semibold mb-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                  Recent Requests
                </h3>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {getSavedRequests().map((req, index) => (
                    <div
                      key={index}
                      onClick={() => loadRequest(req)}
                      className={`p-2 rounded cursor-pointer transition-colors ${
                        theme === 'dark'
                          ? 'bg-gray-700 hover:bg-gray-600'
                          : 'bg-gray-50 hover:bg-gray-100'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className={`text-xs px-2 py-1 rounded font-semibold ${getMethodColor(req.method)}`}>
                            {req.method}
                          </span>
                          <span className={`text-sm truncate ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                            {req.url}
                          </span>
                        </div>
                        <span className={`text-xs ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                          {new Date(req.timestamp).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  ))}
                  {getSavedRequests().length === 0 && (
                    <div className={`text-center py-4 ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                      No saved requests yet
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </motion.div>

          {/* Response Panel */}
          <motion.div
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-xl shadow-lg p-6`}
          >
            <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Response
            </h2>

            {!response ? (
              <div className={`flex items-center justify-center h-64 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                <div className="text-center">
                  <div className="text-4xl mb-2">🚀</div>
                  <p>Send a request to see the response</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Status and Timing */}
                {response.error ? (
                  <div className={`p-3 rounded-lg ${theme === 'dark' ? 'bg-red-900/50' : 'bg-red-50'} border ${theme === 'dark' ? 'border-red-500/30' : 'border-red-200'}`}>
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-red-500 font-semibold">❌ {response.errorType || 'Error'}</span>
                      <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                        {response.duration}ms
                      </span>
                    </div>
                    <p className={`text-sm ${theme === 'dark' ? 'text-red-300' : 'text-red-700'} mb-2`}>
                      {response.error}
                    </p>
                    {response.originalError && response.originalError !== response.error && (
                      <details className={`text-xs ${theme === 'dark' ? 'text-red-400' : 'text-red-600'}`}>
                        <summary className="cursor-pointer">Technical Details</summary>
                        <p className="mt-1 font-mono">{response.originalError}</p>
                      </details>
                    )}
                  </div>
                ) : (
                  <div className={`p-3 rounded-lg ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className={`font-semibold ${getStatusColor(response.status)}`}>
                          {response.status} {response.statusText}
                        </span>
                        <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                          • {response.duration}ms
                        </span>
                      </div>
                      <span className={`text-xs ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                        {new Date(response.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                )}

                {/* Response Headers */}
                {response.headers && Object.keys(response.headers).length > 0 && (
                  <div>
                    <h3 className={`text-sm font-semibold mb-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                      Response Headers
                    </h3>
                    <div className={`p-3 rounded-lg border font-mono text-xs overflow-x-auto ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-gray-300'
                        : 'bg-gray-50 border-gray-200 text-gray-700'
                    }`}>
                      <pre>{formatJson(response.headers)}</pre>
                    </div>
                  </div>
                )}

                {/* Response Body */}
                {response.body && (
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <h3 className={`text-sm font-semibold ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                        Response Body
                      </h3>
                      {response.bodyError && (
                        <span className={`text-xs ${theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600'}`}>
                          ⚠️ {response.bodyError}
                        </span>
                      )}
                    </div>
                    <div className={`p-3 rounded-lg border font-mono text-sm overflow-x-auto max-h-96 overflow-y-auto ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-green-400'
                        : 'bg-gray-50 border-gray-200 text-green-600'
                    }`}>
                      <pre>{typeof response.body === 'string' ? response.body : formatJson(response.body)}</pre>
                    </div>
                  </div>
                )}

                {/* Debug Information */}
                {response && !response.error && (
                  <details className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                    <summary className="cursor-pointer font-medium mb-2">Debug Information</summary>
                    <div className={`p-2 rounded border font-mono ${
                      theme === 'dark' ? 'bg-gray-800 border-gray-600' : 'bg-gray-100 border-gray-200'
                    }`}>
                      <div>Response Type: {response.type}</div>
                      <div>Redirected: {response.redirected ? 'Yes' : 'No'}</div>
                      <div>Final URL: {response.url}</div>
                      <div>OK Status: {response.ok ? 'Yes' : 'No'}</div>
                    </div>
                  </details>
                )}
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ApiTester;
