import React from "react";
import CaseStudy from "./CaseStudy";
import InternshipApply from "../../components/InternshipApply";
import Chapter from "./Chapter";
import FAQS from "./FAQS";
import Introduction from "./Introduction";
import PythonLayout from "./components/PythonLayout";
import PythonHero from "./components/PythonHero";
import LabEnvironmentNew from "./components/LabEnvironmentNew";
import LiveClasses from "./components/LiveClasses";
import PremiumModal from "./components/PremiumModal";
import { CourseResourcesSection } from "../../components/ui";
import CoursePaymentSection from "../../components/ui/CoursePaymentSection";
import { ReviewManager } from "../../components/reviews";
import useSidebarState from "../../hooks/useSidebarState";

const PythonCourse = () => {
  const courseConfig = {
    title: "Python Course Resources",
    subtitle: "Select a resource category to start learning Python through theory, practical exercises, live classes, or FAQs",
    theme: {
      titleColor: "text-gray-800",
      subtitleColor: "text-gray-600"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn Python fundamentals and core concepts",
        icon: "📚",
        component: Introduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Lab Environment",
        description: "Practice coding in interactive environments",
        icon: "💻",
        component: LabEnvironmentNew,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Classes",
        description: "Join instructor-led learning sessions",
        icon: "🎥",
        component: LiveClasses,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to common questions",
        icon: "❓",
        component: FAQS,
        props: {}
      },
      {
        id: "Payment",
        title: "Enroll Now",
        description: "Choose your plan and payment method",
        icon: "💳",
        component: ({ showPremiumOverlay }) => (
          <CoursePaymentSection
            courseId="pythoncourse"
            courseName="Python Programming Course"
            showPremiumOverlay={showPremiumOverlay}
          />
        ),
        props: {}
      },
      {
        id: "Reviews",
        title: "Reviews",
        description: "See what students are saying about this course",
        icon: "⭐",
        component: () => (
          <ReviewManager
            courseId="pythoncourse"
            courseName="Python Master Course"
            showWriteReview={true}
            showStats={true}
          />
        ),
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={PythonHero}
      LayoutComponent={PythonLayout}
      PremiumModalComponent={PremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default PythonCourse;
