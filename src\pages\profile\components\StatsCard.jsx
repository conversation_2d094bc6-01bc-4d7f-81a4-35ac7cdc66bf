const StatsCard = ({ icon: IconComponent, title, value, subtitle, gradientFrom, gradientTo, iconFrom, iconTo }) => {
  return (
    <div className={`bg-gradient-to-br ${gradientFrom} ${gradientTo} backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-opacity-30 hover:border-opacity-50 transition-all duration-500 group`}>
      <div className="flex items-center justify-between mb-4">
        <div className={`w-12 h-12 bg-gradient-to-r ${iconFrom} ${iconTo} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
          <IconComponent className="text-white text-xl" />
        </div>
        <span className="text-opacity-80 text-sm font-semibold" style={{ color: 'inherit' }}>{title}</span>
      </div>
      <div className="text-3xl font-bold text-white mb-1">{value}</div>
      <div className="text-opacity-75 text-sm" style={{ color: 'inherit' }}>{subtitle}</div>
    </div>
  );
};

export default StatsCard;
