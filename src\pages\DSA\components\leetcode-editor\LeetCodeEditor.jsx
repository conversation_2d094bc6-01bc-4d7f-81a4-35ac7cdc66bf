import React, { useState, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import ProblemPanel from "./ProblemPanel";
import CodeEditorPanel from "./CodeEditorPanel";
import ResizablePanel from "./ResizablePanel";
import { useLeetCodeEditor } from "./hooks/useLeetCodeEditor";

const LeetCodeEditor = ({ onBackToCourse, showPremiumOverlay }) => {
  const {
    selectedProblem,
    selectedLanguage,
    code,
    customInput,
    output,
    testResults,
    isRunning,
    isSubmitting,
    theme,
    activeTab,
    problems,
    languages,
    setSelectedProblem,
    setSelectedLanguage,
    setCode,
    setCustomInput,
    setActiveTab,
    toggleTheme,
    runCode,
    submitCode,
    resetCode
  } = useLeetCodeEditor();

  const [panelWidth, setPanelWidth] = useState(50); // Percentage
  const [isMobile, setIsMobile] = useState(false);

  // Check for mobile screen size
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <div className={`min-h-screen flex flex-col ${theme === 'dark' ? 'bg-gray-900' : 'bg-white'}`}>
      {/* Header */}
      <div className={`border-b ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'} px-4 py-3`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button 
              onClick={onBackToCourse}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                theme === 'dark' 
                  ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
              }`}
            >
              ← Back to Course
            </button>
            <h1 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              DSA Practice Lab
            </h1>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Problem Selector */}
            <select
              value={selectedProblem?.id || ''}
              onChange={(e) => {
                const problem = problems.find(p => p.id === e.target.value);
                setSelectedProblem(problem);
              }}
              className={`px-3 py-1 rounded text-sm border ${
                theme === 'dark' 
                  ? 'bg-gray-700 border-gray-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value="">Select Problem</option>
              {problems.map(problem => (
                <option key={problem.id} value={problem.id}>
                  {problem.title} ({problem.difficulty})
                </option>
              ))}
            </select>

            {/* API Tester Button */}
            <Link
              to="/api-tester"
              className={`px-3 py-2 rounded text-sm font-medium transition-colors ${
                theme === 'dark'
                  ? 'bg-green-600 hover:bg-green-700 text-white'
                  : 'bg-green-600 hover:bg-green-700 text-white'
              }`}
              title="API Tester"
            >
              API Tester
            </Link>

            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className={`p-2 rounded transition-colors ${
                theme === 'dark' 
                  ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400' 
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-600'
              }`}
              title="Toggle Theme"
            >
              {theme === 'dark' ? '☀️' : '🌙'}
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className={`flex-1 flex overflow-hidden ${isMobile ? 'flex-col' : 'flex-row'}`}>
        {/* Problem Panel */}
        {isMobile ? (
          <div className={`${theme === 'dark' ? 'border-b border-gray-700' : 'border-b border-gray-300'}`} style={{ height: '40vh' }}>
            <ProblemPanel
              problem={selectedProblem}
              theme={theme}
              showPremiumOverlay={showPremiumOverlay}
            />
          </div>
        ) : (
          <ResizablePanel
            initialWidth={panelWidth}
            minWidth={20}
            maxWidth={80}
            onResize={setPanelWidth}
            className={`border-r ${theme === 'dark' ? 'border-gray-700' : 'border-gray-300'}`}
          >
            <ProblemPanel
              problem={selectedProblem}
              theme={theme}
              showPremiumOverlay={showPremiumOverlay}
            />
          </ResizablePanel>
        )}

        {/* Code Editor Panel */}
        <div className="flex-1 flex flex-col">
          <CodeEditorPanel
            selectedLanguage={selectedLanguage}
            languages={languages}
            code={code}
            customInput={customInput}
            output={output}
            testResults={testResults}
            isRunning={isRunning}
            isSubmitting={isSubmitting}
            theme={theme}
            activeTab={activeTab}
            onLanguageChange={setSelectedLanguage}
            onCodeChange={setCode}
            onCustomInputChange={setCustomInput}
            onTabChange={setActiveTab}
            onRun={runCode}
            onSubmit={submitCode}
            onReset={resetCode}
            showPremiumOverlay={showPremiumOverlay}
          />
        </div>
      </div>
    </div>
  );
};

export default LeetCodeEditor;
