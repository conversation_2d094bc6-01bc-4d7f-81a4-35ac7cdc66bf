/********** Template CSS **********/

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for MERN Lab */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.group:hover .group-hover\\:spin {
  animation: spin-slow 2s linear infinite;
}

/* Standardized Button Styles */
[class*="btn-"] {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition-property: all;
  transition-duration: 200ms;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 40px;
  min-width: 100px;
  cursor: pointer;
  border: none;
  outline: none;
}

.btn-primary {
  background-image: linear-gradient(to right, #2563eb, #7c3aed);
  color: white;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}
.btn-primary:hover {
  background-image: linear-gradient(to right, #1d4ed8, #6d28d9);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.btn-secondary {
  background-image: linear-gradient(to right, #1f2937, #1a1c24);
  color: white;
  border: 1px solid #4b5563;
}
.btn-secondary:hover {
  background-image: linear-gradient(to right, #111827, #0f1117);
}

.btn-danger {
  background-image: linear-gradient(to right, #dc2626, #b91c1c);
  color: white;
}
.btn-danger:hover {
  background-image: linear-gradient(to right, #b91c1c, #991b1b);
}

.btn-success {
  background-image: linear-gradient(to right, #059669, #047857);
  color: white;
}
.btn-success:hover {
  background-image: linear-gradient(to right, #047857, #065f46);
}

/* Button Sizes */
.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  min-height: 32px;
  min-width: 80px;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  min-height: 48px;
  min-width: 120px;
}

/* Social Login Buttons */
.btn-social {
  background-color: white;
  color: #374151;
  border: 1px solid #d1d5db;
}
.btn-social:hover {
  background-color: #f9fafb;
}

/* Action Buttons */
.btn-action {
  background-image: linear-gradient(to right, #3b82f6, #8b5cf6);
  color: white;
}
.btn-action:hover {
  background-image: linear-gradient(to right, #2563eb, #7c3aed);
}

/* Floating Action Button */
.btn-float {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  padding: 1rem;
  border-radius: 9999px;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  z-index: 50;
  background-image: linear-gradient(to right, #2563eb, #7c3aed);
  color: white;
}
.btn-float:hover {
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  background-image: linear-gradient(to right, #1d4ed8, #6d28d9);
}

/* General Sidebar Styling */
.sidebar {
  overflow: hidden;
  transition: all 0.3s;
}

/* Mobile responsiveness improvements */
@media (max-width: 768px) {
  .navbar-brand h4 {
      font-size: 16px; /* Adjust text size for smaller screens */
      display: flex;
      align-items: center;
  }

  .navbar-brand img {
      width: 20px; /* Adjust logo size for smaller screens */
      height: 20px;
  }

  .sidebar {
      width: 100%; /* Expand sidebar to full width on mobile */
      max-width: 280px; /* But don't exceed sidebar width */
  }

  /* Ensure mobile content doesn't get cut off */
  .main-content {
    padding: 1rem;
    margin: 0;
  }

  /* Fix any overlapping issues */
  .fixed {
    position: fixed !important;
  }
}

/*** Spinner ***/
#spinner {
  opacity: 0;
  visibility: hidden;
  transition: opacity .5s ease-out, visibility 0s linear .5s;
  z-index: 99999;
}

#spinner.show {
  transition: opacity .5s ease-out, visibility 0s linear 0s;
  visibility: visible;
  opacity: 1;
}


/*** Button ***/
.btn {
  transition: .5s;
}

.btn.btn-primary {
  color: #FFFFFF;
}

.btn-square {
  width: 38px;
  height: 38px;
}

.btn-sm-square {
  width: 32px;
  height: 32px;
}

.btn-lg-square {
  width: 48px;
  height: 48px;
}

.btn-square,
.btn-sm-square,
.btn-lg-square {
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: normal;
  border-radius: 50px;
}


/*** Layout ***/
.sidebar {
  position: fixed;
  top: 80px; /* Start below fixed navbar */
  left: 0;
  bottom: 0;
  width: 280px; /* Updated width */
  height: calc(100vh - 80px); /* Account for navbar height */
  overflow-y: auto;
  background: var(--light);
  transition: 0.5s;
  z-index: 50; /* Lower than navbar */
}


.content {
  margin-left: 280px; /* Updated to match new sidebar width */
  width: calc(100% - 280px);  /* Subtract sidebar width */
  min-height: 100vh;
  background: #FFFFFF;
  transition: 0.5s;
  padding-top: 80px; /* Account for fixed navbar */
}

.sidebar.open + .content {
  margin-left: 0;
  width: 100%; /* Make content take full width when sidebar is hidden */
}

@media (min-width: 1024px) {
  .sidebar {
      margin-left: 0;
  }

  .sidebar.open {
      margin-left: -280px; /* Updated to match new sidebar width */
  }

  .content {
      width: calc(100% - 280px); /* Updated to match new sidebar width */
      padding-top: 80px; /* Account for fixed navbar */
  }

  .content.open {
      width: 100%;
      margin-left: 0;
  }
}

@media (max-width: 1023px) {
  .sidebar {
      margin-left: -280px; /* Updated to match new sidebar width */
  }

  .sidebar.open {
      margin-left: 0;
  }

  .content {
      width: 100%;
      margin-left: 0;
      padding-top: 80px; /* Account for fixed navbar */
  }
}


/*** Navbar ***/
.sidebar .navbar .navbar-nav .nav-link {
  padding: 7px 20px;
  color: var(--dark);
  font-weight: 500;
  border-left: 3px solid var(--light);
  border-radius: 0 30px 30px 0;
  outline: none;
}

.sidebar .navbar .navbar-nav .nav-link:hover,
.sidebar .navbar .navbar-nav .nav-link.active {
  color: var(--primary);
  background: #FFFFFF;
  border-color: var(--primary);
}

.sidebar .navbar .navbar-nav .nav-link i {
  width: 40px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  border-radius: 40px;
}

.sidebar .navbar .navbar-nav .nav-link:hover i,
.sidebar .navbar .navbar-nav .nav-link.active i {
  background: var(--light);
}

.sidebar .navbar .dropdown-toggle::after {
  position: absolute;
  top: 15px;
  right: 15px;
  border: none;
  content: "\f107";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  transition: .5s;
}

.sidebar .navbar .dropdown-toggle[aria-expanded=true]::after {
  transform: rotate(-180deg);
}

.sidebar .navbar .dropdown-item {
  padding-left: 25px;
  border-radius: 0 30px 30px 0;
}

.content .navbar .navbar-nav .nav-link {
  margin-left: 25px;
  padding: 12px 0;
  color: var(--dark);
  outline: none;
}

.content .navbar .navbar-nav .nav-link:hover,
.content .navbar .navbar-nav .nav-link.active {
  color: var(--primary);
}

.content .navbar .sidebar-toggler,
.content .navbar .navbar-nav .nav-link i {
  width: 40px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  border-radius: 40px;
}

.content .navbar .dropdown-toggle::after {
  margin-left: 6px;
  vertical-align: middle;
  border: none;
  content: "\f107";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  transition: .5s;
}

.content .navbar .dropdown-toggle[aria-expanded=true]::after {
  transform: rotate(-180deg);
}

@media (max-width: 575.98px) {
  .content .navbar .navbar-nav .nav-link {
      margin-left: 15px;
  }
}


/*** Date Picker ***/
.bootstrap-datetimepicker-widget.bottom {
  top: auto !important;
}

.bootstrap-datetimepicker-widget .table * {
  border-bottom-width: 0px;
}

.bootstrap-datetimepicker-widget .table th {
  font-weight: 500;
}

.bootstrap-datetimepicker-widget.dropdown-menu {
  padding: 10px;
  border-radius: 2px;
}

.bootstrap-datetimepicker-widget table td.active,
.bootstrap-datetimepicker-widget table td.active:hover {
  background: var(--primary);
}

.bootstrap-datetimepicker-widget table td.today::before {
  border-bottom-color: var(--primary);
}


/*** Testimonial ***/
.progress .progress-bar {
  width: 0px;
  transition: 2s;
}


/*** Testimonial ***/
.testimonial-carousel .owl-dots {
  margin-top: 24px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.testimonial-carousel .owl-dot {
  position: relative;
  display: inline-block;
  margin: 0 5px;
  width: 15px;
  height: 15px;
  border: 5px solid var(--primary);
  border-radius: 15px;
  transition: .5s;
}

.testimonial-carousel .owl-dot.active {
  background: var(--dark);
  border-color: var(--primary);
}