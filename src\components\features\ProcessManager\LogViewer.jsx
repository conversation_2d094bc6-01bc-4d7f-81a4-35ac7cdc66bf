import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const LogViewer = ({ 
  logs, 
  theme, 
  showLogs, 
  onToggleLogs, 
  onClearLogs, 
  getLogTypeColor,
  selectedProcess 
}) => {
  const logContainerRef = useRef(null);

  // Auto-scroll to bottom when new logs are added
  useEffect(() => {
    if (logContainerRef.current && showLogs) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs, showLogs]);

  const getLogIcon = (type) => {
    switch (type) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      case 'info': return 'ℹ️';
      default: return '📝';
    }
  };

  const filteredLogs = selectedProcess 
    ? logs.filter(log => log.process === selectedProcess.name || log.process === 'system')
    : logs;

  return (
    <div className={`rounded-lg border ${
      theme === 'dark' 
        ? 'bg-gray-800 border-gray-700' 
        : 'bg-white border-gray-200'
    }`}>
      {/* Log Header */}
      <div className={`px-6 py-4 border-b flex items-center justify-between ${
        theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
      }`}>
        <div className="flex items-center space-x-3">
          <button
            onClick={onToggleLogs}
            className={`flex items-center space-x-2 text-lg font-semibold transition-colors ${
              theme === 'dark' ? 'text-white hover:text-gray-300' : 'text-gray-900 hover:text-gray-700'
            }`}
          >
            <span className={`transform transition-transform ${showLogs ? 'rotate-90' : ''}`}>
              ▶️
            </span>
            <span>Process Logs</span>
          </button>
          {selectedProcess && (
            <span className={`text-sm px-2 py-1 rounded ${
              theme === 'dark' ? 'bg-blue-600 text-white' : 'bg-blue-100 text-blue-800'
            }`}>
              {selectedProcess.name} (PID: {selectedProcess.pid})
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`text-sm ${
            theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
          }`}>
            {filteredLogs.length} entries
          </span>
          <button
            onClick={onClearLogs}
            className={`px-3 py-1 text-sm rounded transition-colors ${
              theme === 'dark'
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-red-600 hover:bg-red-700 text-white'
            }`}
          >
            Clear Logs
          </button>
        </div>
      </div>

      {/* Log Content */}
      <AnimatePresence>
        {showLogs && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div 
              ref={logContainerRef}
              className={`p-4 max-h-80 overflow-y-auto font-mono text-sm ${
                theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'
              }`}
            >
              {filteredLogs.length === 0 ? (
                <div className={`text-center py-8 ${
                  theme === 'dark' ? 'text-gray-500' : 'text-gray-400'
                }`}>
                  <div className="text-4xl mb-2">📝</div>
                  <p>No logs to display</p>
                  <p className="text-xs mt-1">
                    {selectedProcess ? `Showing logs for ${selectedProcess.name}` : 'Select a process to view its logs'}
                  </p>
                </div>
              ) : (
                <div className="space-y-1">
                  {filteredLogs.map((log) => (
                    <motion.div
                      key={log.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.2 }}
                      className={`flex items-start space-x-3 p-2 rounded ${
                        theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-white'
                      }`}
                    >
                      <span className="text-lg">{getLogIcon(log.type)}</span>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <span className={`text-xs ${
                            theme === 'dark' ? 'text-gray-500' : 'text-gray-400'
                          }`}>
                            [{log.timestamp}]
                          </span>
                          <span className={`text-xs px-2 py-0.5 rounded ${
                            log.process === 'system'
                              ? theme === 'dark' ? 'bg-purple-600 text-white' : 'bg-purple-100 text-purple-800'
                              : theme === 'dark' ? 'bg-blue-600 text-white' : 'bg-blue-100 text-blue-800'
                          }`}>
                            {log.process}
                          </span>
                          <span className={`text-xs font-medium ${getLogTypeColor(log.type)}`}>
                            {log.type.toUpperCase()}
                          </span>
                        </div>
                        <p className={`mt-1 ${
                          theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          {log.message}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LogViewer;
