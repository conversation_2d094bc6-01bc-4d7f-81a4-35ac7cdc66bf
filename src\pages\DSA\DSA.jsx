import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  DSAHero, 
  NavigationButtons, 
  DifficultySection, 
  InterviewChecklist 
} from "./components";
import { Footer, CourseLayout } from "../../components/layout";
import useDSAData from "./hooks/useDSAData";
import useSidebarState from "../../hooks/useSidebarState";

const DSA = () => {
  const { dsaData, interviewChecklist } = useDSAData();
  const { isSidebarOpen, toggleSidebar } = useSidebarState(true); // Default to open
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Handle scroll to top visibility
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <CourseLayout 
      isSidebarOpen={isSidebarOpen} 
      toggleSidebar={toggleSidebar}
      title="DSA Master"
    >
      {/* Hero Section */}
      <motion.section
        variants={itemVariants}
        className="relative overflow-hidden bg-gradient-to-br from-[#1a3c50] to-[#010509] text-white min-h-screen flex items-center px-6"
      >
        <DSAHero />
      </motion.section>

      {/* Main Content */}
      <div className="flex-1">
        {/* Navigation Buttons */}
        <motion.div variants={itemVariants} className="py-8">
          <div className="max-w-6xl mx-auto px-4">
            <NavigationButtons />
          </div>
        </motion.div>

        {/* DSA Content */}
        <motion.div variants={itemVariants} className="py-8">
          <div className="max-w-6xl mx-auto px-4">
            <DifficultySection dsaData={dsaData} />
          </div>
        </motion.div>

        {/* Interview Checklist */}
        <motion.div variants={itemVariants} className="py-8">
          <div className="max-w-6xl mx-auto px-4">
            <InterviewChecklist interviewChecklist={interviewChecklist} />
          </div>
        </motion.div>
      </div>

      {/* Footer */}
      <Footer />

      {/* Floating Back to Top Button */}
      {showScrollTop && (
        <motion.button
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 z-50 bg-[#303246] hover:bg-[#252035] text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
            <path fillRule="evenodd" d="M8 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V14.5a.5.5 0 0 0 .5.5z"/>
          </svg>
        </motion.button>
      )}
    </CourseLayout>
  );
};

export default DSA;
