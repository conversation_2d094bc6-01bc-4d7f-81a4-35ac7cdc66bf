import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { Plus, Edit, Trash2, Save, Lightbulb } from 'lucide-react';

const ExplanationForm = ({ theme }) => {
  const [explanations, setExplanations] = useState([
    {
      id: 1,
      title: 'React Component Lifecycle',
      content: '<h3>Understanding Component Lifecycle</h3><p>React components go through several phases during their lifetime:</p><ol><li><strong>Mounting:</strong> When the component is being created and inserted into the DOM</li><li><strong>Updating:</strong> When the component is being re-rendered as a result of changes to props or state</li><li><strong>Unmounting:</strong> When the component is being removed from the DOM</li></ol><h4>Key Lifecycle Methods:</h4><ul><li><code>componentDidMount()</code> - Called after component is mounted</li><li><code>componentDidUpdate()</code> - Called after component updates</li><li><code>componentWillUnmount()</code> - Called before component is unmounted</li></ul>',
      category: 'React',
      difficulty: 'Intermediate',
      tags: ['lifecycle', 'components', 'hooks'],
      isPublished: true,
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      title: 'JavaScript Closures Explained',
      content: '<h3>What are Closures?</h3><p>A closure is a function that has access to variables in its outer (enclosing) scope even after the outer function has returned.</p><h4>Example:</h4><pre><code>function outerFunction(x) {\n  return function innerFunction(y) {\n    return x + y;\n  };\n}\n\nconst addFive = outerFunction(5);\nconsole.log(addFive(3)); // Output: 8</code></pre><p>In this example, <code>innerFunction</code> has access to the variable <code>x</code> from <code>outerFunction</code> even after <code>outerFunction</code> has finished executing.</p>',
      category: 'JavaScript',
      difficulty: 'Advanced',
      tags: ['closures', 'scope', 'functions'],
      isPublished: true,
      createdAt: '2024-01-14'
    }
  ]);

  const [showEditor, setShowEditor] = useState(false);
  const [editingExplanation, setEditingExplanation] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    category: 'General',
    difficulty: 'Beginner',
    tags: [],
    isPublished: false
  });

  const categories = ['General', 'React', 'JavaScript', 'Node.js', 'Database', 'CSS', 'HTML'];
  const difficulties = ['Beginner', 'Intermediate', 'Advanced'];

  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['link', 'image'],
      [{ 'align': [] }],
      [{ 'color': [] }, { 'background': [] }],
      ['blockquote', 'code-block'],
      ['clean']
    ]
  };

  const handleAdd = () => {
    setEditingExplanation(null);
    setFormData({
      title: '',
      content: '',
      category: 'General',
      difficulty: 'Beginner',
      tags: [],
      isPublished: false
    });
    setShowEditor(true);
  };

  const handleEdit = (explanation) => {
    setEditingExplanation(explanation);
    setFormData({
      title: explanation.title,
      content: explanation.content,
      category: explanation.category,
      difficulty: explanation.difficulty,
      tags: [...explanation.tags],
      isPublished: explanation.isPublished
    });
    setShowEditor(true);
  };

  const handleSave = () => {
    if (!formData.title.trim() || !formData.content.trim()) {
      alert('Please fill in all required fields');
      return;
    }

    if (editingExplanation) {
      setExplanations(prev => prev.map(exp => 
        exp.id === editingExplanation.id 
          ? { ...exp, ...formData }
          : exp
      ));
    } else {
      const newExplanation = {
        id: Date.now(),
        ...formData,
        createdAt: new Date().toISOString().split('T')[0]
      };
      setExplanations(prev => [...prev, newExplanation]);
    }
    setShowEditor(false);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this explanation?')) {
      setExplanations(prev => prev.filter(exp => exp.id !== id));
    }
  };

  const togglePublished = (id) => {
    setExplanations(prev => prev.map(exp => 
      exp.id === id 
        ? { ...exp, isPublished: !exp.isPublished }
        : exp
    ));
  };

  const handleTagsChange = (tagsString) => {
    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag);
    setFormData(prev => ({ ...prev, tags }));
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      'React': 'bg-blue-100 text-blue-800',
      'JavaScript': 'bg-yellow-100 text-yellow-800',
      'Node.js': 'bg-green-100 text-green-800',
      'Database': 'bg-purple-100 text-purple-800',
      'CSS': 'bg-pink-100 text-pink-800',
      'HTML': 'bg-orange-100 text-orange-800',
      'General': 'bg-gray-100 text-gray-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Explanation Manager
          </h2>
          <p className={`mt-1 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Create detailed explanations with rich text content
          </p>
        </div>
        
        <button
          onClick={handleAdd}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus size={16} />
          <span>Add Explanation</span>
        </button>
      </div>

      {/* Explanations Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {explanations.map((explanation) => (
          <div
            key={explanation.id}
            className={`rounded-lg border p-6 ${
              theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-3">
                  <Lightbulb size={16} className={theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600'} />
                  <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(explanation.difficulty)}`}>
                    {explanation.difficulty}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${getCategoryColor(explanation.category)}`}>
                    {explanation.category}
                  </span>
                  <button
                    onClick={() => togglePublished(explanation.id)}
                    className={`px-2 py-1 text-xs rounded-full ${
                      explanation.isPublished
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {explanation.isPublished ? 'Published' : 'Draft'}
                  </button>
                </div>
                
                <h3 className={`text-lg font-semibold mb-3 ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  {explanation.title}
                </h3>

                {/* Tags */}
                {explanation.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {explanation.tags.map((tag, index) => (
                      <span
                        key={index}
                        className={`px-2 py-1 text-xs rounded-full ${
                          theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'
                        }`}
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                )}

                {/* Content Preview */}
                <div className={`prose prose-sm max-w-none mb-4 ${
                  theme === 'dark' ? 'prose-invert' : ''
                }`}>
                  <div 
                    dangerouslySetInnerHTML={{ 
                      __html: explanation.content.substring(0, 300) + (explanation.content.length > 300 ? '...' : '') 
                    }} 
                  />
                </div>

                <p className={`text-sm ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                  Created: {explanation.createdAt}
                </p>
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => handleEdit(explanation)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                      : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                >
                  <Edit size={16} />
                </button>
                <button
                  onClick={() => handleDelete(explanation.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                      : 'text-red-500 hover:bg-red-50 hover:text-red-700'
                  }`}
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Editor Modal */}
      {showEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className={`rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto ${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-lg font-semibold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {editingExplanation ? 'Edit Explanation' : 'Add New Explanation'}
              </h3>
              <button
                onClick={() => setShowEditor(false)}
                className={`p-2 rounded-lg transition-colors ${
                  theme === 'dark'
                    ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                    : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                }`}
              >
                ×
              </button>
            </div>

            <div className="space-y-6">
              {/* Title and Meta */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                    placeholder="Enter explanation title..."
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Category
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  >
                    {categories.map(cat => (
                      <option key={cat} value={cat}>{cat}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Difficulty
                  </label>
                  <select
                    value={formData.difficulty}
                    onChange={(e) => setFormData(prev => ({ ...prev, difficulty: e.target.value }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  >
                    {difficulties.map(diff => (
                      <option key={diff} value={diff}>{diff}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Tags (comma-separated)
                  </label>
                  <input
                    type="text"
                    value={formData.tags.join(', ')}
                    onChange={(e) => handleTagsChange(e.target.value)}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                    placeholder="react, hooks, state"
                  />
                </div>
              </div>

              {/* Content Editor */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Content *
                </label>
                <div className={`${theme === 'dark' ? 'quill-dark' : ''}`}>
                  <ReactQuill
                    theme="snow"
                    value={formData.content}
                    onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                    modules={quillModules}
                    style={{ height: '400px', marginBottom: '50px' }}
                  />
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isPublished"
                  checked={formData.isPublished}
                  onChange={(e) => setFormData(prev => ({ ...prev, isPublished: e.target.checked }))}
                  className="mr-2"
                />
                <label htmlFor="isPublished" className={`text-sm ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Publish immediately
                </label>
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowEditor(false)}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  theme === 'dark'
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <Save size={16} />
                <span>{editingExplanation ? 'Update' : 'Create'}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExplanationForm;
