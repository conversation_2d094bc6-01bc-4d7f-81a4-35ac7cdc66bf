export const questionData = [
    {
        title: "Sort Array By Parity",
        category: "Data Structures",
        difficulty: "Medium",
        status: "Solved",
        description: "Sort an array such that all even integers appear before all odd integers while maintaining their relative order."
      },
      {
        title: "Add Two Numbers",
        category: "Data Structures",
        difficulty: "Easy",
        status: "Unsolved",
        description: "Add two numbers represented by linked lists, where each node contains a single digit in reverse order."
      },
      {
        title: "Longest Substring Without Repeating Characters",
        category: "Algorithms",
        difficulty: "Hard",
        status: "Unsolved",
        description: "Find the length of the longest substring without repeating characters in a given string."
      },
      {
        title: "Median of Two Sorted Arrays",
        category: "Algorithms",
        difficulty: "Easy",
        status: "Solved",
        description: "Find the median of two sorted arrays by merging and calculating the middle value."
      },
      {
        title: "Longest Palindromic Substring",
        category: "Algorithms",
        difficulty: "Medium",
        status: "Unsolved",
        description: "Find the longest substring that is a palindrome in a given string."
      },
]