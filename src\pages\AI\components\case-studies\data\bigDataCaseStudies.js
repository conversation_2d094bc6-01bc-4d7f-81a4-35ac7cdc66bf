const bigDataCaseStudies = [
  {
    title: "Big Data Processing",
    objective: "Learn big data processing techniques",
    scenario: "Process and analyze large-scale dataset",
    keyConcepts: ["Distributed Computing", "Data Partitioning", "MapReduce", "Stream Processing"],
    solution: `# PySpark Example
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, avg

# Initialize Spark session
spark = SparkSession.builder \
    .appName("BigDataAnalysis") \
    .getOrCreate()

# Read large dataset
df = spark.read.csv("large_dataset.csv", header=True)

# Perform analysis
result = df.groupBy("category") \
    .agg(avg("value").alias("avg_value")) \
    .filter(col("avg_value") > 100) \
    .orderBy("avg_value", ascending=False)

# Show results
result.show()

# Clean up
spark.stop()
`
  }
];

export default bigDataCaseStudies;
