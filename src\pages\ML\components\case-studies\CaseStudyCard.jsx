import React from 'react';

const CaseStudyCard = ({ study, index, isActive, showSolution, toggleContent, toggleSolution }) => {
  return (
    <li className="chapter-item !bg-transparent border border-gray-700/50 rounded-lg overflow-hidden">
      <div 
        className="chapter-header flex justify-between items-center p-3 cursor-pointer hover:bg-blue-900/30 transition-colors"
        onClick={() => toggleContent(index)}
      >
        <span className="text-white font-medium text-sm text-shadow-sm">{study.title}</span>
        <span className="chapter-toggle text-blue-400 hover:text-blue-300">
          {isActive ? "▲" : "▼"}
        </span>
      </div>
      {isActive && (
        <div className="chapter-content p-3 border-t border-gray-700/50 !bg-transparent">
          <div className="mb-3">
            <h4 className="text-sm font-semibold text-white mb-1 flex items-center gap-2 text-shadow-sm">
              <span className="w-2 h-2 bg-blue-500 rounded-full glow-blue-sm"></span>
              Objective
            </h4>
            <p className="text-blue-100 text-xs">{study.objective}</p>
          </div>
          
          <div className="mb-3">
            <h4 className="text-sm font-semibold text-white mb-1 flex items-center gap-2 text-shadow-sm">
              <span className="w-2 h-2 bg-purple-500 rounded-full glow-purple-sm"></span>
              Scenario
            </h4>
            <p className="text-purple-100 text-xs">{study.scenario}</p>
          </div>
          
          <div className="mb-3">
            <h4 className="text-sm font-semibold text-white mb-1 flex items-center gap-2 text-shadow-sm">
              <span className="w-2 h-2 bg-green-500 rounded-full glow-green-sm"></span>
              Key Concepts
            </h4>
            <div className="flex flex-wrap gap-1">
              {Array.isArray(study.keyConcepts) ? 
                study.keyConcepts.map((concept, idx) => (
                  <span key={idx} className="px-2 py-0.5 !bg-transparent border border-blue-700/40 rounded-full text-xs text-green-200">
                    {concept}
                  </span>
                )) : 
                <p className="text-green-200 text-xs">{study.keyConcepts}</p>
              }
            </div>
          </div>
          
          <button 
            onClick={(e) => {
              e.stopPropagation();
              toggleSolution(index);
            }} 
            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-md transition-colors"
          >
            {showSolution ? "Hide Solution" : "Show Solution"}
          </button>
          
          {showSolution && (
            <div className="mt-2 relative">
              <pre className="solution !bg-transparent text-gray-100 p-2 rounded-md overflow-x-auto text-xs leading-relaxed border border-blue-700/40">
                {study.solution}
              </pre>
              <button 
                onClick={() => navigator.clipboard.writeText(study.solution)}
                className="absolute top-1 right-1 p-1 bg-blue-900/60 hover:bg-blue-900/80 rounded-md transition-colors"
                title="Copy code"
              >
                <svg className="w-3 h-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </button>
            </div>
          )}
        </div>
      )}
    </li>
  );
};

export default CaseStudyCard;