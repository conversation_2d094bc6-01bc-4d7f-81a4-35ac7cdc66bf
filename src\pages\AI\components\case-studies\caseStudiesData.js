import { 
  aiCaseStudies,
  pythonCaseStudies,
  nlpCaseStudies,
  cvCaseStudies,
  rlCaseStudies,
  roboticsCaseStudies,
  ethicsCaseStudies,
  healthcareCaseStudies,
  bigDataCaseStudies,
  iotCaseStudies
} from './data';

// Export categories configuration
export const categories = [
  { id: 'ai', name: 'AI & Machine Learning', icon: '🤖', color: 'from-purple-500 to-blue-500', studies: aiCaseStudies },
  { id: 'python', name: 'Python Programming', icon: '🐍', color: 'from-green-500 to-teal-500', studies: pythonCaseStudies },
  { id: 'nlp', name: 'Natural Language Processing', icon: '📝', color: 'from-blue-500 to-cyan-500', studies: nlpCaseStudies },
  { id: 'cv', name: 'Computer Vision', icon: '👁️', color: 'from-yellow-500 to-orange-500', studies: cvCaseStudies },
  { id: 'rl', name: 'Reinforcement Learning', icon: '🎮', color: 'from-red-500 to-pink-500', studies: rlCaseStudies },
  { id: 'robotics', name: 'Robotics', icon: '🤖', color: 'from-indigo-500 to-purple-500', studies: roboticsCaseStudies },
  { id: 'ethics', name: 'AI Ethics', icon: '⚖️', color: 'from-gray-500 to-slate-500', studies: ethicsCaseStudies },
  { id: 'healthcare', name: 'AI in Healthcare', icon: '🏥', color: 'from-emerald-500 to-green-500', studies: healthcareCaseStudies },
  { id: 'bigdata', name: 'AI & Big Data', icon: '📊', color: 'from-orange-500 to-red-500', studies: bigDataCaseStudies },
  { id: 'iot', name: 'AI in IoT', icon: '🔌', color: 'from-sky-500 to-blue-500', studies: iotCaseStudies }
];