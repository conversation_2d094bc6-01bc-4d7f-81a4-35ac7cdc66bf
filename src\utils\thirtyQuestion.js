export const thirtyQuestion = [
    {
        question: "1. Implement a Linked List",
        answer:
            "A linked list has nodes containing data and a reference to the next node.",
    },
    {
        question: "2. Explain Depth-First Search (DFS)",
        answer:
            "DFS explores as far as possible along each branch before backtracking.",
    },
    {
        question: "3. Explain the concept of Breadth-First Search (BFS)",
        answer:
            "BFS explores all neighbors at the present depth before moving to the next depth level.",
    },
    {
        question: "4. Implement Binary Search",
        answer:
            "Binary Search involves dividing a sorted array in half repeatedly until the target element is found.",
    },
    {
        question: "5. Explain Merge Sort Algorithm",
        answer:
            "Merge Sort divides the array into halves, sorts each half, and merges them in sorted order.",
    },
    {
        question: "6. What is Quick Sort?",
        answer:
            "Quick Sort selects a pivot, partitions the array around it, and recursively sorts each partition.",
    },
    {
        question: "7. Implement a Stack Using Queues",
        answer: "Use two queues to emulate stack behavior (LIFO).",
    },
    {
        question: "8. Implement a Queue Using Stacks",
        answer: "Use two stacks to achieve queue behavior (FIFO).",
    },
    {
        question: "9. Explain the concept of a Binary Search Tree (BST)",
        answer:
            "A BST is a tree structure where each node has values greater than its left children and less than its right.",
    },
    {
        question: "10. Implement Heap Sort",
        answer:
            "Heap Sort builds a max heap and repeatedly extracts the maximum element to sort the array.",
    },
    {
        question: "11. Explain the Two-Pointer Technique",
        answer:
            "Two-pointer technique uses two pointers moving at different speeds to solve problems efficiently.",
    },
    {
        question: "12. Find the Lowest Common Ancestor in a Binary Tree",
        answer:
            "Use recursion to trace ancestors of two nodes and find the first common ancestor.",
    },
    {
        question: "13. Describe Floyd’s Cycle-Finding Algorithm",
        answer:
            "Floyd's algorithm detects cycles in linked lists using two pointers moving at different speeds.",
    },
    {
        question: "14. Implement the KMP Pattern Matching Algorithm",
        answer:
            "KMP uses a partial match table to skip unnecessary comparisons in pattern matching.",
    },
    {
        question: "15. Explain Dynamic Programming (DP)",
        answer:
            "DP solves complex problems by breaking them into overlapping subproblems and storing results.",
    },
    {
        question: "16. Explain Dijkstra’s Algorithm",
        answer:
            "Dijkstra's algorithm finds the shortest path from a source node to other nodes in a weighted graph.",
    },
    {
        question: "17. Explain the Greedy Algorithm",
        answer:
            "Greedy algorithms make optimal choices at each step to reach a global solution.",
    },
    {
        question: "18. Solve the Knapsack Problem",
        answer:
            "Use dynamic programming to select items maximizing value without exceeding weight capacity.",
    },
    {
        question: "19. Explain Topological Sort",
        answer:
            "Topological Sort orders nodes in a directed acyclic graph such that each node comes before nodes it points to.",
    },
    {
        question: "20. What is a Trie Data Structure?",
        answer:
            "A Trie is a tree-like structure for efficient storage and retrieval of strings.",
    },
    {
        question: "21. Explain the concept of Hashing",
        answer:
            "Hashing uses a hash function to map keys to values, providing efficient data retrieval.",
    },
    {
        question: "22. Implement a Binary Tree",
        answer:
            "A binary tree is a tree structure where each node has at most two children.",
    },
    {
        question: "23. What is an AVL Tree?",
        answer:
            "An AVL tree is a self-balancing binary search tree that maintains balance factors for each node.",
    },
    {
        question: "24. Explain Bubble Sort Algorithm",
        answer:
            "Bubble Sort repeatedly steps through the list, compares adjacent elements, and swaps them if necessary.",
    },
    {
        question: "25. Explain Selection Sort Algorithm",
        answer:
            "Selection Sort finds the minimum element in each pass and places it in its correct position.",
    },
    {
        question: "26. What is Insertion Sort?",
        answer:
            "Insertion Sort builds a sorted array one element at a time by comparing with previous elements.",
    },
    {
        question: "27. Explain Radix Sort Algorithm",
        answer:
            "Radix Sort processes numbers by each digit place, using a stable sort to sort by each place.",
    },
    {
        question: "28. Implement Counting Sort",
        answer:
            "Counting Sort counts occurrences of each element to sort an array efficiently.",
    },
    {
        question: "29. Explain the concept of Backtracking",
        answer:
            "Backtracking explores all possibilities and backtracks once it realizes a path is unsuitable.",
    },
    {
        question: "30. Solve N-Queens Problem",
        answer:
            "Use backtracking to place queens on a chessboard such that no two queens attack each other.",
    },
]