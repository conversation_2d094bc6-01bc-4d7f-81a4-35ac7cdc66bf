// Utility functions for the lab environment

export const getFileIcon = (filename) => {
  const ext = filename.split('.').pop().toLowerCase();
  switch (ext) {
    case 'html': return '🌐';
    case 'css': return '🎨';
    case 'js': return '⚡';
    case 'json': return '📋';
    case 'md': return '📝';
    default: return '📄';
  }
};

export const getLanguageFromFilename = (filename) => {
  const ext = filename.split('.').pop().toLowerCase();
  switch (ext) {
    case 'html': return 'html';
    case 'css': return 'css';
    case 'js': return 'javascript';
    case 'json': return 'json';
    case 'md': return 'markdown';
    case 'tsx': return 'typescript';
    case 'ts': return 'typescript';
    default: return 'plaintext';
  }
};

export const generatePreviewContent = (files) => {
  const htmlFile = files['index.html'];
  const cssFile = files['styles.css'];
  const jsFile = files['script.js'];
  
  if (!htmlFile) return '<p>No HTML file found</p>';
  
  return `
    ${htmlFile.content}
    <style>${cssFile?.content || ''}</style>
    <script>${jsFile?.content || ''}</script>
  `;
};

export const challengeTemplates = {
  "todo-app": {
    'index.html': {
      id: 'index.html',
      name: 'index.html',
      language: 'html',
      content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MERN Todo App</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="root">
        <div class="todo-container">
            <h1>MERN Todo Application</h1>
            <div class="todo-input">
                <input type="text" id="todoInput" placeholder="Add a new task...">
                <button onclick="addTodo()">Add Task</button>
            </div>
            <ul id="todoList" class="todo-list"></ul>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>`,
      saved: false
    },
    'styles.css': {
      id: 'styles.css',
      name: 'styles.css',
      language: 'css',
      content: `/* Todo App Styles */
.todo-container {
    max-width: 600px;
    margin: 50px auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.todo-input {
    display: flex;
    margin-bottom: 20px;
}

.todo-input input {
    flex: 1;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px 0 0 5px;
    font-size: 16px;
}

.todo-input button {
    padding: 12px 20px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
}

.todo-list {
    list-style: none;
    padding: 0;
}

.todo-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}`,
      saved: false
    },
    'script.js': {
      id: 'script.js',
      name: 'script.js',
      language: 'javascript',
      content: `// MERN Todo App JavaScript
let todos = [];
let todoId = 1;

function addTodo() {
    const input = document.getElementById('todoInput');
    const text = input.value.trim();
    
    if (text) {
        const todo = {
            id: todoId++,
            text: text,
            completed: false
        };
        
        todos.push(todo);
        input.value = '';
        renderTodos();
    }
}

function deleteTodo(id) {
    todos = todos.filter(todo => todo.id !== id);
    renderTodos();
}

function toggleTodo(id) {
    const todo = todos.find(todo => todo.id === id);
    if (todo) {
        todo.completed = !todo.completed;
        renderTodos();
    }
}

function renderTodos() {
    const todoList = document.getElementById('todoList');
    todoList.innerHTML = '';
    
    todos.forEach(todo => {
        const li = document.createElement('li');
        li.className = 'todo-item';
        li.innerHTML = \`
            <span style="text-decoration: \${todo.completed ? 'line-through' : 'none'}">
                \${todo.text}
            </span>
            <div>
                <button onclick="toggleTodo(\${todo.id})" style="margin-right: 10px;">
                    \${todo.completed ? 'Undo' : 'Complete'}
                </button>
                <button onclick="deleteTodo(\${todo.id})" style="background: #f44336;">
                    Delete
                </button>
            </div>
        \`;
        todoList.appendChild(li);
    });
}

// Allow Enter key to add todo
document.addEventListener('DOMContentLoaded', () => {
    const input = document.getElementById('todoInput');
    input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            addTodo();
        }
    });
});`,
      saved: false
    }
  }
};
