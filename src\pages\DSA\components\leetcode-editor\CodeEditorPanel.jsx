import React from "react";
import { motion } from "framer-motion";
import Editor from "@monaco-editor/react";
import TabPanel from "./TabPanel";

const CodeEditorPanel = ({
  selectedLanguage,
  languages,
  code,
  customInput,
  output,
  testResults,
  isRunning,
  isSubmitting,
  theme,
  activeTab,
  onLanguageChange,
  onCodeChange,
  onCustomInputChange,
  onTabChange,
  onRun,
  onSubmit,
  onReset,
  showPremiumOverlay
}) => {
  return (
    <div className={`flex flex-col h-full ${
      theme === 'dark' ? 'bg-gray-900' : 'bg-white'
    }`}>
      {/* Editor Header */}
      <div className={`border-b px-4 py-3 ${
        theme === 'dark' ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
      }`}>
        <div className="flex items-center justify-between">
          {/* Language Selector */}
          <div className="flex items-center space-x-4">
            <label className={`text-sm font-medium ${
              theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Language:
            </label>
            <select
              value={selectedLanguage}
              onChange={(e) => onLanguageChange(e.target.value)}
              className={`px-3 py-1 rounded border text-sm ${
                theme === 'dark' 
                  ? 'bg-gray-700 border-gray-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              {languages.map(lang => (
                <option key={lang.id} value={lang.id}>
                  {lang.name}
                </option>
              ))}
            </select>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={onReset}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                theme === 'dark' 
                  ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' 
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
              }`}
            >
              Reset
            </button>
            <button
              onClick={onRun}
              disabled={isRunning}
              className={`px-4 py-1 text-sm rounded transition-colors ${
                isRunning
                  ? 'bg-gray-500 cursor-not-allowed text-white'
                  : 'bg-green-600 hover:bg-green-700 text-white'
              }`}
            >
              {isRunning ? 'Running...' : '▶ Run'}
            </button>
            <button
              onClick={onSubmit}
              disabled={isSubmitting}
              className={`px-4 py-1 text-sm rounded transition-colors ${
                isSubmitting
                  ? 'bg-gray-500 cursor-not-allowed text-white'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isSubmitting ? 'Submitting...' : '📤 Submit'}
            </button>
          </div>
        </div>
      </div>

      {/* Code Editor */}
      <div className="flex-1 flex flex-col">
        <div className="flex-1">
          <Editor
            height="100%"
            language={selectedLanguage}
            value={code}
            onChange={onCodeChange}
            theme={theme === 'dark' ? 'vs-dark' : 'light'}
            options={{
              minimap: { enabled: false },
              fontSize: 14,
              wordWrap: "on",
              automaticLayout: true,
              scrollBeyondLastLine: false,
              renderWhitespace: 'selection',
              selectOnLineNumbers: true,
              roundedSelection: false,
              readOnly: false,
              cursorStyle: 'line',
              glyphMargin: true,
              folding: true,
              lineNumbers: 'on',
              lineDecorationsWidth: 10,
              lineNumbersMinChars: 3,
              renderLineHighlight: 'all',
              contextmenu: true,
              mouseWheelZoom: true,
              smoothScrolling: true,
              cursorBlinking: 'blink',
              cursorSmoothCaretAnimation: true,
              renderFinalNewline: true,
              quickSuggestions: true,
              suggestOnTriggerCharacters: true,
              acceptSuggestionOnEnter: 'on',
              tabCompletion: 'on',
              wordBasedSuggestions: true,
              parameterHints: { enabled: true },
              autoClosingBrackets: 'always',
              autoClosingQuotes: 'always',
              autoSurround: 'languageDefined',
              colorDecorators: true,
              dragAndDrop: true,
              find: {
                seedSearchStringFromSelection: true,
                autoFindInSelection: 'never'
              }
            }}
          />
        </div>

        {/* Bottom Panel with Tabs */}
        <div className={`border-t ${
          theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
        }`} style={{ height: '300px' }}>
          <TabPanel
            activeTab={activeTab}
            onTabChange={onTabChange}
            customInput={customInput}
            onCustomInputChange={onCustomInputChange}
            output={output}
            testResults={testResults}
            theme={theme}
            showPremiumOverlay={showPremiumOverlay}
          />
        </div>
      </div>
    </div>
  );
};

export default CodeEditorPanel;
