import React from "react";
import { motion, AnimatePresence } from "framer-motion";

const CaseStudyCard = ({ 
  study, 
  index, 
  activeSection,
  activeIndex, 
  showSolutions, 
  toggleChapter, 
  toggleSolution, 
  itemVariants 
}) => {
  const isActive = activeIndex === index;
  const solutionKey = `${activeSection}-${index}`;
  const showSolution = showSolutions[solutionKey];

  return (
    <motion.div
      key={`${activeSection}-${index}`}
      variants={itemVariants}
      className="bg-gray-50 rounded-2xl overflow-hidden border border-gray-200 hover:shadow-lg transition-all duration-300"
    >
      <motion.div
        onClick={() => toggleChapter(index)}
        className="p-6 cursor-pointer hover:bg-gray-100 transition-colors duration-200 flex items-center justify-between"
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center text-white font-bold text-lg">
            {index + 1}
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900">
              {study.title}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {study.keyConcepts}
            </p>
          </div>
        </div>
        
        <motion.div
          animate={{ rotate: isActive ? 180 : 0 }}
          transition={{ duration: 0.3 }}
          className="text-2xl text-gray-400"
        >
          ↓
        </motion.div>
      </motion.div>

      <AnimatePresence>
        {isActive && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.4 }}
            className="overflow-hidden"
          >
            <div className="p-6 bg-gradient-to-r from-orange-50 to-red-50 border-t border-gray-200">
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">🎯 Objective:</h4>
                  <p className="text-gray-700">{study.objective}</p>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">📝 Scenario:</h4>
                  <p className="text-gray-700">{study.scenario}</p>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">🔑 Key Concepts:</h4>
                  <p className="text-gray-700">{study.keyConcepts}</p>
                </div>
                
                <div className="pt-4">
                  <motion.button
                    onClick={() => toggleSolution(index)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-600 to-red-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <span className="mr-2">
                      {showSolution ? "🙈" : "👁️"}
                    </span>
                    {showSolution ? "Hide Solution" : "Show Solution"}
                  </motion.button>
                  
                  {showSolution && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="mt-4 bg-gray-900 rounded-xl p-6 overflow-x-auto"
                    >
                      <pre className="text-green-400 text-sm font-mono leading-relaxed">
                        {study.solution}
                      </pre>
                    </motion.div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default CaseStudyCard;
