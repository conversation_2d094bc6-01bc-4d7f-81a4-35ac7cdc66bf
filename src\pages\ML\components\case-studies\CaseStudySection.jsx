import React from 'react';
import CaseStudyCard from './CaseStudyCard';

const CaseStudySection = ({ title, studies, activeIndex, showSolutions, toggleContent, toggleSolution, isExpanded, toggleSection }) => {
  return (
    <div id={title.toLowerCase().replace(/\s+/g, '-')} className="main-content py-2">
      <div 
        className="p-3 rounded-lg border border-blue-700 cursor-pointer !bg-transparent hover:bg-blue-900/10 transition-colors"
        onClick={() => toggleSection(title)}
      >
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold text-white">{title}</h2>
            <p className="text-gray-300 text-xs">{studies.length} case studies</p>
          </div>
          <span className="text-blue-400">
            {isExpanded ? "▲" : "▼"}
          </span>
        </div>
      </div>
      
      {isExpanded && (
        <ul className="chapter-list space-y-2 mt-2">
          {studies.map((study, index) => (
            <CaseStudyCard
              key={index}
              study={study}
              index={index}
              isActive={activeIndex === index}
              showSolution={showSolutions[index]}
              toggleContent={toggleContent}
              toggleSolution={toggleSolution}
            />
          ))}
        </ul>
      )}
    </div>
  );
};

export default CaseStudySection;