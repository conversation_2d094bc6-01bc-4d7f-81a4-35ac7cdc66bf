import React from "react";
import { motion } from "framer-motion";

const ProblemPanel = ({ problem, theme, showPremiumOverlay }) => {
  if (!problem) {
    return (
      <div className={`h-full flex items-center justify-center ${
        theme === 'dark' ? 'bg-gray-900 text-gray-400' : 'bg-gray-50 text-gray-500'
      }`}>
        <div className="text-center">
          <div className="text-4xl mb-4">🧩</div>
          <p className="text-lg font-medium mb-2">Select a Problem</p>
          <p className="text-sm">Choose a DSA problem from the dropdown to get started</p>
        </div>
      </div>
    );
  }

  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'text-green-500';
      case 'medium': return 'text-yellow-500';
      case 'hard': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getDifficultyBg = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'hard': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <div className={`h-full overflow-y-auto ${
      theme === 'dark' ? 'bg-gray-900' : 'bg-white'
    }`}>
      <div className="p-6">
        {/* Problem Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h1 className={`text-2xl font-bold ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              {problem.title}
            </h1>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyBg(problem.difficulty)}`}>
              {problem.difficulty}
            </span>
          </div>
          
          {/* Tags */}
          <div className="flex flex-wrap gap-2 mb-4">
            {problem.tags?.map((tag, index) => (
              <span
                key={index}
                className={`px-2 py-1 text-xs rounded ${
                  theme === 'dark' 
                    ? 'bg-blue-900/30 text-blue-400 border border-blue-700/30' 
                    : 'bg-blue-100 text-blue-800 border border-blue-200'
                }`}
              >
                {tag}
              </span>
            ))}
          </div>
        </motion.div>

        {/* Problem Description */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-6"
        >
          <h2 className={`text-lg font-semibold mb-3 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            Description
          </h2>
          <div className={`prose max-w-none ${
            theme === 'dark' ? 'prose-invert text-gray-300' : 'text-gray-700'
          }`}>
            <p className="whitespace-pre-line">{problem.description}</p>
          </div>
        </motion.div>

        {/* Examples */}
        {problem.examples && problem.examples.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-6"
          >
            <h2 className={`text-lg font-semibold mb-3 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              Examples
            </h2>
            {problem.examples.map((example, index) => (
              <div
                key={index}
                className={`mb-4 p-4 rounded-lg border ${
                  theme === 'dark' 
                    ? 'bg-gray-800 border-gray-700' 
                    : 'bg-gray-50 border-gray-200'
                }`}
              >
                <h3 className={`font-medium mb-2 ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  Example {index + 1}:
                </h3>
                <div className={`font-mono text-sm ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  <div className="mb-2">
                    <span className="font-semibold">Input:</span> {example.input}
                  </div>
                  <div className="mb-2">
                    <span className="font-semibold">Output:</span> {example.output}
                  </div>
                  {example.explanation && (
                    <div>
                      <span className="font-semibold">Explanation:</span> {example.explanation}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </motion.div>
        )}

        {/* Constraints */}
        {problem.constraints && problem.constraints.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mb-6"
          >
            <h2 className={`text-lg font-semibold mb-3 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              Constraints
            </h2>
            <ul className={`list-disc list-inside space-y-1 ${
              theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
            }`}>
              {problem.constraints.map((constraint, index) => (
                <li key={index} className="font-mono text-sm">{constraint}</li>
              ))}
            </ul>
          </motion.div>
        )}

        {/* Hints */}
        {problem.hints && problem.hints.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mb-6"
          >
            <h2 className={`text-lg font-semibold mb-3 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              Hints
            </h2>
            <div className="space-y-2">
              {problem.hints.map((hint, index) => (
                <details
                  key={index}
                  className={`p-3 rounded border cursor-pointer ${
                    theme === 'dark' 
                      ? 'bg-gray-800 border-gray-700 text-gray-300' 
                      : 'bg-gray-50 border-gray-200 text-gray-700'
                  }`}
                >
                  <summary className="font-medium">Hint {index + 1}</summary>
                  <p className="mt-2 text-sm">{hint}</p>
                </details>
              ))}
            </div>
          </motion.div>
        )}

        {/* Premium Features */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-8"
        >
          <div className={`p-4 rounded-lg border-2 border-dashed ${
            theme === 'dark' 
              ? 'border-yellow-600/50 bg-yellow-900/20' 
              : 'border-yellow-400/50 bg-yellow-50'
          }`}>
            <div className="flex items-center mb-2">
              <span className="text-yellow-500 mr-2">⭐</span>
              <h3 className={`font-semibold ${
                theme === 'dark' ? 'text-yellow-400' : 'text-yellow-700'
              }`}>
                Premium Features
              </h3>
            </div>
            <p className={`text-sm mb-3 ${
              theme === 'dark' ? 'text-yellow-300' : 'text-yellow-600'
            }`}>
              Unlock detailed solutions, video explanations, and similar problems
            </p>
            <button
              onClick={showPremiumOverlay}
              className="px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg text-sm font-medium hover:from-yellow-600 hover:to-orange-600 transition-all"
            >
              Upgrade to Premium
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ProblemPanel;
