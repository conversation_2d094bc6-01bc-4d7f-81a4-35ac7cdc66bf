import React from "react";
import { FaFolder } from "react-icons/fa";

const InstructionsBanner = ({ onToggleSidebar }) => {
  return (
    <div className="bg-blue-900/20 border-b border-blue-700/30 px-6 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h3 className="text-sm font-semibold text-blue-400">Lab Challenges Instructions</h3>
          <div className="flex items-center space-x-6 text-xs text-gray-300">
            <span>1. Select a challenge from the dropdown</span>
            <span>2. Write HTML, CSS, and JavaScript in the code editor</span>
            <span>3. See live preview updates as you type</span>
            <span>4. Submit your solution for evaluation</span>
          </div>
        </div>
        <button
          onClick={onToggleSidebar}
          className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs transition-colors"
          title="Toggle File Explorer"
        >
          <FaFolder className="inline mr-1" />
          Files
        </button>
      </div>
    </div>
  );
};

export default InstructionsBanner;
