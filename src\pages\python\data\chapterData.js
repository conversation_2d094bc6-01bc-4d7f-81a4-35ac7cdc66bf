export const chapterData = [
  {
    _id: "ch1",
    title: "Introduction to Python",
    description: "Learn the basics of Python programming, including syntax, variables, data types, and basic operations. This chapter covers the fundamentals you need to get started with Python programming.",
    difficulty: 1,
    tags: ["basics", "syntax", "beginners"]
  },
  {
    _id: "ch2",
    title: "Control Flow",
    description: "Master conditional statements, loops, and control flow in Python. Learn how to use if-else statements, for loops, while loops, and break/continue statements to control the execution flow of your programs.",
    difficulty: 2,
    tags: ["control flow", "loops", "conditionals"]
  },
  {
    _id: "ch3",
    title: "Functions and Modules",
    description: "Understand how to create reusable code with functions and modules in Python. Learn about parameters, return values, scope, and how to organize your code into modules for better maintainability.",
    difficulty: 2,
    tags: ["functions", "modules", "organization"]
  },
  {
    _id: "ch4",
    title: "Data Structures",
    description: "Explore Python's built-in data structures like lists, tuples, sets, and dictionaries. Learn how to manipulate these data structures efficiently for various programming tasks.",
    difficulty: 3,
    tags: ["data structures", "lists", "dictionaries"]
  },
  {
    _id: "ch5",
    title: "File Handling",
    description: "Learn how to read from and write to files in Python. Understand different file modes, error handling, and best practices for file operations.",
    difficulty: 3,
    tags: ["files", "I/O", "error handling"]
  },
  {
    _id: "ch6",
    title: "Object-Oriented Programming",
    description: "Master the principles of object-oriented programming in Python. Learn about classes, objects, inheritance, encapsulation, and polymorphism to build complex systems.",
    difficulty: 4,
    tags: ["OOP", "classes", "inheritance"]
  },
  {
    _id: "ch7",
    title: "Exception Handling",
    description: "Understand how to handle errors and exceptions in Python. Learn about try-except blocks, raising custom exceptions, and best practices for error handling in your programs.",
    difficulty: 3,
    tags: ["exceptions", "error handling", "debugging"]
  },
  {
    _id: "ch8",
    title: "Working with APIs",
    description: "Learn how to interact with web APIs using Python. Understand how to make HTTP requests, parse JSON responses, and integrate external services into your applications.",
    difficulty: 4,
    tags: ["APIs", "HTTP", "JSON"]
  }
];
