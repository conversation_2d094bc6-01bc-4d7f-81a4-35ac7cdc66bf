import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import axios from "axios";
import toast from "react-hot-toast";
import { Spinner } from "../../components/ui";

const ForgotPassword = () => {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);

  const validateFields = () => {
    if (!email) {
      toast.error("Please Enter Email");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateFields()) return;
    setLoading(true);
    try {
      const { data } = await axios.post(
        "http://localhost:8000/api/v1/auth/generate-reset-token",
        { email },
        { withCredentials: true }
      );
      if (data.success) {
        toast.success(data.message);
        setEmail("");
      } else {
        toast.error(data.error);
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };
  return (
    <div>
      <div className="forgot-password-container">
        <div className="form-container-forgot">
          <h2>Forgot Password</h2>
          <p>
            Enter your email address to receive a link to reset your password.
          </p>

          <form onSubmit={handleSubmit}>
            <input
              type="email"
              className="email-input-forgot"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <button
              type="submit"
              className="btn-primary"
              disabled={loading}
            >
              {loading ? <Spinner /> : "Send Reset Link"}
            </button>
          </form>

          <Link
            to="/log-In"
            style={{ textDecoration: "none" }}
            className="back-link-forgot"
          >
            Back to Login
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
