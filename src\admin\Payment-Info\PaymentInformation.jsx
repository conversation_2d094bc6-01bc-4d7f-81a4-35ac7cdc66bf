import { useState } from "react";
import { DeleteModal } from "../../components/ui";

const PaymentInformation = () => {
  const [isOpen, setIsOpen] = useState(false);

  const openDeleteModal = () => {
    setIsOpen(true);
  };

  const closeDeleteModal = () => {
    setIsOpen(false);
  };

  const deleteConfirmation = () => {
    alert("Payment Information Deleted!");
    setIsOpen(false);
  };

  return (
    <div className="bg-gray-100 min-h-screen py-10">
      <div className="w-[95%] max-w-[1100px] mx-auto p-5 rounded-lg text-white shadow-lg border border-gray-300">
        <h2 className="text-center text-3xl font-extrabold text-blue-400 mb-4">
          Payment Information
        </h2>

        <div className="w-full h-[400px] overflow-y-auto bg-white p-4 rounded-lg shadow-md text-gray-800">
          <h3 className="text-xl font-semibold mb-4 text-center sm:text-left">
            Payment Table
          </h3>

          {/* Horizontal Scroll Wrapper */}
          <div className="overflow-x-auto">
            <table className="w-full min-w-[600px] border-collapse text-sm sm:text-base">
              <thead className="bg-blue-900 text-white">
                <tr className="uppercase font-bold text-left">
                  <th className="p-3">
                    <input type="checkbox" />
                  </th>
                  <th className="p-3">Date</th>
                  <th className="p-3">Amount</th>
                  <th className="p-3 hidden sm:table-cell">UPI ID</th>
                  <th className="p-3 hidden md:table-cell">Transaction ID</th>
                  <th className="p-3">Status</th>
                  <th className="p-3">Action</th>
                </tr>
              </thead>
              <tbody>
                {[...Array(6)].map((_, index) => (
                  <tr
                    key={index}
                    className="border-b border-gray-300 even:bg-gray-50 text-center"
                  >
                    <td className="p-3">
                      <input type="checkbox" />
                    </td>
                    <td className="p-3">01 Jan 2002</td>
                    <td className="p-3">Rs.123</td>
                    <td className="p-3 hidden sm:table-cell">
                      <EMAIL>
                    </td>
                    <td className="p-3 hidden md:table-cell">TRXN-0289</td>
                    <td className="p-3">
                      <span
                        className={`px-2 py-1 rounded text-white font-semibold ${
                          index % 2 === 0 ? "bg-green-500" : "bg-yellow-500"
                        }`}
                      >
                        {index % 2 === 0 ? "Complete" : "Pending"}
                      </span>
                    </td>
                    <td className="p-3">
                      <button
                        className="px-3 py-2 bg-red-500 text-white rounded-md font-bold transition-transform transform hover:scale-105"
                        onClick={openDeleteModal}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <DeleteModal
          title={"Delete Payment Information"}
          message={"Are You Sure Want to Delete?"}
          onClose={closeDeleteModal}
          isOpen={isOpen}
          onConfirm={deleteConfirmation}
        />

        {/* Filter and Export Section */}
        <div className="w-full bg-white border-gray-300 rounded-lg bg-gray-100 px-6 py-8 mt-6 rounded-lg shadow-sm">
          <h3 className="text-xl mb-5 ml-2 font-semibold text-blue-500">
            Filter Payments
          </h3>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 px-2 sm:px-8">
            <div className="flex flex-col">
              <label htmlFor="Date" className="font-semibold text-gray-700">
                Date
              </label>
              <input
                type="date"
                className="w-full mt-2 h-10 px-3 border rounded-md outline-none"
              />
            </div>
            <div className="flex flex-col">
              <label
                htmlFor="payment-method"
                className="font-semibold text-gray-700"
              >
                Payment Method
              </label>
              <select
                id="select-method"
                className="w-full mt-2 h-10 px-3 border rounded-md outline-none"
              >
                <option value="" disabled selected>
                  Select Method
                </option>
                <option value="UPI">UPI</option>
                <option value="Card">Card</option>
              </select>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end px-2 sm:px-8 mt-6 gap-4">
            <button className="bg-green-600 text-white font-bold text-sm py-2 px-5 rounded-md hover:bg-green-700 transition-transform transform hover:scale-105">
              Filter
            </button>
            <button className="bg-blue-500 text-white font-bold text-sm py-2 px-5 rounded-md hover:bg-blue-600 transition-transform transform hover:scale-105">
              Export
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentInformation;
