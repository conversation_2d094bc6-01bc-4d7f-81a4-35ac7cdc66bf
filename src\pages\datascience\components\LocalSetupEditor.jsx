import React from "react";
import { ReusableCodeEditor } from "../../../components/ui";

const LocalSetupEditor = ({ onBack }) => {
  // Data science specific challenges
  const dsCodeChallenges = [
    {
      id: "ds-challenge-1",
      title: "Data Preprocessing",
      difficulty: "Beginner",
      description: "Clean and preprocess a dataset for analysis.",
      requirements: [
        "Load the dataset",
        "Handle missing values",
        "Normalize numerical features",
        "Encode categorical variables"
      ],
      template: `# Data Preprocessing Challenge
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, OneHotEncoder

# Load the dataset
data = pd.read_csv('sample_data.csv')

# TODO: Check for missing values and handle them

# TODO: Normalize numerical features

# TODO: Encode categorical variables

# Display the processed data
print(data.head())
`
    },
    {
      id: "ds-challenge-2",
      title: "Exploratory Data Analysis",
      difficulty: "Intermediate",
      description: "Perform EDA on a dataset to extract insights.",
      requirements: [
        "Calculate descriptive statistics",
        "Visualize distributions",
        "Identify correlations",
        "Generate insights"
      ],
      template: `# Exploratory Data Analysis Challenge
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Load the dataset
data = pd.read_csv('sample_data.csv')

# TODO: Calculate and display descriptive statistics

# TODO: Create visualizations for key variables

# TODO: Calculate and visualize correlation matrix

# TODO: Generate insights based on the analysis

`
    },
    {
      id: "ds-challenge-3",
      title: "Machine Learning Model",
      difficulty: "Advanced",
      description: "Build and evaluate a machine learning model.",
      requirements: [
        "Split data into train/test sets",
        "Train a model",
        "Evaluate performance",
        "Tune hyperparameters"
      ],
      template: `# Machine Learning Model Challenge
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
from sklearn.model_selection import GridSearchCV

# Load and prepare data
data = pd.read_csv('sample_data.csv')
X = data.drop('target', axis=1)
y = data['target']

# TODO: Split data into training and testing sets

# TODO: Create and train a machine learning model

# TODO: Evaluate the model performance

# TODO: Tune hyperparameters to improve performance

`
    }
  ];

  return (
    <div className="space-y-6">
      <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-2xl font-bold text-white">Local Development Environment</h3>
          <button 
            onClick={onBack}
            className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm flex items-center"
          >
            <span className="mr-1">←</span> Back
          </button>
        </div>
        <p className="text-white/80 mb-4">
          Practice data science coding in your local environment with these interactive challenges.
          Select a challenge below to start coding and test your data science skills.
        </p>
      </div>
      
      <ReusableCodeEditor 
        challenges={dsCodeChallenges}
        showPremiumOverlay={() => {}}
      />
    </div>
  );
};

export default LocalSetupEditor;