import { Link, useNavigate } from "react-router-dom";
import { useState } from "react";
import toast from "react-hot-toast";
import { Spinner } from "../../components/ui";
import axiosInstance from "../../utils/axiosInstance";

const logo = "/upcoding_logo.png";
const SignIn = () => {
  const navigate = useNavigate();
  const [image, setImage] = useState(null);
  const [file, setFile] = useState(null);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFile(file);
      const reader = new FileReader();
      reader.onloadend = () => setImage(reader.result);
      reader.readAsDataURL(file);
    }
  };

  const validateFields = () => {
    let newErrors = {};
    if (!name) newErrors.name = "Name is required";
    if (!email) newErrors.email = "Email is required";

    if (!password) {
      newErrors.password = "Password is required";
    } else if (password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }

    if (password !== confirmPassword)
      newErrors.confirmPassword = "Passwords do not match";

    if (!file) {
      toast.error("Please select an Image");
      return false;
    }

    setErrors(newErrors);
    setTimeout(() => setErrors({}), 3000);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignUp = async (e) => {
    e.preventDefault();
    if (!validateFields()) return;

    setLoading(true);
    const formData = new FormData();
    formData.append("name", name);
    formData.append("email", email);
    formData.append("password", password);
    formData.append("confirmPassword", confirmPassword);
    formData.append("image", file);

    try {
      const { data } = await axiosInstance.post(
        "/auth/register",
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
          withCredentials: true,
        }
      );

      if (data.success) {
        toast.success(data.message);
        navigate("/otp");
      } else {
        toast.error(data.error);
      }
    } catch (error) {
      console.error(
        "Registration Error:",
        error.response?.data || error.message
      );
      toast.error("Something went wrong!");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-gradient-to-br from-[#181c25] to-[#12171d] flex items-center justify-center">
      <div className="w-full max-w-6xl bg-black/60  rounded-2xl shadow-xl p-6 md:p-12 flex flex-col lg:flex-row gap-8 lg:gap-16">
        {/* left */}
        <div className="w-full lg:w-1/2 text-white flex flex-col justify-center">
          <div className="flex items-center mb-4">
            <img src={logo} alt="UpcodingLabs" className="w-22 h-20 mr-3" />
            <span className="text-5xl md:text-5xl font-extrabold bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent leading-tight md:leading-snug">
              Welcome to Upcoding
            </span>
          </div>
          <h3 className="text-3xl font-bold mb-6">Why Join Us</h3>
          <ul className="space-y-4 text-gray-300 text-base">
            <li className="flex items-start gap-3">
              🚀 <span>Hands-on coding environments—no setup required</span>
            </li>
            <li className="flex items-start gap-3">
              🧠 <span>Interactive challenges to boost real-world skills</span>
            </li>
            <li className="flex items-start gap-3">
              📈 <span>Progress tracking to measure your learning journey</span>
            </li>
            <li className="flex items-start gap-3">
              👥 <span>Learn with peers in a thriving dev community</span>
            </li>
            <li className="flex items-start gap-3">
              🔒 <span>No hidden charges—get started for free</span>
            </li>
          </ul>
        </div>

        {/* Right:  Sign-Up Form */}
        <div className="w-full lg:w-1/2 flex flex-col items-center">
          {/* <h2 className="text-4xl md:text-5xl font-extrabold bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent mb-6 text-center">
            Create Your Account
          </h2> */}

          {/* Avatar Upload */}
          <label className="cursor-pointer flex flex-col items-center mb-2">
            <input
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleImageChange}
            />
            <div className="w-20 h-20 rounded-full bg-gray-800 border-2 border-gray-600 flex items-center justify-center overflow-hidden">
              {image ? (
                <img
                  src={image}
                  alt="Avatar"
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-white text-xl">📷</span>
              )}
            </div>
            <span className="text-xs text-gray-400 mt-1">
              Upload Profile Photo
            </span>
          </label>

          <form
            onSubmit={handleSignUp}
            className="w-full max-w-md flex flex-col gap-4"
          >
            <input
              type="text"
              placeholder="Name"
              className="px-4 py-3 rounded-lg border border-gray-700 bg-[#181e24] text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
            {errors.name && (
              <p className="text-red-400 text-xs">{errors.name}</p>
            )}

            <input
              type="email"
              placeholder="Email"
              className="px-4 py-3 rounded-lg border border-gray-700 bg-[#181e24] text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            {errors.email && (
              <p className="text-red-400 text-xs">{errors.email}</p>
            )}

            {/* Password */}
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                className="px-4 py-3 rounded-lg border border-gray-700 bg-[#181e24] text-white focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-blue-500"
              >
                {showPassword ? "Hide" : "Show"}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-400 text-xs">{errors.password}</p>
            )}

            {/* Confirm Password */}
            <div className="relative">
              <input
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm Password"
                className="px-4 py-3 rounded-lg border border-gray-700 bg-[#181e24] text-white focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-blue-500"
              >
                {showConfirmPassword ? "Hide" : "Show"}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="text-red-400 text-xs">{errors.confirmPassword}</p>
            )}

            <button
              type="submit"
              className="w-full py-3 rounded-lg bg-blue-600 text-white font-semibold text-lg mt-2 hover:bg-blue-700 transition"
              disabled={loading}
            >
              {loading ? <Spinner /> : "Sign Up"}
            </button>
          </form>

          {/* Google Sign-In Button */}
          <button
            type="button"
            className="w-full max-w-md mt-4 py-2 rounded-lg border border-gray-700 flex items-center justify-center bg-[#181e24] text-white font-semibold hover:bg-gray-800 transition"
          >
            <img
              src="/images/google.jpg"
              alt="Google"
              className="w-5 h-5 mr-3"
            />
            Sign In With Google
          </button>

          {/* Link to Login */}
          <p className="text-center text-gray-400 mt-4 text-sm">
            Already have an account?{" "}
            <Link to="/login" className="text-blue-400 hover:underline">
              Log In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default SignIn;
