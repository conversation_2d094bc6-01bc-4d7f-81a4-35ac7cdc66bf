import React from "react";
import { FaTerminal, FaTimes } from "react-icons/fa";

const ConsolePanel = ({
  consoleOutput,
  layout,
  onClearConsole,
  onToggleConsole
}) => {
  if (!layout.showConsole) return null;

  return (
    <div className="bg-gray-900 border-t border-gray-700" style={{ height: `${layout.consoleHeight}px` }}>
      <div className="bg-gray-800 px-4 py-2 border-b border-gray-700 flex items-center justify-between">
        <h4 className="text-sm font-medium text-yellow-400 flex items-center">
          <FaTerminal className="mr-2" />
          Console
        </h4>
        <div className="flex items-center space-x-2">
          <button
            onClick={onClearConsole}
            className="px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 rounded transition-colors"
          >
            Clear
          </button>
          <button
            onClick={onToggleConsole}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
            title="Close Console"
          >
            <FaTimes className="text-xs text-gray-400" />
          </button>
        </div>
      </div>
      
      <div className="p-4 h-full overflow-y-auto font-mono text-sm">
        {consoleOutput.map((log, index) => (
          <div key={index} className="mb-1 flex items-start space-x-2">
            <span className="text-gray-500 text-xs">
              {log.timestamp.toLocaleTimeString()}
            </span>
            <span className={`${
              log.type === 'error' ? 'text-red-400' :
              log.type === 'success' ? 'text-green-400' :
              log.type === 'warning' ? 'text-yellow-400' :
              'text-gray-300'
            }`}>
              {log.message}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ConsolePanel;
