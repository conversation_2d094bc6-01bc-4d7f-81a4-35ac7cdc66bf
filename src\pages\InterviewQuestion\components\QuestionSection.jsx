import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

const QuestionSection = ({ interviewData }) => {
  const [openIndex, setOpenIndex] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [questionsPerPage] = useState(10);

  // Reset open answer when page changes
  useEffect(() => {
    setOpenIndex(null);
  }, [currentPage]);

  const toggleAnswer = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  
  // Get current questions
  const indexOfLastQuestion = currentPage * questionsPerPage;
  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;
  const currentQuestions = interviewData.slice(indexOfFirstQuestion, indexOfLastQuestion);
  
  // Calculate total pages
  const totalPages = Math.ceil(interviewData.length / questionsPerPage);
  
  // Go to specific page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  
  // Go to next page
  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };
  
  // Go to previous page
  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
      },
    },
  };

  return (
    <section className="py-10" id="interview-questions">
      <div className="bg-white rounded-xl shadow-md p-6 mb-8">
        <div className="flex items-center gap-3 mb-6">
          <div className="bg-blue-100 text-blue-600 p-3 rounded-lg">
            <i className="fas fa-question-circle text-xl"></i>
          </div>
          <h2 className="text-3xl font-bold text-gray-800">
            Top Interview Questions
          </h2>
        </div>
        
        <p className="text-gray-600 mb-8">
          Master these frequently asked interview questions from top multinational companies. 
          Click on each question to reveal detailed answers and explanations.
        </p>

        <motion.ul 
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="divide-y divide-gray-200"
        >
          {currentQuestions.map((item, index) => (
            <motion.li key={index} variants={itemVariants} className="py-3">
              <div 
                onClick={() => toggleAnswer(index)} 
                className="flex justify-between items-center cursor-pointer py-3 hover:bg-gray-50 px-3 rounded-lg transition-colors"
              >
                <div className="flex gap-3 items-center">
                  <span className="bg-gray-100 text-gray-700 font-semibold rounded-full w-8 h-8 flex items-center justify-center">
                    {indexOfFirstQuestion + index + 1}
                  </span>
                  <h3 className="font-medium text-lg text-gray-800">{item.question}</h3>
                </div>
                <div className={`transition-transform duration-300 ${openIndex === index ? 'rotate-180' : ''}`}>
                  <i className="fas fa-chevron-down text-gray-500"></i>
                </div>
              </div>

              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="bg-blue-50 p-6 rounded-lg my-3 ml-10 border-l-4 border-blue-500">
                      <div className="prose prose-blue max-w-none text-gray-700">
                        {item.answer}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.li>
          ))}
        </motion.ul>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="flex justify-center items-center mt-8 space-x-2"
          >
            {/* Previous Button */}
            <button
              onClick={prevPage}
              disabled={currentPage === 1}
              className={`px-4 py-2 rounded-md flex items-center gap-1 transition-colors
                ${currentPage === 1 
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                  : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                }`}
            >
              <i className="fas fa-chevron-left text-sm"></i>
              Prev
            </button>
            
            {/* Page Numbers - with ellipsis for many pages */}
            <div className="flex items-center space-x-1">
              {totalPages <= 7 ? (
                // Show all page numbers if 7 or fewer
                Array.from({ length: totalPages }, (_, i) => (
                  <button
                    key={i}
                    onClick={() => paginate(i + 1)}
                    className={`w-10 h-10 rounded-md flex items-center justify-center transition-colors
                      ${currentPage === i + 1
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                  >
                    {i + 1}
                  </button>
                ))
              ) : (
                // Show ellipsis for many pages
                <>
                  {/* First page always shown */}
                  <button
                    onClick={() => paginate(1)}
                    className={`w-10 h-10 rounded-md flex items-center justify-center transition-colors
                      ${currentPage === 1
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                  >
                    1
                  </button>
                  
                  {/* Show ellipsis or second page */}
                  {currentPage > 3 && (
                    <span className="w-10 h-10 flex items-center justify-center text-gray-500">
                      ...
                    </span>
                  )}
                  
                  {/* Current page range */}
                  {Array.from(
                    { length: 3 },
                    (_, i) => {
                      const pageNum = currentPage > 3 
                        ? currentPage - 1 + i 
                        : 2 + i;
                      
                      return pageNum <= totalPages - 1 && pageNum > 1 && (
                        <button
                          key={pageNum}
                          onClick={() => paginate(pageNum)}
                          className={`w-10 h-10 rounded-md flex items-center justify-center transition-colors
                            ${currentPage === pageNum
                              ? 'bg-blue-600 text-white' 
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                        >
                          {pageNum}
                        </button>
                      );
                    }
                  )}
                  
                  {/* Show ellipsis or second-to-last page */}
                  {currentPage < totalPages - 2 && (
                    <span className="w-10 h-10 flex items-center justify-center text-gray-500">
                      ...
                    </span>
                  )}
                  
                  {/* Last page always shown */}
                  <button
                    onClick={() => paginate(totalPages)}
                    className={`w-10 h-10 rounded-md flex items-center justify-center transition-colors
                      ${currentPage === totalPages
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                  >
                    {totalPages}
                  </button>
                </>
              )}
            </div>
            
            {/* Page indicator */}
            <div className="text-gray-500 text-sm ml-2">
              <span className="hidden sm:inline">Page </span>
              {currentPage} of {totalPages}
            </div>
            
            {/* Next Button */}
            <button
              onClick={nextPage}
              disabled={currentPage === totalPages}
              className={`px-4 py-2 rounded-md flex items-center gap-1 transition-colors
                ${currentPage === totalPages 
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                  : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                }`}
            >
              Next
              <i className="fas fa-chevron-right text-sm"></i>
            </button>
          </motion.div>
        )}
      </div>
    </section>
  );
};

export default QuestionSection;
