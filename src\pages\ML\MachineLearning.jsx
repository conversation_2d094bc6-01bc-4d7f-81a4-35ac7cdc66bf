import React from "react";
import Introduction from "./Introduction";
import ChapterNew from "./ChapterNew";
import CaseStudyNew from "./CaseStudyNew";
import FAQS from "./FAQS";
import MLHero from "./components/MLHero";
import MLLabEnvironmentNew from "./components/MLLabEnvironmentNew";
import MLLiveClasses from "./components/MLLiveClasses";
import MLPremiumModal from "./components/MLPremiumModal";
import { CourseResourcesSection } from "../../components/ui";
import { ReviewManager } from "../../components/reviews";
import useSidebarState from "../../hooks/useSidebarState";

const MachineLearning = () => {
  const courseConfig = {
    title: "Machine Learning Course Resources",
    subtitle: "Master machine learning through comprehensive modules, practical applications, live workshops, and expert guidance",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-gray-300"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn ML fundamentals and core algorithms",
        icon: "🧠",
        component: Introduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Lab Environment",
        description: "Practice with ML datasets and tools",
        icon: "🔬",
        component: MLLabEnvironmentNew,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Classes",
        description: "Join expert-led ML workshops",
        icon: "🎓",
        component: MLLiveClasses,
        props: {}
      },
      {
        id: "Chapter",
        title: "Chapters",
        description: "Dive deep into machine learning concepts",
        icon: "📚",
        component: ChapterNew,
        props: {}
      },
      {
        id: "CaseStudy",
        title: "Case Study",
        description: "Explore real-world ML applications",
        icon: "💼",
        component: CaseStudyNew,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQ's",
        description: "Get answers to ML questions",
        icon: "❓",
        component: FAQS,
        props: {}
      },
      {
        id: "Reviews",
        title: "Reviews",
        description: "See what students are saying about this course",
        icon: "⭐",
        component: () => (
          <ReviewManager
            courseId="ml-course"
            courseName="Machine Learning Course"
            showWriteReview={true}
            showStats={true}
          />
        ),
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={MLHero}
      PremiumModalComponent={MLPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default MachineLearning;
