import React from 'react';
import { motion } from 'framer-motion';

export const GradientOrbs = () => (
  <>
    <motion.div
      animate={{
        x: [0, 40, 0],
        y: [0, -20, 0],
      }}
      transition={{
        duration: 25,
        repeat: Infinity,
        ease: "easeInOut",
      }}
      className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-gradient-to-br from-[#1a3c50]/30 to-[#010509]/20 rounded-full opacity-20 blur-[100px]"
    />
    <motion.div
      animate={{
        x: [0, -30, 0],
        y: [0, 30, 0],
      }}
      transition={{
        duration: 20,
        repeat: Infinity,
        ease: "easeInOut",
      }}
      className="absolute bottom-1/4 right-1/4 w-[400px] h-[400px] bg-gradient-to-br from-[#1a3c50]/20 to-[#010509]/15 rounded-full opacity-20 blur-[80px]"
    />
    <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0wIDBoNjB2NjBIMHoiLz48cGF0aCBkPSJNMzAgMzFhMSAxIDAgMTEtMi0uMDAxIDEgMSAwIDAxMiAuMDAxeiIgZmlsbD0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjAyKSIgZmlsbC1ydWxlPSJub256ZXJvIi8+PC9nPjwvc3ZnPg==')] opacity-30" />
  </>
);

export const FloatingParticles = () => (
  <>
    {[...Array(8)].map((_, i) => (
      <motion.div
        key={i}
        animate={{
          y: [0, -15, 0],
          opacity: [0.2, 0.4, 0.2],
        }}
        transition={{
          duration: 4 + i,
          repeat: Infinity,
          delay: i * 0.5,
        }}
        className="absolute w-1 h-1 bg-white/30 rounded-full"
        style={{
          left: `${10 + i * 10}%`,
          top: `${20 + (i % 5) * 15}%`,
        }}
      />
    ))}
  </>
);

export const BackToTopButton = ({ handleScrollToTop }) => (
  <motion.button
    initial={{ scale: 0 }}
    animate={{ scale: 1 }}
    whileHover={{ scale: 1.1 }}
    whileTap={{ scale: 0.9 }}
    className="fixed bottom-8 right-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 z-50"
    onClick={handleScrollToTop}
    style={{ backdropFilter: "blur(10px)" }}
  >
    <motion.span
      animate={{ y: [-2, 2, -2] }}
      transition={{ duration: 2, repeat: Infinity }}
      className="text-2xl block"
    >
      ↑
    </motion.span>
  </motion.button>
);
