import { motion } from "framer-motion";
import { Footer } from "../layout";
import { Widgets } from "../ui";
import HeroSection from "../sections/HeroSection";
import CourseSection from "../sections/CourseSection";
import LearningPathsSection from "../sections/LearningPathsSection";
import { BackToTopButton } from "../sections/common/BackgroundEffects";

const Content = ({ toggleSidebar }) => {
  // const location = useLocation();

  const handleScrollToTop = (e) => {
    e.preventDefault();
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      className="flex flex-col min-h-screen bg-gradient-to-br from-slate-50 to-blue-50"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Hero Section */}
      <HeroSection itemVariants={itemVariants} />

      {/* Main Content Sections */}
      <div className="flex-1 bg-gradient-to-br from-[#1a3c50] to-[#010509]">
        {/* Courses Section */}
        <CourseSection itemVariants={itemVariants} />

        {/* Learning Paths Section */}
        <LearningPathsSection itemVariants={itemVariants} />

        {/* Widgets */}
        <motion.div variants={itemVariants} className="py-8 relative overflow-hidden">
          <div className="relative z-10 text-white">
            <Widgets />
          </div>
        </motion.div>
      </div>

      {/* Footer */}
      <motion.div variants={itemVariants} className="bg-[#010509] border-t border-white/10">
        <Footer />
      </motion.div>

      {/* Back to Top Button */}
      <BackToTopButton handleScrollToTop={handleScrollToTop} />
    </motion.div>
  );
};

export default Content;
