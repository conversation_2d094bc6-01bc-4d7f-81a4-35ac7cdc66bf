import { useState } from "react";

const useSidebarState = (initialState = true) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(initialState);

  const toggleSidebar = () => {
    setIsSidebarOpen((prev) => !prev);
  };

  const openSidebar = () => {
    setIsSidebarOpen(true);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  return {
    isSidebarOpen,
    toggleSidebar,
    openSidebar,
    closeSidebar,
  };
};

export default useSidebarState;
