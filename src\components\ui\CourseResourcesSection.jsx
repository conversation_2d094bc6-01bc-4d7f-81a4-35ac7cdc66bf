
import React, { useState } from "react";
import InternshipApply from "../../components/InternshipApply";


import { LoadingSpinner } from "./index";

const CourseResourcesSection = ({ 
  courseConfig,
  HeroComponent,
  LayoutComponent,
  PremiumModalComponent,
  useSidebarHook
}) => {
  const loading = false;
  const { isSidebarOpen, toggleSidebar } = useSidebarHook ? useSidebarHook(true) : { isSidebarOpen: false, toggleSidebar: () => {} };
  const [activeSection, setActiveSection] = useState(null);
  const [showModal, setShowModal] = useState(false);

  const showPremiumOverlay = () => {
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
  };

  const handleCardSelect = (cardId) => {
    setActiveSection(cardId);
  };

  const onBackToCourse = () => {
    setActiveSection(null);
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  const renderContent = () => {
    const section = courseConfig.sections.find(s => s.id === activeSection);
    if (!section) return null;

    const Component = section.component;
    return (
      <Component 
        showPremiumOverlay={showPremiumOverlay} 
        onBackToCourse={onBackToCourse}
        {...section.props}
      />
    );
  };

  // Course Cards Component
  const CourseCards = ({ onCardSelect }) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {courseConfig.sections.map((section, idx) => (
        <React.Fragment key={section.id}>
          <div
            onClick={() => onCardSelect(section.id)}
            className="relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 transform hover:scale-105 hover:shadow-2xl group bg-gradient-to-br from-[#118c6e] to-[#0d6b56] border border-white/10"
          >
            <div className="absolute inset-0 bg-black/20"></div>
            <div className="relative z-10 p-6 text-white">
              <div className="mb-4 opacity-80 group-hover:opacity-100 transition-opacity text-white">
                {section.icon}
              </div>
              <h3 className="text-xl font-bold mb-2 group-hover:text-white transition-colors">
                {section.title}
              </h3>
              <p className="text-sm opacity-80 group-hover:opacity-100 transition-opacity">
                {section.description}
              </p>
            </div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
          </div>
        </React.Fragment>
      ))}
      <InternshipApply />
    </div>
  );

  const content = (
    <>
      {HeroComponent && <HeroComponent showPremiumOverlay={showPremiumOverlay} />}
      <div className="min-h-screen bg-gradient-to-br from-[#1a3c50] to-[#010509] flex items-center justify-center">
        <div className="container mx-auto px-4 transition-all duration-300 !pt-0 !mt-0">
        {/* Section Header */}
        <div className="text-center !mt-0 !mb-0 !pt-0 !pb-0">
          <h2 className={`text-3xl font-bold mb-2 ${courseConfig.theme?.titleColor || 'text-white'}`}>
            {courseConfig.title}
          </h2>
          <p className={`max-w-2xl mx-auto ${courseConfig.theme?.subtitleColor || 'text-gray-400'}`}>
            {courseConfig.subtitle}
          </p>
        </div>

        {/* Course Cards */}
        <CourseCards onCardSelect={handleCardSelect} />
        
        {/* Selected Content */}
        {activeSection && (
          <div className="mt-12 content-animate">
            <style dangerouslySetInnerHTML={{ __html: `
              @keyframes fadeInUp {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
              }
              .content-animate {
                animation: fadeInUp 0.5s ease-out forwards;
              }
            `}} />
            {renderContent()}
          </div>
        )}
        </div>
      </div>
      {PremiumModalComponent && <PremiumModalComponent isOpen={showModal} onClose={closeModal} />}
    </>
  );

  // If LayoutComponent is provided, wrap content in it
  if (LayoutComponent) {
    return (
      <LayoutComponent 
        isSidebarOpen={isSidebarOpen}
        toggleSidebar={toggleSidebar}
      >
        {content}
      </LayoutComponent>
    );
  }

  // For courses without layout wrapper (like AI)
  return <div>{content}</div>;
};

export default CourseResourcesSection;
