import React from "react";
import { motion } from "framer-motion";

const StatsSection = ({ statsData, itemVariants }) => {
  return (
    <motion.div
      variants={itemVariants}
      className="mt-16 grid md:grid-cols-3 gap-6"
    >
      {statsData.map((stat, index) => (
        <motion.div
          key={index}
          whileHover={{ y: -5 }}
          className="bg-white p-6 rounded-2xl shadow-lg text-center hover:shadow-xl transition-all duration-300"
        >
          <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${stat.color} flex items-center justify-center text-white text-2xl`}>
            {stat.icon}
          </div>
          <div className="text-3xl font-bold text-gray-900 mb-2">
            {stat.number}
          </div>
          <div className="text-gray-600">{stat.label}</div>
        </motion.div>
      ))}
    </motion.div>
  );
};

export default StatsSection;
