// Review Service for managing course reviews
class ReviewService {
  constructor() {
    this.reviews = this.loadReviews();
  }

  // Load reviews from localStorage
  loadReviews() {
    const stored = localStorage.getItem('courseReviews');
    if (stored) {
      return JSON.parse(stored);
    }
    
    // Default mock data
    return [
      {
        id: 1,
        courseId: 'pythoncourse',
        courseName: 'Python Master Course',
        userId: 'user1',
        userName: '<PERSON>',
        userEmail: '<EMAIL>',
        rating: 5,
        title: 'Excellent Course!',
        comment: 'This course exceeded my expectations. The instructor explained complex concepts in a very simple and understandable way.',
        date: new Date('2024-01-15').toISOString(),
        isApproved: true,
        adminReply: null
      },
      {
        id: 2,
        courseId: 'pythoncourse',
        courseName: 'Python Master Course',
        userId: 'user2',
        userName: '<PERSON>',
        userEmail: '<EMAIL>',
        rating: 4,
        title: 'Great Content',
        comment: 'Very comprehensive course with practical examples. Some sections could use more detailed explanations.',
        date: new Date('2024-01-10').toISOString(),
        isApproved: true,
        adminReply: 'Thank you for your feedback! We\'re working on adding more detailed explanations.'
      },
      {
        id: 3,
        courseId: 'ml-course',
        courseName: 'Machine Learning Course',
        userId: 'user3',
        userName: 'Alex Johnson',
        userEmail: '<EMAIL>',
        rating: 5,
        title: 'Outstanding ML Course',
        comment: 'Perfect introduction to machine learning. The hands-on projects were incredibly valuable.',
        date: new Date('2024-01-08').toISOString(),
        isApproved: true,
        adminReply: null
      },
      {
        id: 4,
        courseId: 'datascience-course',
        courseName: 'Data Science Course',
        userId: 'user4',
        userName: 'Sarah Wilson',
        userEmail: '<EMAIL>',
        rating: 4,
        title: 'Solid Foundation',
        comment: 'Good course for beginners. Covers all the essential topics in data science.',
        date: new Date('2024-01-05').toISOString(),
        isApproved: true,
        adminReply: null
      },
      {
        id: 5,
        courseId: 'fullstack-course',
        courseName: 'Full Stack Development Course',
        userId: 'user5',
        userName: 'Mike Brown',
        userEmail: '<EMAIL>',
        rating: 5,
        title: 'Complete Package',
        comment: 'This course covers everything you need to become a full-stack developer. Highly recommended!',
        date: new Date('2024-01-03').toISOString(),
        isApproved: true, // Now approved for immediate visibility
        adminReply: null
      },
      {
        id: 6,
        courseId: 'ai-course',
        courseName: 'Artificial Intelligence Course',
        userId: 'user6',
        userName: 'Emily Davis',
        userEmail: '<EMAIL>',
        rating: 5,
        title: 'Cutting-edge AI Content',
        comment: 'Amazing course with latest AI techniques and practical implementations. The projects are very engaging.',
        date: new Date('2024-01-02').toISOString(),
        isApproved: true,
        adminReply: null
      },
      {
        id: 7,
        courseId: 'sql-course',
        courseName: 'SQL Course',
        userId: 'user7',
        userName: 'David Wilson',
        userEmail: '<EMAIL>',
        rating: 4,
        title: 'Great SQL Foundation',
        comment: 'Solid course for learning SQL fundamentals. Good examples and practice problems.',
        date: new Date('2024-01-01').toISOString(),
        isApproved: true,
        adminReply: 'Thank you! We\'re glad you found the SQL examples helpful.'
      },
      {
        id: 8,
        courseId: 'dsa-course',
        courseName: 'Data Structures & Algorithms Course',
        userId: 'user8',
        userName: 'Lisa Chen',
        userEmail: '<EMAIL>',
        rating: 5,
        title: 'Excellent DSA Course',
        comment: 'Perfect for interview preparation. Clear explanations and challenging problems.',
        date: new Date('2023-12-30').toISOString(),
        isApproved: true,
        adminReply: null
      },
      {
        id: 9,
        courseId: 'javascript-course',
        courseName: '30 Days JavaScript Course',
        userId: 'user9',
        userName: 'Tom Anderson',
        userEmail: '<EMAIL>',
        rating: 4,
        title: 'Good JavaScript Learning Path',
        comment: 'Well-structured 30-day program. Helped me understand JavaScript concepts step by step.',
        date: new Date('2023-12-28').toISOString(),
        isApproved: true,
        adminReply: null
      },
      {
        id: 10,
        courseId: 'system-design-course',
        courseName: 'System Design Course',
        userId: 'user10',
        userName: 'Rachel Green',
        userEmail: '<EMAIL>',
        rating: 5,
        title: 'Excellent System Design Course',
        comment: 'Perfect for understanding large-scale system architecture. Great real-world examples and case studies.',
        date: new Date('2023-12-25').toISOString(),
        isApproved: true,
        adminReply: 'Thank you! We\'re glad the real-world examples were helpful for your learning.'
      },
      {
        id: 11,
        courseId: 'gg75-course',
        courseName: 'GG 75 Interview Questions',
        userId: 'user11',
        userName: 'Mark Johnson',
        userEmail: '<EMAIL>',
        rating: 5,
        title: 'Best Interview Prep Resource',
        comment: 'These 75 questions cover all the important topics. Helped me crack my FAANG interview!',
        date: new Date('2023-12-20').toISOString(),
        isApproved: true,
        adminReply: 'Congratulations on your FAANG interview success! 🎉'
      }
    ];
  }

  // Save reviews to localStorage
  saveReviews() {
    localStorage.setItem('courseReviews', JSON.stringify(this.reviews));
  }

  // Get all reviews
  getAllReviews() {
    return this.reviews;
  }

  // Get reviews for a specific course
  getCourseReviews(courseId, approvedOnly = true) {
    return this.reviews.filter(review => 
      review.courseId === courseId && (!approvedOnly || review.isApproved)
    );
  }

  // Get review statistics for a course
  getCourseStats(courseId) {
    const courseReviews = this.getCourseReviews(courseId, true);
    
    if (courseReviews.length === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
      };
    }

    const totalRating = courseReviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = (totalRating / courseReviews.length).toFixed(1);
    
    const ratingDistribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
    courseReviews.forEach(review => {
      ratingDistribution[review.rating]++;
    });

    return {
      totalReviews: courseReviews.length,
      averageRating: parseFloat(averageRating),
      ratingDistribution
    };
  }

  // Add a new review
  addReview(reviewData) {
    const newReview = {
      id: Date.now(),
      ...reviewData,
      date: new Date().toISOString(),
      isApproved: true, // Auto-approve reviews for immediate publishing
      adminReply: null
    };

    this.reviews.push(newReview);
    this.saveReviews();
    return newReview;
  }

  // Update review approval status
  approveReview(reviewId, isApproved) {
    const review = this.reviews.find(r => r.id === reviewId);
    if (review) {
      review.isApproved = isApproved;
      this.saveReviews();
      return review;
    }
    return null;
  }

  // Add admin reply to review
  addAdminReply(reviewId, reply) {
    const review = this.reviews.find(r => r.id === reviewId);
    if (review) {
      review.adminReply = reply;
      this.saveReviews();
      return review;
    }
    return null;
  }

  // Delete a review
  deleteReview(reviewId) {
    const index = this.reviews.findIndex(r => r.id === reviewId);
    if (index !== -1) {
      const deleted = this.reviews.splice(index, 1)[0];
      this.saveReviews();
      return deleted;
    }
    return null;
  }

  // Get overall platform statistics
  getOverallStats() {
    const approvedReviews = this.reviews.filter(r => r.isApproved);
    
    if (approvedReviews.length === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        courseStats: {}
      };
    }

    const totalRating = approvedReviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = (totalRating / approvedReviews.length).toFixed(1);
    
    // Group by course
    const courseStats = {};
    approvedReviews.forEach(review => {
      if (!courseStats[review.courseId]) {
        courseStats[review.courseId] = {
          courseName: review.courseName,
          reviews: [],
          totalRating: 0
        };
      }
      courseStats[review.courseId].reviews.push(review);
      courseStats[review.courseId].totalRating += review.rating;
    });

    // Calculate average for each course
    Object.keys(courseStats).forEach(courseId => {
      const stats = courseStats[courseId];
      stats.averageRating = (stats.totalRating / stats.reviews.length).toFixed(1);
      stats.totalReviews = stats.reviews.length;
    });

    return {
      totalReviews: approvedReviews.length,
      averageRating: parseFloat(averageRating),
      courseStats
    };
  }
}

// Create singleton instance
const reviewService = new ReviewService();
export default reviewService;
