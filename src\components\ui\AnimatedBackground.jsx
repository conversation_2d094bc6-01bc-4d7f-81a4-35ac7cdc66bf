const AnimatedBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes float1 {
          0% { transform: translate(0, 0); }
          50% { transform: translate(100px, -100px); }
          100% { transform: translate(0, 0); }
        }
        @keyframes float2 {
          0% { transform: translate(0, 0); }
          50% { transform: translate(-50px, 100px); }
          100% { transform: translate(0, 0); }
        }
        .float-1 {
          animation: float1 20s linear infinite;
        }
        .float-2 {
          animation: float2 15s linear infinite;
        }
      `}} />
      
      {/* Floating Circle 1 */}
      <div className="absolute top-20 left-20 w-64 h-64 bg-blue-200 rounded-full opacity-10 float-1" />
      
      {/* Floating Circle 2 */}
      <div className="absolute bottom-20 right-20 w-48 h-48 bg-purple-200 rounded-full opacity-10 float-2" />
    </div>
  );
};

export default AnimatedBackground;
