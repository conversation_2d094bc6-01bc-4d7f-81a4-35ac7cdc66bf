import { useState, useEffect } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { motion } from "framer-motion";
import ReusableNavbar from "../layout/ReusableNavbar";

const CodeEditor = () => {
  const { id, mode } = useParams(); // mode can be 'solve' or 'solution'
  const navigate = useNavigate();
  const [code, setCode] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("javascript");
  const [isRunning, setIsRunning] = useState(false);
  const [output, setOutput] = useState("");
  const [showSolution, setShowSolution] = useState(false);

  // Real coding problems data
  const problems = {
    1: {
      id: 1,
      title: "Two Sum",
      difficulty: "Easy",
      description:
        "Given an array of integers 'nums' and an integer 'target', return indices of the two numbers such that they add up to 'target'.",
      explanation:
        "You can return the answer in any order. You may not use the same element twice. There is exactly one solution.",
      examples: [
        {
          input: "nums = [2,7,11,15], target = 9",
          output: "[0,1]",
          explanation: "Because nums[0] + nums[1] = 2 + 7 = 9"
        },
        {
          input: "nums = [3,2,4], target = 6",
          output: "[1,2]"
        },
        {
          input: "nums = [3,3], target = 6",
          output: "[0,1]"
        }
      ],
      constraints: [
        "2 <= nums.length <= 104",
        "-109 <= nums[i] <= 109",
        "-109 <= target <= 109",
        "Only one valid answer exists."
      ],
      starterCode: {
        javascript: `/**
 * @param {number[]} nums
 * @param {number} target
 * @return {number[]}
 */
function twoSum(nums, target) {
  // Write your code here
  
}`,
        python: `def twoSum(nums, target):
    # Write your code here
    
    pass`,
        java: `class Solution {
    public int[] twoSum(int[] nums, int target) {
        // Write your code here
        
    }
}`
      },
      solution: {
        javascript: `/**
 * @param {number[]} nums
 * @param {number} target
 * @return {number[]}
 */
function twoSum(nums, target) {
  const map = new Map();
  
  for (let i = 0; i < nums.length; i++) {
    const complement = target - nums[i];
    
    if (map.has(complement)) {
      return [map.get(complement), i];
    }
    
    map.set(nums[i], i);
  }
  
  return []; // No solution found
}`,
        python: `def twoSum(nums, target):
    # Create a dictionary to store values and their indices
    num_map = {}
    
    # Iterate through the array
    for i, num in enumerate(nums):
        # Calculate the complement needed
        complement = target - num
        
        # If complement exists in the map, return both indices
        if complement in num_map:
            return [num_map[complement], i]
        
        # Store current number and its index
        num_map[num] = i
        
    return []  # No solution found`,
        java: `class Solution {
    public int[] twoSum(int[] nums, int target) {
        Map<Integer, Integer> map = new HashMap<>();
        
        for (int i = 0; i < nums.length; i++) {
            int complement = target - nums[i];
            
            if (map.containsKey(complement)) {
                return new int[] {map.get(complement), i};
            }
            
            map.put(nums[i], i);
        }
        
        return new int[0]; // No solution found
    }
}`
      }
    },
    // Additional problems can be added here
    2: {
      id: 2,
      title: "Longest Substring Without Repeating Characters",
      difficulty: "Medium",
      description:
        "Given a string 's', find the length of the longest substring without repeating characters.",
      examples: [
        {
          input: 's = "abcabcbb"',
          output: '3',
          explanation: "The answer is 'abc', with the length of 3."
        },
        {
          input: 's = "bbbbb"',
          output: '1',
          explanation: "The answer is 'b', with the length of 1."
        },
        {
          input: 's = "pwwkew"',
          output: '3',
          explanation: "The answer is 'wke', with the length of 3."
        }
      ],
      constraints: [
        "0 <= s.length <= 5 * 104",
        "s consists of English letters, digits, symbols and spaces."
      ],
      starterCode: {
        javascript: `/**
 * @param {string} s
 * @return {number}
 */
function lengthOfLongestSubstring(s) {
  // Write your code here
  
}`,
        python: `def lengthOfLongestSubstring(s):
    # Write your code here
    
    pass`
      },
      solution: {
        javascript: `/**
 * @param {string} s
 * @return {number}
 */
function lengthOfLongestSubstring(s) {
  let max_length = 0;
  let start = 0;
  const char_map = new Map();
  
  for (let end = 0; end < s.length; end++) {
    const current_char = s[end];
    
    if (char_map.has(current_char)) {
      // Move the start pointer to position after the last occurrence
      // of current_char, or keep it where it is if the last occurrence
      // is before the current window start
      start = Math.max(start, char_map.get(current_char) + 1);
    }
    
    char_map.set(current_char, end);
    max_length = Math.max(max_length, end - start + 1);
  }
  
  return max_length;
}`,
        python: `def lengthOfLongestSubstring(s):
    # Use a dictionary to store the position of each character
    char_map = {}
    start = 0
    max_length = 0
    
    for end in range(len(s)):
        # If character exists in the map and its position is after the current window start
        if s[end] in char_map:
            # Move start to position after the last occurrence
            start = max(start, char_map[s[end]] + 1)
            
        # Add or update the character position
        char_map[s[end]] = end
        
        # Update max length found so far
        max_length = max(max_length, end - start + 1)
    
    return max_length`
      }
    }
  };

  const currentProblem = problems[id];

  useEffect(() => {
    if (!currentProblem) {
      navigate("/");
      return;
    }

    if (mode === "solution") {
      setShowSolution(true);
      setCode(currentProblem.solution[selectedLanguage] || "");
    } else {
      setShowSolution(false);
      setCode(currentProblem.starterCode[selectedLanguage] || "");
    }
  }, [id, mode, selectedLanguage, currentProblem, navigate]);

  const handleLanguageChange = (language) => {
    setSelectedLanguage(language);
    if (showSolution) {
      setCode(currentProblem.solution[language] || "");
    } else {
      setCode(currentProblem.starterCode[language] || "");
    }
  };

  const handleRunCode = () => {
    if (!code.trim()) {
      setOutput("❌ Error: Please write some code first!");
      return;
    }

    setIsRunning(true);
    setOutput("🔄 Running your code...\n");
    
    setTimeout(() => {
      try {
        let result = "";
        const startTime = performance.now();
        
        if (selectedLanguage === "javascript") {
          // Create a safe environment for JavaScript execution
          const originalConsoleLog = console.log;
          const outputs = [];
          
          // Override console.log to capture output
          console.log = (...args) => {
            outputs.push(args.map(arg => 
              typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' '));
          };
          
          try {
            // Execute the user's code
            const func = new Function('console', `
              ${code}
              
              // Test the function if it exists
              if (typeof twoSum !== 'undefined') {
                console.log("Testing twoSum function:");
                console.log("Input: [2,7,11,15], target: 9");
                console.log("Output:", JSON.stringify(twoSum([2,7,11,15], 9)));
                console.log("\\nInput: [3,2,4], target: 6");
                console.log("Output:", JSON.stringify(twoSum([3,2,4], 6)));
              }
              
              if (typeof lengthOfLongestSubstring !== 'undefined') {
                console.log("Testing lengthOfLongestSubstring function:");
                console.log("Input: 'abcabcbb'");
                console.log("Output:", lengthOfLongestSubstring('abcabcbb'));
                console.log("\\nInput: 'bbbbb'");
                console.log("Output:", lengthOfLongestSubstring('bbbbb'));
              }
            `);
            
            func(console);
            
            result = outputs.join('\n');
            
            // If no output was captured, provide a hint
            if (!result) {
              result = "⚠️ No output detected. Did you add any console.log statements?";
            }
            
          } finally {
            // Restore original console.log
            console.log = originalConsoleLog;
          }
          
        } else if (selectedLanguage === "python") {
          // For Python, we'll simulate execution since we can't run Python in browser
          result = `🐍 Python Simulation:
Note: Python code cannot be executed directly in the browser.
Your code syntax appears to be valid.

To test your Python code:
1. Copy the code
2. Run it in a Python environment
3. Or use online Python interpreters

Your code:
${code}`;
          
        } else if (selectedLanguage === "java") {
          // For Java, we'll simulate execution
          result = `☕ Java Simulation:
Note: Java code cannot be executed directly in the browser.
Your code syntax appears to be valid.

To test your Java code:
1. Copy the code
2. Compile and run in a Java environment
3. Or use online Java compilers

Your code:
${code}`;
        }
        
        const endTime = performance.now();
        const executionTime = Math.round(endTime - startTime);
        
        const finalOutput = `✅ Execution completed in ${executionTime}ms\n\n${result}`;
        setOutput(finalOutput);
        
      } catch (error) {
        setOutput(`❌ Error: ${error.message}\n\nPlease check your code syntax and try again.`);
      }
      
      setIsRunning(false);
    }, 500); // Short delay for better UX
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case "Easy": return "text-green-600 bg-green-100";
      case "Medium": return "text-yellow-600 bg-yellow-100";
      case "Hard": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  if (!currentProblem) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-500 mb-2">Problem Not Found</h2>
          <p className="text-gray-600 mb-4">The problem you're looking for doesn't exist.</p>
          <Link 
            to="/"
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navbar */}
      <ReusableNavbar />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link to="/" className="text-blue-600 hover:underline flex items-center gap-1">
            <span>← Back to Problems</span>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Problem Description */}
          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <div className="mb-4">
              <div className="flex justify-between items-start">
                <h1 className="text-2xl font-bold">{currentProblem.title}</h1>
                <span className={`inline-block rounded-full px-3 py-1 text-sm font-semibold ${getDifficultyColor(currentProblem.difficulty)}`}>
                  {currentProblem.difficulty}
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-semibold mb-2">Problem:</h3>
                <p className="text-gray-700 mb-4">
                  {currentProblem.description}
                </p>
                {currentProblem.explanation && (
                  <p className="text-gray-700 mb-4">
                    {currentProblem.explanation}
                  </p>
                )}

                <h3 className="text-lg font-semibold mb-3">Examples:</h3>
                <div className="space-y-4">
                  {currentProblem.examples.map((example, index) => (
                    <div key={index} className="bg-gray-50 p-3 rounded-md">
                      <div>
                        <strong>Input:</strong> {example.input}
                      </div>
                      <div>
                        <strong>Output:</strong> {example.output}
                      </div>
                      {example.explanation && (
                        <div>
                          <strong>Explanation:</strong> {example.explanation}
                        </div>
                      )}
                    </div>
                  ))}

                  <h3 className="text-lg font-semibold mb-3">Constraints:</h3>
                  <ul className="list-disc list-inside space-y-1">
                    {currentProblem.constraints.map((constraint, index) => (
                      <li key={index} className="text-gray-700">{constraint}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Code Editor */}
          <motion.div
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">
                {showSolution ? "Solution" : "Code Editor"}
              </h2>
              
              <div className="flex items-center gap-4">
                {/* Language Selector */}
                <select
                  value={selectedLanguage}
                  onChange={(e) => handleLanguageChange(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="javascript">JavaScript</option>
                  <option value="python">Python</option>
                  {currentProblem.starterCode.java && (
                    <option value="java">Java</option>
                  )}
                </select>

                {/* API Tester Button */}
                <Link
                  to="/api-tester"
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  API Tester
                </Link>

                {/* Mode Toggle */}
                {!showSolution && (
                  <Link
                    to={`/code-editor/${id}/solution`}
                    className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    View Solution
                  </Link>
                )}
                
                {showSolution && (
                  <Link
                    to={`/code-editor/${id}/solve`}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Try Solving
                  </Link>
                )}
              </div>
            </div>

            {/* Code Area */}
            <div className="bg-gray-900 rounded-lg overflow-hidden border-2 border-gray-700 focus-within:border-blue-500 transition-colors">
              <div className="bg-gray-800 px-4 py-2 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="ml-4 text-gray-300 text-sm">
                    {selectedLanguage === 'javascript' ? 'script.js' : selectedLanguage === 'python' ? 'solution.py' : 'Solution.java'}
                  </span>
                </div>
              </div>
              <div className="relative">
                <textarea
                  value={code}
                  onChange={(e) => !showSolution && setCode(e.target.value)}
                  className={`w-full h-96 bg-gray-900 font-mono text-sm resize-none border-none outline-none pl-14 pr-4 py-4 ${
                    showSolution 
                      ? "text-gray-300 cursor-default" 
                      : "text-green-400 focus:text-white caret-white"
                  }`}
                  style={{ 
                    fontFamily: "'Fira Code', 'Monaco', 'Consolas', monospace",
                    lineHeight: "1.5",
                    tabSize: "2"
                  }}
                  spellCheck={false}
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  disabled={showSolution}
                  onKeyDown={(e) => {
                    if (showSolution) return;
                    
                    // Handle tab key for indentation
                    if (e.key === 'Tab') {
                      e.preventDefault();
                      const start = e.target.selectionStart;
                      const end = e.target.selectionEnd;
                      
                      const newValue = code.substring(0, start) + '    ' + code.substring(end);
                      setCode(newValue);
                      setTimeout(() => {
                        e.target.selectionStart = e.target.selectionEnd = start + 4;
                      }, 0);
                    }
                    
                    // Handle Enter key for auto-indentation
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      const start = e.target.selectionStart;
                      const lines = code.substring(0, start).split('\n');
                      const currentLine = lines[lines.length - 1];
                      const indent = currentLine.match(/^\s*/)[0];
                      const newValue = code.substring(0, start) + '\n' + indent + code.substring(start);
                      setCode(newValue);
                      setTimeout(() => {
                        e.target.selectionStart = e.target.selectionEnd = start + 1 + indent.length;
                      }, 0);
                    }
                  }}
                />
                
                {/* Line numbers */}
                <div className="absolute left-0 top-0 w-12 h-full bg-gray-800 border-r border-gray-700 pointer-events-none flex flex-col">
                  <div className="p-4 text-gray-500 text-xs font-mono leading-6 flex-1">
                    {code.split('\n').map((_, index) => (
                      <div key={index} className="text-right pr-2 h-6">
                        {index + 1}
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Syntax highlighting overlay (simple) */}
                {!showSolution && (
                  <div className="absolute top-4 right-4 text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded">
                    {selectedLanguage === 'javascript' ? 'JS' : selectedLanguage === 'python' ? 'PY' : 'JAVA'}
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            {!showSolution && (
              <div className="mt-4">
                {/* Run Code Button */}
                <div className="mb-4">
                  <button
                    onClick={handleRunCode}
                    disabled={isRunning}
                    className={`w-full flex items-center justify-center gap-2 py-3 px-4 ${
                      isRunning 
                        ? "bg-gray-500 cursor-not-allowed" 
                        : "bg-green-600 hover:bg-green-700"
                    } text-white rounded-lg font-medium transition-colors`}
                  >
                    {isRunning ? (
                      <>
                        <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
                        Running...
                      </>
                    ) : (
                      <>
                        <span>▶️</span>
                        Run Code
                      </>
                    )}
                  </button>
                </div>

                {/* Submit Button */}
                <div className="mb-4">
                  <button
                    className="w-full py-3 px-4 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                  >
                    <span>🚀</span>
                    Submit Solution
                  </button>
                </div>
                      {/* Additional Tools */}
            <div className="flex gap-2">
              <button
                onClick={() => {
                  setCode(currentProblem.starterCode[selectedLanguage] || "");
                  setOutput("");
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm flex items-center gap-2"
              >
                <span>🔄</span>
                Reset Code
              </button>
              <button
                onClick={() => {
                  // Basic code formatting
                  const formatted = code
                    .split('\n')
                    .map(line => line.trim())
                    .join('\n')
                    .replace(/\n\s*\n\s*\n/g, '\n\n');
                  setCode(formatted);
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm flex items-center gap-2"
              >
                <span>✨</span>
                Format
              </button>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(code);
                  alert("Code copied to clipboard!");
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm flex items-center gap-2"
              >
                <span>📋</span>
                Copy
              </button>
              <button
                onClick={() => {
                  const newCode = `// Quick test - uncomment and modify as needed
// console.log("Hello World!");
// console.log(2 + 3);
// console.log([1, 2, 3]);

${code}`;
                  setCode(newCode);
                }}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors text-sm flex items-center gap-2"
              >
                <span>🧪</span>
                Add Test
              </button>
            </div>
              </div>
            )}

            {/* Solution Actions */}
            {showSolution && (
              <div className="flex gap-3 mt-4">
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(code);
                    alert("Solution copied to clipboard!");
                  }}
                  className="flex-1 bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center justify-center gap-2"
                >
                  <span>📋</span>
                  Copy Solution
                </button>
                <Link
                  to={`/code-editor/${id}/solve`}
                  className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center"
                >
                  Try It Yourself
                </Link>
              </div>
            )}

            {/* Output Console */}
            {output && !showSolution && (
              <div className="mt-6">
                <h3 className="font-medium mb-2 flex items-center gap-2">
                  <span>📋</span>
                  Output:
                </h3>
                <pre className="bg-gray-800 text-green-400 p-4 rounded-md overflow-auto max-h-60 whitespace-pre-wrap">
                  {output}
                </pre>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default CodeEditor;
