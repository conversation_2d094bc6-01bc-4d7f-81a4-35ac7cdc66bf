import React, { useState } from "react";
import Chapter from "../Chapter"; // Import the existing Chapter component

const LiveClasses = () => {
  const [activeTab, setActiveTab] = useState("schedule");

  const tabs = [
    { id: "schedule", label: "Class Schedule", icon: "fas fa-calendar-alt" },
    { id: "chapters", label: "Chapter Materials", icon: "fas fa-book" },
    { id: "recordings", label: "Past Recordings", icon: "fas fa-play-circle" }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case "schedule":
        return <ClassSchedule />;
      case "chapters":
        return <Chapter />; // Reusing the existing Chapter component
      case "recordings":
        return <PastRecordings />;
      default:
        return <ClassSchedule />;
    }
  };

  // Animation styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .fade-in {
      animation: fadeIn 0.5s forwards;
    }
  `;

  return (
    <div className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md overflow-hidden text-white">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="p-6 bg-gradient-to-r from-[#1e293b]/20 to-[#0f172a]/20">
        <h2 className="text-3xl font-bold mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Python Live Classes</h2>
        <p className="text-blue-100/80">Interactive sessions with instructors and structured chapter-based learning</p>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-white/10 flex">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium transition-colors duration-200 relative ${
              activeTab === tab.id
                ? "text-blue-400 border-b-2 border-blue-400"
                : "text-white/70 hover:text-white"
            }`}
          >
            <i className={`${tab.icon} mr-2`}></i>
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Content */}
      <div className="p-6 fade-in">
        {renderContent()}
      </div>
    </div>
  );
};

// Class Schedule Component
const ClassSchedule = () => {
  const upcomingClasses = [
    {
      title: "Python Basics: Variables, Data Types & Operators",
      instructor: "Dr. Sarah Johnson",
      date: "July 10, 2025",
      time: "10:00 AM - 11:30 AM",
      level: "Beginner",
      registered: true
    },
    {
      title: "Control Flow: Conditionals and Loops",
      instructor: "Mark Williams",
      date: "July 12, 2025",
      time: "2:00 PM - 3:30 PM",
      level: "Beginner",
      registered: false
    },
    {
      title: "Functions and Modules",
      instructor: "Dr. Sarah Johnson",
      date: "July 15, 2025",
      time: "10:00 AM - 11:30 AM",
      level: "Beginner",
      registered: true
    },
    {
      title: "Data Structures: Lists and Dictionaries",
      instructor: "Mark Williams",
      date: "July 17, 2025",
      time: "2:00 PM - 3:30 PM",
      level: "Intermediate",
      registered: false
    },
    {
      title: "Object-Oriented Programming",
      instructor: "Dr. Sarah Johnson",
      date: "July 20, 2025",
      time: "10:00 AM - 11:30 AM",
      level: "Intermediate",
      registered: false
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-2xl font-bold text-white" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Upcoming Live Classes</h3>
        <button className="text-sm bg-[#303246]/60 backdrop-blur-lg text-blue-300 px-4 py-2 rounded-md border border-white/10 hover:bg-[#303246]/80 transition-colors flex items-center gap-2">
          <i className="fas fa-sync-alt"></i> 
          Sync with Calendar
        </button>
      </div>
      
      <div className="space-y-4">
        {upcomingClasses.map((classItem, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/10 hover:shadow-md transition-shadow">
            <div className="flex flex-wrap justify-between">
              <div className="w-full md:w-7/12 mb-2 md:mb-0">
                <h4 className="text-lg font-semibold text-white">{classItem.title}</h4>
                <p className="text-blue-100/80">Instructor: {classItem.instructor}</p>
              </div>
              <div className="w-full md:w-4/12 flex flex-col md:items-end">
                <div className="flex items-center text-blue-100/80">
                  <i className="far fa-calendar-alt mr-2"></i>
                  <span>{classItem.date}</span>
                </div>
                <div className="flex items-center text-blue-100/80">
                  <i className="far fa-clock mr-2"></i>
                  <span>{classItem.time}</span>
                </div>
              </div>
            </div>
            
            <div className="flex justify-between mt-4 items-center">
              <span className={`text-xs font-medium px-3 py-1 rounded-full ${
                classItem.level === "Beginner" 
                  ? "bg-green-500/20 text-green-400 border border-green-500/20" 
                  : classItem.level === "Intermediate"
                    ? "bg-yellow-500/20 text-yellow-400 border border-yellow-500/20"
                    : "bg-red-500/20 text-red-400 border border-red-500/20"
              }`}>
                {classItem.level}
              </span>
              
              <button className={`px-4 py-2 rounded-md text-sm font-medium ${
                classItem.registered 
                  ? "bg-[#303246]/40 text-blue-300 border border-white/10" 
                  : "bg-blue-500/30 text-white hover:bg-blue-500/40 border border-blue-500/20"
              }`}>
                {classItem.registered ? "Registered" : "Register Now"}
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-center mt-6">
        <button className="text-blue-300 flex items-center gap-2 hover:text-blue-400 transition-colors">
          View all upcoming classes
          <i className="fas fa-arrow-right"></i>
        </button>
      </div>
    </div>
  );
};

// Past Recordings Component
const PastRecordings = () => {
  const recordings = [
    {
      title: "Introduction to Python Programming",
      instructor: "Dr. Sarah Johnson",
      date: "June 28, 2025",
      duration: "1h 30m",
      thumbnail: "https://via.placeholder.com/640x360.png?text=Python+Intro+Course"
    },
    {
      title: "Variables and Data Types",
      instructor: "Mark Williams",
      date: "June 30, 2025",
      duration: "1h 15m",
      thumbnail: "https://via.placeholder.com/640x360.png?text=Python+Variables"
    },
    {
      title: "Control Flow and Conditionals",
      instructor: "Dr. Sarah Johnson",
      date: "July 2, 2025",
      duration: "1h 45m",
      thumbnail: "https://via.placeholder.com/640x360.png?text=Control+Flow"
    },
    {
      title: "Loops and Iterations",
      instructor: "Mark Williams",
      date: "July 5, 2025",
      duration: "1h 20m",
      thumbnail: "https://via.placeholder.com/640x360.png?text=Python+Loops"
    }
  ];

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-white mb-4" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Past Class Recordings</h3>
      <p className="text-blue-100/80">Watch recordings from previous sessions at your own pace.</p>
      
      <div className="grid md:grid-cols-2 gap-6 mt-6">
        {recordings.map((recording, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:border-white/20">
            <div className="relative">
              <img src={recording.thumbnail} alt={recording.title} className="w-full h-48 object-cover" />
              <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                <button className="bg-white/90 backdrop-blur-sm text-purple-600 rounded-full w-12 h-12 flex items-center justify-center border border-white/20 hover:bg-white transition-colors">
                  <i className="fas fa-play text-lg"></i>
                </button>
              </div>
              <div className="absolute bottom-2 right-2 bg-black/70 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-md border border-white/10">
                {recording.duration}
              </div>
            </div>
            <div className="p-4">
              <h4 className="text-lg font-semibold text-white mb-1" style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)' }}>{recording.title}</h4>
              <div className="flex justify-between text-sm text-blue-100/70">
                <span>{recording.instructor}</span>
                <span>{recording.date}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-center mt-6">
        <button className="text-purple-400 flex items-center gap-2 hover:text-purple-300 transition-colors font-medium">
          View all recordings
          <i className="fas fa-arrow-right"></i>
        </button>
      </div>
    </div>
  );
};

export default LiveClasses;
