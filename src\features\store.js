import { configureStore } from '@reduxjs/toolkit';
import authSlice from './authSlice';

const store = configureStore({
  reducer: {
    auth:authSlice
  },
//   middleware: (getDefaultMiddleware) =>
//     getDefaultMiddleware().concat(userSlice.middleware).concat(adminApi.middleware), 
});

// const initializeApp = async()=>{
//     await store.dispatch(userSlice.endpoints.loadUser.initiate({},{forceRefetch:true}));
// }

// initializeApp();

export default store;
