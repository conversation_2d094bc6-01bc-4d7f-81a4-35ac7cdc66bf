import React, { useState, useEffect } from 'react';
import { FaStar, FaComment, FaFilter, FaReply, FaTimes, FaCheck, FaTrash, FaEye } from 'react-icons/fa';
import reviewService from '../../../services/reviewService';

const FeedbackSection = () => {
  const [reviews, setReviews] = useState([]);
  const [filteredReviews, setFilteredReviews] = useState([]);
  const [overallStats, setOverallStats] = useState({});
  const [selectedCourse, setSelectedCourse] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [replyingTo, setReplyingTo] = useState(null);
  const [replyText, setReplyText] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadReviews();
  }, []);

  useEffect(() => {
    filterReviews();
  }, [reviews, selectedCourse, statusFilter]);

  const loadReviews = () => {
    setLoading(true);
    try {
      const allReviews = reviewService.getAllReviews();
      const stats = reviewService.getOverallStats();

      setReviews(allReviews);
      setOverallStats(stats);
    } catch (error) {
      console.error('Error loading reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterReviews = () => {
    let filtered = [...reviews];

    if (selectedCourse !== 'all') {
      filtered = filtered.filter(review => review.courseId === selectedCourse);
    }

    if (statusFilter !== 'all') {
      if (statusFilter === 'approved') {
        filtered = filtered.filter(review => review.isApproved);
      } else if (statusFilter === 'pending') {
        filtered = filtered.filter(review => !review.isApproved);
      }
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => new Date(b.date) - new Date(a.date));

    setFilteredReviews(filtered);
  };

  const handleApproveReview = (reviewId, isApproved) => {
    reviewService.approveReview(reviewId, isApproved);
    loadReviews();
  };

  const handleDeleteReview = (reviewId) => {
    if (window.confirm('Are you sure you want to delete this review?')) {
      reviewService.deleteReview(reviewId);
      loadReviews();
    }
  };

  const handleReplySubmit = (reviewId) => {
    if (replyText.trim()) {
      reviewService.addAdminReply(reviewId, replyText.trim());
      setReplyingTo(null);
      setReplyText('');
      loadReviews();
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Render stars based on rating
  const renderStars = (rating) => {
    return Array(5).fill(0).map((_, i) => (
      <FaStar
        key={i}
        className={i < rating ? "text-yellow-500" : "text-gray-300"}
      />
    ));
  };

  const getUniqueCoursesFromReviews = () => {
    const courses = [...new Set(reviews.map(review => review.courseId))];
    return courses.map(courseId => {
      const review = reviews.find(r => r.courseId === courseId);
      return {
        id: courseId,
        name: review?.courseName || courseId
      };
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading reviews...</span>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Course Reviews Management</h2>
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <FaFilter className="mr-2 text-gray-500" />
            <select
              value={selectedCourse}
              onChange={(e) => setSelectedCourse(e.target.value)}
              className="border rounded-md px-3 py-1 text-sm"
            >
              <option value="all">All Courses</option>
              {getUniqueCoursesFromReviews().map(course => (
                <option key={course.id} value={course.id}>
                  {course.name}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="border rounded-md px-3 py-1 text-sm"
            >
              <option value="all">All Status</option>
              <option value="approved">Approved</option>
              <option value="pending">Pending Approval</option>
            </select>
          </div>
        </div>
      </div>

      {/* Feedback Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Overall Rating</h3>
            <div className="flex items-center justify-center mb-2">
              <div className="flex mr-2">
                {renderStars(Math.floor(overallStats.averageRating || 0))}
              </div>
            </div>
            <span className="text-3xl font-bold text-gray-800">
              {overallStats.averageRating || 0}
            </span>
            <span className="text-sm text-gray-500 ml-2">out of 5</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Total Reviews</h3>
            <span className="text-3xl font-bold text-blue-600">
              {overallStats.totalReviews || 0}
            </span>
            <p className="text-sm text-gray-500 mt-1">All courses</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Published Reviews</h3>
            <span className="text-3xl font-bold text-green-600">
              {reviews.filter(r => r.isApproved).length}
            </span>
            <p className="text-sm text-gray-500 mt-1">Live on site</p>
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <h3 className="font-semibold text-gray-700">
              Reviews ({filteredReviews.length})
            </h3>
            <div className="text-sm text-gray-500">
              {selectedCourse !== 'all' && `Filtered by course`}
              {statusFilter !== 'all' && ` • ${statusFilter} only`}
            </div>
          </div>
        </div>

        {filteredReviews.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <FaComment className="text-4xl mx-auto mb-4 text-gray-300" />
            <p>No reviews found matching your filters.</p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {filteredReviews.map((review) => (
              <li key={review.id} className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <p className="font-medium text-gray-800">{review.userName}</p>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        review.isApproved
                          ? 'bg-green-100 text-green-800'
                          : 'bg-orange-100 text-orange-800'
                      }`}>
                        {review.isApproved ? 'Approved' : 'Pending'}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500 mb-2">
                      {review.courseName} • {formatDate(review.date)}
                    </p>
                    <div className="flex items-center mb-3">
                      <div className="flex mr-2">
                        {renderStars(review.rating)}
                      </div>
                      <span className="text-sm font-medium text-gray-700">
                        {review.rating}/5
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {!review.isApproved && (
                      <button
                        onClick={() => handleApproveReview(review.id, true)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                        title="Approve Review"
                      >
                        <FaCheck />
                      </button>
                    )}
                    {review.isApproved && (
                      <button
                        onClick={() => handleApproveReview(review.id, false)}
                        className="p-2 text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
                        title="Unapprove Review"
                      >
                        <FaEye />
                      </button>
                    )}
                    <button
                      onClick={() => setReplyingTo(replyingTo === review.id ? null : review.id)}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="Reply to Review"
                    >
                      <FaReply />
                    </button>
                    <button
                      onClick={() => handleDeleteReview(review.id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Delete Review"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </div>

                <div className="mb-4">
                  <h4 className="font-semibold text-gray-800 mb-2">{review.title}</h4>
                  <p className="text-gray-600">{review.comment}</p>
                </div>

                {review.adminReply && (
                  <div className="bg-blue-50 p-4 rounded-lg mb-4">
                    <div className="flex items-center mb-2">
                      <FaReply className="text-blue-600 mr-2" />
                      <span className="font-semibold text-blue-800 text-sm">Admin Response</span>
                    </div>
                    <p className="text-blue-700 text-sm">{review.adminReply}</p>
                  </div>
                )}

                {replyingTo === review.id && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <textarea
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      placeholder="Write your response to this review..."
                      className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={3}
                    />
                    <div className="flex justify-end space-x-2 mt-3">
                      <button
                        onClick={() => {
                          setReplyingTo(null);
                          setReplyText('');
                        }}
                        className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={() => handleReplySubmit(review.id)}
                        disabled={!replyText.trim()}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        Send Reply
                      </button>
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default FeedbackSection;