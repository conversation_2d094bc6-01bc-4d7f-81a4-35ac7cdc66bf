const computerVisionCaseStudies = [
  {
    title: "Image Classification",
    objective: "Learn basic image classification",
    scenario: "Build an image classifier using CNN",
    keyConcepts: ["Convolutional Neural Networks", "Feature Extraction", "Image Preprocessing", "Transfer Learning"],
    solution: `# Image Classification Example
import torch
import torchvision.models as models
import torchvision.transforms as transforms

# Load pre-trained model
model = models.resnet18(pretrained=True)
model.eval()

# Define image transforms
transform = transforms.Compose([
    transforms.Resize(256),
    transforms.CenterCrop(224),
    transforms.ToTensor(),
    transforms.Normalize(
        mean=[0.485, 0.456, 0.406],
        std=[0.229, 0.224, 0.225]
    )
])

# Example usage
def predict_image(image_path):
    img = Image.open(image_path)
    img_t = transform(img)
    batch_t = torch.unsqueeze(img_t, 0)
    out = model(batch_t)
    return out
`
  },
  {
    title: "Object Detection",
    objective: "Implement object detection",
    scenario: "Detect and locate objects in images",
    keyConcepts: ["Bounding Boxes", "Anchor Boxes", "Non-max Suppression", "YOLO"],
    solution: `# Object Detection Example
import torch
from torchvision.models.detection import fasterrcnn_resnet50_fpn

# Load model
model = fasterrcnn_resnet50_fpn(pretrained=True)
model.eval()

def detect_objects(image):
    # Transform image
    transform = transforms.ToTensor()
    img_tensor = transform(image)
    
    # Get predictions
    with torch.no_grad():
        prediction = model([img_tensor])
    
    return prediction[0]
`
  }
];

export default computerVisionCaseStudies;
