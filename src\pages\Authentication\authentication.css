/* Signup.css */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(to right, #c2cdd4, #91caf0);
  animation: fadeIn 0.5s ease-in-out;
}

.signup-box {
  background: white;
  padding: 10px 20px;
  border-radius: 12px;
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.15);
  text-align: center;
  width: 560px;
  transition: transform 0.3s ease-in-out;
}

.signup-box:hover {
  transform: translateY(-3px);
}

.avatar-label {
  display: block;
  position: relative;
  margin: 0 auto 15px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #009cff;
  cursor: pointer;
  transition: 0.3s ease-in-out;
}

.avatar-label:hover {
  border-color: #007acc;
  animation: pulse 1s infinite;
}

.avatar {
  width: 100%;
  height: 100%;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  transition: background 0.3s;
}

.avatar:hover {
  background: #f3f3f3;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-input {
  display: none;
}

.input-field {
  width: 100%;
  padding: 12px;
  margin-bottom: 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 16px;
  outline: none;
  transition: all 0.3s ease-in-out;
}

.input-field:focus {
  border-color: #009cff;
  box-shadow: 0 0 8px rgba(0, 156, 255, 0.3);
}

.error-message{
  color: red;
  display: flex;
  margin: 0;
}

.signup-button {
  width: 100%;
  padding: 12px;
  background: #009cff;
  color: white;
  font-size: 16px;
  border: none;
  outline: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  font-weight: bold;
}

.signup-button:hover {
  background: #007acc;
  box-shadow: 0px 4px 10px rgba(0, 124, 255, 0.3);
}

.signin-text {
  margin-top: 12px;
  color: #555;
  font-size: 14px;
}

.signin-link {
  color: #009cff;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s;
}

.signin-link:hover {
  text-decoration: underline;
  color: #007acc;
}

/* Google SignIn Button */
.google-signin-button {
  width: 100%;
  padding: 12px;
  background: #db4437;
  color: white;
  font-size: 16px;
  border: none;
  border-radius: 30px;
  outline: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.google-signin-button:hover {
  background: #c1351d;
  box-shadow: 0px 4px 10px rgba(219, 68, 55, 0.3);
}

.google-logo {
  width: 30px;
  height: 30px;
  margin-right: 10px;
  border-radius: 30px;
}

.forgot-password {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.forgot-checks{
  width: 100%;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: space-between;
}

.forgot-password-link {
  font-size: 18px;
  color: #007bff;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.forgot-password-link:hover {
  color: #0056b3;
}

@media screen and (max-width: 500px) {
  .signup-box {
    width: 90%;
  }
}

/* forgot password design */

.forgot-password-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(to right, #c2cdd4, #91caf0);
}

.form-container-forgot {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.form-container-forgot h2 {
  margin-bottom: 20px;
  color: #e40808;
  font-size: 34px;
  font-weight: 700;
}

.form-container-forgot p {
  margin-bottom: 25px;
  color: #555;
  font-size: 14px;
  font-weight: 500;
}

.email-input-forgot {
  width: 100%;
  padding: 12px;
  margin: 15px 0;
  font-size: 16px;
  border: 2px solid #ccc;
  border-radius: 8px;
  outline: none;
  transition: all 0.3s ease-in-out;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: #effbff;
}

.email-input-forgot:focus{
  border-color: #009cff;
  box-shadow: 0px 0px 8px rgba(0, 156, 255, 0.6);
  background: #fff;
}

.submit-btn-forgot {
  width: 100%;
  padding: 12px;
  background-color: #009cff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 10px;
  transition: background-color 0.3s ease;
  font-weight: 700;
  font-size: 18px;
}

.submit-btn-forgot:hover {
  background-color: #0056b3;
}

.back-link-forgot {
  display: block;
  margin-top: 15px;
  color: #007bff;
  text-decoration: none;
}

.back-link:hover {
  text-decoration: underline;
}

.parent-otp {
  background: #91caf0;
  backdrop-filter: blur(30px);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  height: 100vh;
  margin: 0;
  font-family: Arial, sans-serif;
}

.otp-container {
  display: flex;
  justify-content: center;
  gap: 20px;
  align-items: center;
  height: 100vh;
}

.otp-card {
  background: white;
  padding-block: 5rem;
  padding-inline: 2rem;
  border-radius: 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
  text-align: center;
}

.otp-card button{
  width: 100%;
  height: 50px;
  margin-top: 2rem;
  background-color: #009cff;
  color: white;
  font-weight: 700;
  border: none;
  outline: none;
}

.otp-card button:hover{
  background-color: #6cb1db;
}

.otp-card h2 {
  margin-bottom: 20px;
  color: #009cff;
  font-weight: 700;
}

.otp-inputs {
  display: flex;
  justify-content: center;
  gap: 30px;
}

.otp-inputs input {
  width: 60px;
  height: 50px;
  text-align: center;
  font-size: 20px;
  border: 2px solid #ccc;
  border-radius: 5px;
  outline: none;
  transition: border-color 0.3s;
}

.otp-inputs input:focus {
  border-color: #007bff;
}
