import React, { useState } from "react";

const FullstackLiveClasses = () => {
  const [activeTab, setActiveTab] = useState("schedule");

  const tabs = [
    { id: "schedule", label: "Class Schedule", icon: "fas fa-calendar-alt" },
    { id: "modules", label: "Module Materials", icon: "fas fa-book" },
    { id: "recordings", label: "Past Recordings", icon: "fas fa-play-circle" }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case "schedule":
        return <ClassSchedule />;
      case "modules":
        return <ModuleMaterials />;
      case "recordings":
        return <PastRecordings />;
      default:
        return <ClassSchedule />;
    }
  };

  // Animation styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .fade-in {
      animation: fadeIn 0.5s forwards;
    }
  `;

  return (
    <div className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md overflow-hidden text-white">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="p-6 bg-gradient-to-r from-[#1e293b]/20 to-[#0f172a]/20">
        <h2 className="text-3xl font-bold mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Full Stack Live Classes</h2>
        <p className="text-blue-100/80">Interactive sessions with instructors and structured module-based learning</p>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-white/10 flex">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium transition-colors duration-200 relative ${
              activeTab === tab.id
                ? "text-purple-400 border-b-2 border-purple-400"
                : "text-white/70 hover:text-white"
            }`}
          >
            <i className={`${tab.icon} mr-2`}></i>
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Content */}
      <div className="p-6 fade-in">
        {renderContent()}
      </div>
    </div>
  );
};

// Class Schedule Component
const ClassSchedule = () => {
  const upcomingClasses = [
    {
      title: "React Fundamentals: Components & JSX",
      instructor: "Alex Rodriguez",
      date: "July 10, 2025",
      time: "10:00 AM - 11:30 AM",
      level: "Beginner",
      registered: true,
      technology: "React"
    },
    {
      title: "Node.js & Express: Building APIs",
      instructor: "Sarah Chen",
      date: "July 12, 2025",
      time: "2:00 PM - 3:30 PM",
      level: "Intermediate",
      registered: false,
      technology: "Node.js"
    },
    {
      title: "MongoDB Database Design",
      instructor: "Michael Johnson",
      date: "July 15, 2025",
      time: "10:00 AM - 11:30 AM",
      level: "Beginner",
      registered: true,
      technology: "MongoDB"
    },
    {
      title: "State Management with Redux",
      instructor: "Emily Zhang",
      date: "July 17, 2025",
      time: "3:00 PM - 4:30 PM",
      level: "Advanced",
      registered: false,
      technology: "Redux"
    },
    {
      title: "Authentication & Security",
      instructor: "David Kumar",
      date: "July 20, 2025",
      time: "11:00 AM - 12:30 PM",
      level: "Intermediate",
      registered: true,
      technology: "Security"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-2xl font-bold" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Upcoming Classes</h3>
        <div className="text-sm text-blue-200/80">
          <i className="fas fa-clock mr-1"></i>
          All times are in EST
        </div>
      </div>
      
      <div className="grid gap-4">
        {upcomingClasses.map((class_, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm p-6 rounded-xl border border-white/10 transition-all duration-300 hover:border-white/20">
            <div className="flex flex-col md:flex-row md:items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center mb-2">
                  <h4 className="text-lg font-semibold text-white mr-3">{class_.title}</h4>
                  <span className={`text-xs px-2 py-1 rounded ${
                    class_.technology === "React" ? "bg-blue-500/20 text-blue-300" :
                    class_.technology === "Node.js" ? "bg-green-500/20 text-green-300" :
                    class_.technology === "MongoDB" ? "bg-yellow-500/20 text-yellow-300" :
                    class_.technology === "Redux" ? "bg-purple-500/20 text-purple-300" :
                    "bg-red-500/20 text-red-300"
                  }`}>
                    {class_.technology}
                  </span>
                </div>
                
                <p className="text-blue-100/80 text-sm mb-2">
                  <i className="fas fa-user mr-1"></i> {class_.instructor}
                </p>
                
                <div className="flex flex-wrap gap-4 text-sm text-white/70">
                  <span><i className="fas fa-calendar mr-1"></i> {class_.date}</span>
                  <span><i className="fas fa-clock mr-1"></i> {class_.time}</span>
                  <span className={`${
                    class_.level === "Beginner" ? "text-green-400" :
                    class_.level === "Intermediate" ? "text-yellow-400" :
                    "text-red-400"
                  }`}>
                    <i className="fas fa-signal mr-1"></i> {class_.level}
                  </span>
                </div>
              </div>
              
              <div className="mt-4 md:mt-0 md:ml-6">
                {class_.registered ? (
                  <div className="flex items-center">
                    <span className="bg-green-500/20 text-green-300 px-3 py-2 rounded text-sm mr-3">
                      <i className="fas fa-check mr-1"></i> Registered
                    </span>
                    <button className="bg-blue-600/50 hover:bg-blue-600/70 text-white px-4 py-2 rounded text-sm transition-colors">
                      Join Class
                    </button>
                  </div>
                ) : (
                  <button className="bg-gradient-to-r from-purple-600/50 to-pink-600/50 hover:from-purple-600/70 hover:to-pink-600/70 text-white px-6 py-2 rounded text-sm transition-all duration-200">
                    Register Now
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="bg-gradient-to-r from-[#303246]/30 to-[#1e293b]/30 backdrop-blur-sm p-6 rounded-xl border border-white/10">
        <h4 className="text-lg font-semibold mb-3 text-blue-300">Class Guidelines</h4>
        <ul className="space-y-2 text-white/80 text-sm">
          <li className="flex items-start">
            <span className="text-green-400 mr-2 mt-1">•</span>
            Join classes 5 minutes early to test your audio and video
          </li>
          <li className="flex items-start">
            <span className="text-green-400 mr-2 mt-1">•</span>
            Have your development environment ready before the session
          </li>
          <li className="flex items-start">
            <span className="text-green-400 mr-2 mt-1">•</span>
            Ask questions in the chat or unmute during Q&A segments
          </li>
          <li className="flex items-start">
            <span className="text-green-400 mr-2 mt-1">•</span>
            Recordings will be available within 24 hours for registered students
          </li>
        </ul>
      </div>
    </div>
  );
};

// Module Materials Component
const ModuleMaterials = () => {
  const modules = [
    {
      title: "Frontend Development with React",
      description: "Master React fundamentals including components, hooks, and state management",
      topics: ["JSX & Components", "Props & State", "React Hooks", "Event Handling", "Routing"],
      progress: 75,
      status: "In Progress"
    },
    {
      title: "Backend Development with Node.js",
      description: "Build robust server-side applications using Node.js and Express",
      topics: ["Express Setup", "Middleware", "REST APIs", "Database Integration", "Error Handling"],
      progress: 40,
      status: "In Progress"
    },
    {
      title: "Database Management with MongoDB",
      description: "Learn NoSQL database design and operations with MongoDB",
      topics: ["MongoDB Basics", "Schema Design", "CRUD Operations", "Aggregation", "Indexing"],
      progress: 100,
      status: "Completed"
    },
    {
      title: "Full Stack Integration",
      description: "Connect frontend and backend for complete application development",
      topics: ["API Integration", "Authentication", "Deployment", "Testing", "Performance"],
      progress: 0,
      status: "Upcoming"
    }
  ];

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Module Materials</h3>
      
      <div className="grid gap-6">
        {modules.map((module, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-xl p-6 border border-white/10">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h4 className="text-xl font-semibold mb-2 text-white">{module.title}</h4>
                <p className="text-white/70 text-sm">{module.description}</p>
              </div>
              <span className={`px-3 py-1 rounded text-xs font-medium ${
                module.status === "Completed" ? "bg-green-500/20 text-green-300" :
                module.status === "In Progress" ? "bg-yellow-500/20 text-yellow-300" :
                "bg-gray-500/20 text-gray-300"
              }`}>
                {module.status}
              </span>
            </div>
            
            <div className="mb-4">
              <div className="flex justify-between text-sm text-white/70 mb-2">
                <span>Progress</span>
                <span>{module.progress}%</span>
              </div>
              <div className="w-full bg-gray-700/50 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${module.progress}%` }}
                ></div>
              </div>
            </div>
            
            <div className="mb-4">
              <h5 className="text-sm text-blue-300 mb-2">Topics Covered:</h5>
              <div className="flex flex-wrap gap-2">
                {module.topics.map((topic, i) => (
                  <span key={i} className="bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded">
                    {topic}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="flex gap-3">
              <button className="bg-blue-600/50 hover:bg-blue-600/70 text-white px-4 py-2 rounded text-sm transition-colors">
                View Materials
              </button>
              <button className="border border-white/30 hover:border-white/50 text-white px-4 py-2 rounded text-sm transition-colors">
                Download Resources
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Past Recordings Component
const PastRecordings = () => {
  const recordings = [
    {
      title: "Introduction to Full Stack Development",
      instructor: "Alex Rodriguez",
      date: "July 5, 2025",
      duration: "1h 25m",
      views: 156,
      rating: 4.8
    },
    {
      title: "Setting up Development Environment",
      instructor: "Sarah Chen",
      date: "July 3, 2025",
      duration: "1h 15m",
      views: 203,
      rating: 4.9
    },
    {
      title: "Git & Version Control Basics",
      instructor: "Michael Johnson",
      date: "July 1, 2025",
      duration: "1h 35m",
      views: 178,
      rating: 4.7
    }
  ];

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Past Recordings</h3>
      
      <div className="grid gap-4">
        {recordings.map((recording, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm p-6 rounded-xl border border-white/10 transition-all duration-300 hover:border-white/20">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-lg font-semibold mb-2 text-white">{recording.title}</h4>
                <div className="flex flex-wrap gap-4 text-sm text-white/70 mb-3">
                  <span><i className="fas fa-user mr-1"></i> {recording.instructor}</span>
                  <span><i className="fas fa-calendar mr-1"></i> {recording.date}</span>
                  <span><i className="fas fa-clock mr-1"></i> {recording.duration}</span>
                  <span><i className="fas fa-eye mr-1"></i> {recording.views} views</span>
                </div>
                <div className="flex items-center">
                  <div className="flex text-yellow-400 mr-2">
                    {[...Array(5)].map((_, i) => (
                      <i key={i} className={`fas fa-star ${i < Math.floor(recording.rating) ? '' : 'opacity-30'}`}></i>
                    ))}
                  </div>
                  <span className="text-white/70 text-sm">{recording.rating}</span>
                </div>
              </div>
              
              <div className="ml-6">
                <button className="bg-gradient-to-r from-purple-600/50 to-pink-600/50 hover:from-purple-600/70 hover:to-pink-600/70 text-white px-6 py-2 rounded text-sm transition-all duration-200 flex items-center">
                  <i className="fas fa-play mr-2"></i>
                  Watch
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="text-center py-8">
        <button className="border border-white/30 hover:border-white/50 text-white px-6 py-3 rounded text-sm transition-colors">
          Load More Recordings
        </button>
      </div>
    </div>
  );
};

export default FullstackLiveClasses;
